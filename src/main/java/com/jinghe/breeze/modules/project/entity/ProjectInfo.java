package com.jinghe.breeze.modules.project.entity;

import java.io.Serializable;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * @Description: project_info
 * @Author: jeecg-boot
 * @Date:   2024-04-17
 * @Version: V1.0
 */
@Data
@TableName("project_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="project_info对象", description="project_info")
public class ProjectInfo implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private java.lang.String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private java.util.Date updateTime;
	/**deleteFlag*/
	@Excel(name = "delFlag", width = 15)
    @ApiModelProperty(value = "delFlag")
    private java.lang.Integer delFlag;
	/**组织机构编码*/
    @ApiModelProperty(value = "组织机构编码")
    private java.lang.String sysOrgCode;
	/**项目编码*/
	@Excel(name = "项目编码", width = 15)
    @ApiModelProperty(value = "项目编码")
    private java.lang.String code;
	/**项目名称*/
	@Excel(name = "项目名称", width = 15)
    @ApiModelProperty(value = "项目名称")
    private java.lang.String name;
	/**项目施工范围*/
	@Excel(name = "项目施工范围", width = 15, dicCode = "project_scope")
	@Dict(dicCode = "project_scope")
    @ApiModelProperty(value = "项目施工范围")
    private java.lang.String proScope;
	/**业主单位 */
	@Excel(name = "业主单位 ", width = 15)
    @ApiModelProperty(value = "业主单位 ")
    private java.lang.String ownerUnits;
	/**设计单位 */
	@Excel(name = "设计单位 ", width = 15)
    @ApiModelProperty(value = "设计单位 ")
    private java.lang.String designUnits;
	/**监理单位 */
	@Excel(name = "监理单位 ", width = 15)
    @ApiModelProperty(value = "监理单位 ")
    private java.lang.String supervisorUnits;
	/**总承包单位 */
	@Excel(name = "总承包单位 ", width = 15)
    @ApiModelProperty(value = "总承包单位 ")
    private java.lang.String contractUnits;
	/**计划开工时间*/
	@Excel(name = "计划开工时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "计划开工时间")
    private java.util.Date startDate;
	/**实际开工时间*/
	@Excel(name = "实际开工时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "实际开工时间")
    @TableField(updateStrategy = FieldStrategy.IGNORED, insertStrategy = FieldStrategy.IGNORED) //忽略空值检查
    private java.util.Date actualStartDate;
	/**计划竣工时间*/
	@Excel(name = "计划竣工时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "计划竣工时间")
    @NotNull(message = "计划竣工时间不能为空!")
    private java.util.Date endDate;
	/**实际竣工时间*/
	@Excel(name = "实际竣工时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "实际竣工时间")
    @TableField(updateStrategy = FieldStrategy.IGNORED, insertStrategy = FieldStrategy.IGNORED) //忽略空值检查
    private java.util.Date actualEndDate;
	/**项目地址*/
	@Excel(name = "项目地址", width = 15)
    @ApiModelProperty(value = "项目地址")
    private java.lang.String projectAddress;
	/**项目坐标信息*/
	@Excel(name = "项目坐标信息", width = 15)
    @ApiModelProperty(value = "项目坐标信息")
    private java.lang.String point;
	/**风机数量*/
	@Excel(name = "风机数量", width = 15)
    @ApiModelProperty(value = "风机数量")
    private java.lang.Integer totalFun;
	/**装机容量*/
	@Excel(name = "装机容量", width = 15)
    @ApiModelProperty(value = "装机容量")
    private BigDecimal capacity;
	/**ais定位区域*/
	@Excel(name = "ais定位区域", width = 15)
    @ApiModelProperty(value = "ais定位区域")
    private java.lang.String shipApiRegion;
    /**风场范围*/
    @Excel(name = "风场范围", width = 15)
    @ApiModelProperty(value = "风场范围")
    private java.lang.String siteRange;
	/**简介*/
	@Excel(name = "简介", width = 15)
    @ApiModelProperty(value = "简介")
    private java.lang.String remark;
	/**图片*/
	@Excel(name = "图片", width = 15)
    @ApiModelProperty(value = "图片")
    private java.lang.String images;
	/**项目宣传片*/
	@Excel(name = "项目宣传片", width = 15)
    @ApiModelProperty(value = "项目宣传片")
    private java.lang.String video;
    /**工艺视频*/
    @Excel(name = "工艺视频", width = 15)
    @ApiModelProperty(value = "工艺视频")
    private java.lang.String processVideo;
    /**工艺视频简介*/
    @Excel(name = "工艺视频简介", width = 15)
    @ApiModelProperty(value = "工艺视频简介")
    private java.lang.String processVideoDescription;
    /**宣传视频简介*/
    @Excel(name = "宣传视频简介", width = 15)
    @ApiModelProperty(value = "宣传视频简介")
    private java.lang.String videoDescription;
}
