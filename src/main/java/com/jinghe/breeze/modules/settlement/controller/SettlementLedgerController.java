package com.jinghe.breeze.modules.settlement.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinghe.breeze.modules.settlement.entity.ContractInfo;
import com.jinghe.breeze.modules.settlement.entity.ContractInfoDetail;
import com.jinghe.breeze.modules.settlement.entity.Settlement;
import com.jinghe.breeze.modules.settlement.service.IContractInfoDetailService;
import com.jinghe.breeze.modules.settlement.service.IContractInfoService;
import com.jinghe.breeze.modules.settlement.service.ISettlementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

@Api(tags = "settlementLedger")
@RestController
@RequestMapping("/settlement/settlementLedger")
@Slf4j
public class SettlementLedgerController {

    @Autowired
    private ISettlementService settlementService;
    @Autowired
    private IContractInfoDetailService contractInfoDetailService;
    @Autowired
    private IContractInfoService contractInfoService;

    @AutoLog(value = "settlementLedger查询合同下级")
    @ApiOperation(value = "settlementLedger查询合同下级", notes = "settlementLedger查询合同下级")
    @GetMapping(value = "/childList")
    public Result<?> queryContract(@RequestParam(name = "pid", required = true) String id,
                                   @RequestParam(name = "code", required = false) String code) {
        List<ContractInfoDetail> contractInfoDetails = settlementService.getChild(code, id);
        IPage<ContractInfoDetail> pageList = new Page<>(1, contractInfoDetails.size(), contractInfoDetails.size());
        pageList.setRecords(contractInfoDetails);
        return Result.OK(pageList);
    }


    @ApiModelProperty(value = "contract_info-获取已登记过的合同列表", notes = "contract_info-获取已登记过的合同列表")
    @GetMapping(value = "/rootList")
    public Result<?> getSettledContractList(@RequestParam(name = "code", required = false) String code,
                                            @RequestParam(name = "contractId", required = false) String contractId) {
        List<ContractInfo> contractInfoList = contractInfoService.getSettledContractList(code, contractId);
        Page<ContractInfo> page = new Page<ContractInfo>(1, contractInfoList.size());
        page.setTotal(contractInfoList.size());
        page.setRecords(contractInfoList);
        return Result.OK(page);
    }

    @ApiModelProperty(value = "getChildListBatch", notes = "getChildListBatch")
    @GetMapping(value = "/getChildListBatch")
    public Result<?> getChildListBatch(@RequestParam("parentIds") String parentIds,
                                       @RequestParam("code") String code) {
        List<String> ids = Arrays.asList(parentIds.split(","));
        List<ContractInfoDetail> contractInfoDetails = new ArrayList<>();
        ids.stream().forEach(id -> {
            contractInfoDetails.addAll(settlementService.getChild(code, id));
            contractInfoDetailService.setSettledMoney(contractInfoDetails);
        });
        IPage<ContractInfoDetail> page = new Page<>(1, contractInfoDetails.size(), contractInfoDetails.size());
        page.setRecords(contractInfoDetails);
        return Result.OK(page);
    }

    @ApiModelProperty(value = "getSettledContractList", notes = "getSettledContractList")
    @GetMapping(value = "/getSettledContractList")
    public Result<?> getSettledContractList() {
        List<String> contractIdList = settlementService.list(new LambdaQueryWrapper<Settlement>()
                        .groupBy(Settlement::getContractId)
                        .select(Settlement::getContractId)).stream()
                .map(Settlement::getContractId).collect(Collectors.toList());

        List<ContractInfo> contractInfoList = contractInfoService.list(new LambdaQueryWrapper<ContractInfo>()
                .in(ContractInfo::getId, contractIdList));
        contractInfoList.forEach(contractInfo -> {
            contractInfoService.setSettledMoney(contractInfo);
        });
        return Result.OK(contractInfoList);
    }
}
