package com.jinghe.breeze.modules.ship.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jinghe.breeze.modules.ship.entity.ShipInfo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: ship_info
 * @Author: jeecg-boot
 * @Date:   2024-04-19
 * @Version: V1.0
 */
public interface IShipInfoService extends IService<ShipInfo> {

    void saveInfo(ShipInfo shipInfo);

    void delete(String id);

    IPage<ShipInfo> getList(ShipInfo shipInfo, Integer pageNo, Integer pageSize);
}
