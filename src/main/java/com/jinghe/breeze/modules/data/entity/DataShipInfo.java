package com.jinghe.breeze.modules.data.entity;

import java.io.Serializable;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 船舶信息
 * @Author: jeecg-boot
 * @Date:   2024-04-17
 * @Version: V1.0
 */
@Data
@TableName("data_ship_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="data_ship_info对象", description="船舶信息")
public class DataShipInfo implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**船舶名称*/
	@Excel(name = "船舶名称", width = 15)
    @ApiModelProperty(value = "船舶名称")
    private java.lang.String shipName;
	/**MMSI*/
	@Excel(name = "MMSI", width = 15)
    @ApiModelProperty(value = "MMSI")
    private java.lang.String mmsi;
	/**船东*/
	@Excel(name = "船东", width = 15)
    @ApiModelProperty(value = "船东")
    private java.lang.String shipOwner;
	/**船东联系人	*/
	@Excel(name = "船东联系人	", width = 15)
    @ApiModelProperty(value = "船东联系人	")
    private java.lang.String shipLink;
	/**联系人电话	*/
	@Excel(name = "联系人电话	", width = 11)
    @ApiModelProperty(value = "联系人电话	")
    private java.lang.String linkPhone;
	/**目前状态	*/
	@Excel(name = "目前状态", width = 15, dicCode = "ship_states")
	@Dict(dicCode = "ship_states")
    @ApiModelProperty(value = "目前状态")
    private java.lang.String shipState;
	/**长(m)*/
	@Excel(name = "长(m)", width = 15)
    @ApiModelProperty(value = "长(m)")
    private BigDecimal shipLong;
	/**宽(m)	*/
	@Excel(name = "宽(m)", width = 15)
    @ApiModelProperty(value = "宽(m)	")
    private BigDecimal shipWide;
	/**型深(m)	*/
	@Excel(name = "型深(m)", width = 15)
    @ApiModelProperty(value = "型深(m)	")
    private BigDecimal typeDepth;
	/**船籍港*/
	@Excel(name = "船籍港", width = 15)
    @ApiModelProperty(value = "船籍港")
    private java.lang.String homePort;
	/**图片*/
	@Excel(name = "图片", width = 15)
    @ApiModelProperty(value = "图片")
    private java.lang.String picture;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
	/**是否无动力船*/
	@Excel(name = "是否无动力船", width = 15, dicCode = "unpowered_type")
	@Dict(dicCode = "unpowered_type")
    @ApiModelProperty(value = "是否无动力船")
    private java.lang.String unpoweredShip;
	/**抗浪涌情况(m)*/
	@Excel(name = "抗浪涌情况(m)", width = 15)
    @ApiModelProperty(value = "抗浪涌情况(m)")
    private java.lang.String resistSurgeSituation;
	/**抗风等级	*/
	@Excel(name = "抗风等级	", width = 15)
    @ApiModelProperty(value = "抗风等级	")
    private BigDecimal resistWindLevel;
	/**定位方式	*/
	@Excel(name = "定位方式	", width = 15)
    @ApiModelProperty(value = "定位方式	")
    private java.lang.String positionMethod;
	/**适用海域	*/
	@Excel(name = "适用海域	", width = 15)
    @ApiModelProperty(value = "适用海域	")
    private java.lang.String applicableSeaArea;
	/**支腿长度(m)	*/
	@Excel(name = "支腿长度(m)	", width = 15)
    @ApiModelProperty(value = "支腿长度(m)	")
    private BigDecimal legLength;
	/**主吊起重能力	*/
	@Excel(name = "主吊起重能力	", width = 15)
    @ApiModelProperty(value = "主吊起重能力	")
    private BigDecimal mainLiftingCapacity;
	/**主吊起吊高度(m)	*/
	@Excel(name = "主吊起吊高度(m)	", width = 15)
    @ApiModelProperty(value = "主吊起吊高度(m)	")
    private BigDecimal mainLiftingHeight;
	/**副吊起重能力	*/
	@Excel(name = "副吊起重能力	", width = 15)
    @ApiModelProperty(value = "副吊起重能力	")
    private BigDecimal deputyLiftingCapacity;
	/**副吊起吊高度(m)	*/
	@Excel(name = "副吊起吊高度(m)	", width = 15)
    @ApiModelProperty(value = "副吊起吊高度(m)	")
    private BigDecimal deputyLiftingHeight;
	/**作业水深(m)	*/
	@Excel(name = "作业水深(m)	", width = 15)
    @ApiModelProperty(value = "作业水深(m)	")
    private BigDecimal operatingWaterDepth;
	/**起重机类型	*/
	@Excel(name = "起重机类型	", width = 15)
    @ApiModelProperty(value = "起重机类型	")
    private java.lang.String craneType;
	/**全回转起重能力	*/
	@Excel(name = "全回转起重能力	", width = 15)
    @ApiModelProperty(value = "全回转起重能力	")
    private java.lang.String fullLiftingCapacity;
	/**固定艉吊能力	*/
	@Excel(name = "固定艉吊能力	", width = 15)
    @ApiModelProperty(value = "固定艉吊能力	")
    private BigDecimal fixedLiftingAbility;
	/**吊高	*/
	@Excel(name = "吊高	", width = 15)
    @ApiModelProperty(value = "吊高	")
    private BigDecimal hangHigh;
	/**桩架高度	*/
	@Excel(name = "桩架高度	", width = 15)
    @ApiModelProperty(value = "桩架高度	")
    private BigDecimal frameHeight;
	/**起重能力	*/
	@Excel(name = "起重能力	", width = 15)
    @ApiModelProperty(value = "起重能力	")
    private BigDecimal elevatingCapacity;
	/**最大打桩长度	*/
	@Excel(name = "最大打桩长度	", width = 15)
    @ApiModelProperty(value = "最大打桩长度	")
    private BigDecimal maxPileLength;
	/**最大径桩	*/
	@Excel(name = "最大径桩	", width = 15)
    @ApiModelProperty(value = "最大径桩	")
    private BigDecimal maxDiameterPile;
	/**吨位（t）	*/
	@Excel(name = "吨位（t）	", width = 15)
    @ApiModelProperty(value = "吨位（t）	")
    private BigDecimal tonnage;
	/**可载人数	*/
	@Excel(name = "可载人数	", width = 15)
    @ApiModelProperty(value = "可载人数	")
    private java.lang.Integer carryNumber;
	/**总吨（t）*/
	@Excel(name = "总吨（t）", width = 15)
    @ApiModelProperty(value = "总吨（t）")
    private BigDecimal grossTon;
	/**净吨（t）*/
	@Excel(name = "净吨（t）", width = 15)
    @ApiModelProperty(value = "净吨（t）")
    private BigDecimal natTon;
	/**吃水（m）*/
	@Excel(name = "吃水（m）", width = 15)
    @ApiModelProperty(value = "吃水（m）")
    private BigDecimal draught;
	/**类型*/
	@Excel(name = "类型", width = 15)
    @ApiModelProperty(value = "类型")
    private java.lang.String shipType;
	/**航速（节）*/
	@Excel(name = "航速（节）", width = 15)
    @ApiModelProperty(value = "航速（节）")
    private BigDecimal shipSpeed;
    /**类型*/
    @Excel(name = "运输船类型", width = 20)
    @ApiModelProperty(value = "运输船类型")
    private java.lang.String transportShipType;

    /**是否关联*/
    @Excel(name = "是否关联", width = 20)
    @ApiModelProperty(value = "是否关联0/1 否/是")
    private java.lang.Integer isRelevance;
}
