package com.jinghe.breeze.modules.education.service;

import com.jinghe.breeze.modules.education.entity.EduQuestion;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghe.breeze.modules.education.entity.EduTrainingMaterial;
import lombok.NonNull;

import java.util.List;

/**
 * @Description: 题库
 * @Author: jeecg-boot
 * @Date: 2025-06-24
 * @Version: V1.0
 */
public interface IEduQuestionService extends IService<EduQuestion> {
    /**
     * 根据题目类型和标签随机获取指定数量的题目
     *
     * @param type  题目类型
     * @param tags  题目标签，多个标签用逗号分隔
     * @param count 需要获取的题目数量
     * @return 随机题目列表
     */
    List<EduQuestion> getRandomQuestionsByTypeAndTags(
            @NonNull String type,
            @NonNull String tags,
            @NonNull Integer count);

    /**
     * 保存题目并维护标签关联
     *
     * @param eduQuestion 题目信息
     * @return 是否成功
     */
    boolean saveQuestionWithTags(EduQuestion eduQuestion);

    /**
     * 更新题目并维护标签关联
     *
     * @param eduQuestion 题目信息
     * @return 是否成功
     */
    boolean updateQuestionWithTags(EduQuestion eduQuestion);

    /**
     * 删除题目并清理标签关联
     *
     * @param id 题目ID
     * @return 是否成功
     */
    boolean removeQuestionWithTags(String id);

    /**
     * 批量删除题目并清理标签关联
     *
     * @param ids 题目ID列表
     * @return 是否成功
     */
    boolean removeQuestionsWithTags(List<String> ids);
}
