# 多类型围栏检测功能说明

## 功能概述

系统支持两种类型的电子围栏检测：
- **安全防护围栏** (fence_type = '1'): 用于标识安全区域
- **施工围栏** (fence_type = '3'): 用于标识施工作业区域

### 核心特性
1. **多围栏支持**: 每种类型可以有多个围栏区域
2. **独立检测**: 分别检测是否在安全围栏内和施工围栏内
3. **互斥逻辑**: 两种围栏不重叠，设备只能在其中一种围栏内
4. **实时缓存**: 围栏数据缓存5分钟，提高检测性能

## 优化内容

### 1. 多类型围栏缓存机制
- **分类缓存**: `cachedSafetyFences` 和 `cachedConstructionFences` 分别缓存两种围栏
- **批量加载**: 一次性加载所有同类型围栏数据
- **缓存时间**: 5分钟自动过期，避免数据过时
- **线程安全**: 使用 `volatile` 和 `synchronized` 确保多线程安全

### 2. 实时多围栏检测
- **检测时机**: 在接收位置数据时立即进行两种围栏检测
- **检测逻辑**: 只要在任意一个同类型围栏内就返回true
- **数据存储**: 将两种围栏检测结果分别存储到Redis
- **性能优化**: 避免每次查询时重复检测

### 3. 兼容性处理
- **新旧数据兼容**: 支持处理旧格式的位置数据
- **补充检测**: 对于缺少围栏检测结果的数据进行补充检测
- **错误处理**: 围栏检测失败时仍保存原始位置数据

## 核心方法说明

### `loadFenceDataIfNeeded()`
```java
// 获取缓存的围栏数据，支持自动刷新
private void loadFenceDataIfNeeded()
```
- 分别加载安全防护围栏和施工围栏数据
- 检查缓存是否过期，使用双重检查锁定确保线程安全
- 自动从数据库重新加载所有同类型围栏数据

### `checkLocationInSafetyFence()`
```java
// 检测位置是否在安全防护围栏内
private boolean checkLocationInSafetyFence(Double longitude, Double latitude)
```
- 遍历所有安全防护围栏进行检测
- 只要在任意一个安全围栏内就返回true
- 调用 `MapWktUtil.isPointInPolygon()` 进行几何计算

### `checkLocationInConstructionFence()`
```java
// 检测位置是否在施工围栏内
private boolean checkLocationInConstructionFence(Double longitude, Double latitude)
```
- 遍历所有施工围栏进行检测
- 只要在任意一个施工围栏内就返回true
- 包含完整的错误处理

### `receiveLocationData()`
```java
// 接收位置数据并进行实时多围栏检测
public void receiveLocationData(List<LocationDataDTO> locationDataList)
```
- 对每个位置数据进行两种围栏检测
- 创建增强的位置数据对象，包含两个围栏检测结果
- 存储包含围栏检测结果的数据到Redis

### `refreshFenceCache()`
```java
// 手动刷新围栏缓存
public void refreshFenceCache()
```
- 清除两种围栏的缓存
- 立即重新加载围栏数据
- 用于围栏数据更新后的即时刷新

## API接口

### 1. 接收位置数据
```
POST /jeecg-boot/deviceHelmet/location/receive
```
**功能**: 接收安全帽位置数据，自动进行围栏检测

**请求体**:
```json
[
  {
    "puname": "HELMET_001",
    "gpstime": 1704067200000,
    "longitude": 116.400428,
    "latitude": 39.90423,
    "speed": 2.5,
    "power": "85",
    "alarmtype": "0",
    "datatype": "GPS",
    "reserved": ""
  }
]
```

### 2. 获取所有位置信息
```
GET /jeecg-boot/deviceHelmet/location/all
```
**功能**: 获取所有人员位置信息，包含围栏检测结果

**响应示例**:
```json
{
  "success": true,
  "result": [
    {
      "puname": "HELMET_001",
      "longitude": 116.400428,
      "latitude": 39.90423,
      "isInSafetyFence": true,
      "isInConstructionFence": false,
      "isRegistered": false,
      "isAssessed": null,
      "personId": null,
      "latestViolation": null
    },
    {
      "puname": "HELMET_002",
      "longitude": 116.413000,
      "latitude": 39.90500,
      "isInSafetyFence": false,
      "isInConstructionFence": true,
      "isRegistered": true,
      "isAssessed": true,
      "personId": "PERSON_001",
      "latestViolation": null
    }
  ]
}
```

### 3. 刷新围栏缓存
```
POST /jeecg-boot/deviceHelmet/fence/refresh
```
**功能**: 手动刷新围栏缓存，用于围栏数据更新后

**响应示例**:
```json
{
  "success": true,
  "message": "围栏缓存刷新成功"
}
```

## 数据流程

### 1. 位置数据接收流程
```
位置数据推送 → 围栏检测 → 创建增强数据对象 → 存储到Redis → 返回成功响应
```

### 2. 位置数据查询流程
```
查询请求 → 从Redis获取数据 → 补充人员信息 → 兼容性检测 → 返回完整数据
```

### 3. 围栏缓存流程
```
首次访问 → 从数据库加载 → 缓存5分钟 → 自动过期重新加载
```

## 性能优化

### 1. 缓存机制
- **减少数据库查询**: 围栏数据缓存5分钟，避免频繁查询
- **内存缓存**: 使用JVM内存缓存，访问速度快
- **懒加载**: 只在需要时加载围栏数据

### 2. 实时检测
- **前置检测**: 在数据接收时就完成围栏检测
- **避免重复计算**: 查询时直接使用已检测的结果
- **批量处理**: 支持批量位置数据的处理

### 3. 错误处理
- **优雅降级**: 围栏检测失败时仍保存原始数据
- **详细日志**: 记录检测过程和错误信息
- **兼容处理**: 支持新旧数据格式

## 使用建议

### 1. 围栏数据更新
当围栏数据在数据库中更新后，建议调用刷新缓存接口：
```bash
curl -X POST http://your-server/jeecg-boot/deviceHelmet/fence/refresh
```

### 2. 监控围栏外设备
可以通过查询接口监控围栏外的设备：
```javascript
// 过滤围栏外的设备
const outsideDevices = locations.filter(device => !device.isInFence);
```

### 3. 性能监控
- 监控围栏缓存命中率
- 关注围栏检测的响应时间
- 观察Redis存储的数据量

## 注意事项

1. **围栏数据格式**: 确保围栏数据为有效的JSON格式坐标数组
2. **坐标精度**: 建议使用6位小数的经纬度坐标
3. **缓存时间**: 可根据实际需求调整缓存过期时间
4. **并发处理**: 系统支持高并发的位置数据接收
5. **数据一致性**: 围栏数据更新后及时刷新缓存
