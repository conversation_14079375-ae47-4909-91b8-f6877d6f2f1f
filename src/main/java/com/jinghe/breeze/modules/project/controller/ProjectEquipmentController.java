package com.jinghe.breeze.modules.project.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.jinghe.breeze.modules.project.entity.ProjectEquipment;
import com.jinghe.breeze.modules.project.service.IProjectEquipmentService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: project_equipment
 * @Author: jeecg-boot
 * @Date: 2024-04-15
 * @Version: V1.0
 */
@Api(tags = "project_equipment")
@RestController
@RequestMapping("/project/projectEquipment")
@Slf4j
public class ProjectEquipmentController extends JeecgController<ProjectEquipment, IProjectEquipmentService> {
    @Autowired
    private IProjectEquipmentService projectEquipmentService;

    /**
     * 分页列表查询
     *
     * @param projectEquipment
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "project_equipment-分页列表查询")
    @ApiOperation(value = "project_equipment-分页列表查询", notes = "project_equipment-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(ProjectEquipment projectEquipment,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<ProjectEquipment> queryWrapper = QueryGenerator.initQueryWrapper(projectEquipment, req.getParameterMap());
        Page<ProjectEquipment> page = new Page<ProjectEquipment>(pageNo, pageSize);
        IPage<ProjectEquipment> pageList = projectEquipmentService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param projectEquipment
     * @return
     */
    @AutoLog(value = "project_equipment-添加")
    @ApiOperation(value = "project_equipment-添加", notes = "project_equipment-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody ProjectEquipment projectEquipment) {
        if (projectEquipment.getCode() == null) {
            return Result.error(400, "设备编号不能为空");
        }
        if (projectEquipment.getName() == null) {
            return Result.error(400, "设备名称不能为空");
        }
        if (projectEquipment.getType() == null) {
            return Result.error(400, "设备类型不能为空" );
        }

        projectEquipmentService.save(projectEquipment);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param projectEquipment
     * @return
     */
    @AutoLog(value = "project_equipment-编辑")
    @ApiOperation(value = "project_equipment-编辑", notes = "project_equipment-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody ProjectEquipment projectEquipment) {
        projectEquipmentService.updateById(projectEquipment);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "project_equipment-通过id删除")
    @ApiOperation(value = "project_equipment-通过id删除", notes = "project_equipment-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        projectEquipmentService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "project_equipment-批量删除")
    @ApiOperation(value = "project_equipment-批量删除", notes = "project_equipment-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.projectEquipmentService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "project_equipment-通过id查询")
    @ApiOperation(value = "project_equipment-通过id查询", notes = "project_equipment-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        ProjectEquipment projectEquipment = projectEquipmentService.getById(id);
        if (projectEquipment == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(projectEquipment);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param projectEquipment
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ProjectEquipment projectEquipment) {
        return super.exportXls(request, projectEquipment, ProjectEquipment.class, "project_equipment");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ProjectEquipment.class);
    }

}
