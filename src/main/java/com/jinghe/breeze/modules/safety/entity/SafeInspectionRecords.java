package com.jinghe.breeze.modules.safety.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.*;
/**
 * @Description: 安全检查
 * @Author: jeecg-boot
 * @Date:   2025-06-24
 * @Version: V1.0
 */
@Data
@TableName("safe_inspection_records")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="safe_inspection_records对象", description="安全检查")
public class SafeInspectionRecords implements Serializable {
    private static final long serialVersionUID = 1L;

	/**标识*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "标识")
    private String id;
	/**创建人*/

    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**更新人*/

    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
	/**delFlag*/
	@Excel(name = "delFlag", width = 15)
    @ApiModelProperty(value = "delFlag")
    private Integer delFlag;
	/**组织机构编码*/

    @ApiModelProperty(value = "组织机构编码")
    private String sysOrgCode;
	/**检查编号*/
	@Excel(name = "检查编号", width = 15)
    @ApiModelProperty(value = "检查编号")
    private String code;
	/**检查工区*/
	@Excel(name = "检查工区", width = 15)
    @ApiModelProperty(value = "检查工区")
    private String workArea;
	/**检查类型*/
	@Excel(name = "检查类型", width = 15, dicCode = "inspection_type")
	@Dict(dicCode = "inspection_type")
    @ApiModelProperty(value = "检查类型")
    private String inspectionType;
	/**检查日期*/
	@Excel(name = "检查日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "检查日期")
    private Date inspectionDate;
	/**检查结果*/
	@Excel(name = "检查结果", width = 15, dicCode = "inspection_result")
	@Dict(dicCode = "inspection_result")
    @ApiModelProperty(value = "检查结果")
    private String inspectionResult;
	/**检查人员*/
	@Excel(name = "检查人员", width = 15)
    @ApiModelProperty(value = "检查人员")
    @Dict(dictTable = "person_info", dicText = "name", dicCode = "id")
    private String inspector;
	/**检查上报人*/
	@Excel(name = "检查上报人", width = 15)
    @ApiModelProperty(value = "检查上报人")
    private String reporter;
	/**检查单位*/
	@Excel(name = "检查单位", width = 15)
    @ApiModelProperty(value = "检查单位")
    @Dict(dictTable = "project_unit", dicText = "name", dicCode = "id")
    private String reportingUnit;
	/**检查内容*/
	@Excel(name = "检查内容", width = 15)
    @ApiModelProperty(value = "检查内容")
    private String inspectionContent;
	/**检查依据*/
	@Excel(name = "检查依据", width = 15)
    @ApiModelProperty(value = "检查依据")
    private String inspectionBasis;

    @TableField(exist = false)
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "违规时间")
    private Date startDate;

    @TableField(exist = false)
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "违规时间")
    private Date endDate;

    @TableField(exist = false)
    private List<SafeHiddenDangerRecords> details;
}
