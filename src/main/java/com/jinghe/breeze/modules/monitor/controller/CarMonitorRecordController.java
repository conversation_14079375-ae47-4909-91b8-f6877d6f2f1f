package com.jinghe.breeze.modules.monitor.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinghe.breeze.common.enums.ActionEnum;
import com.jinghe.breeze.common.enums.DelFlagEnum;
import com.jinghe.breeze.common.enums.GateTypeEnum;
import com.jinghe.breeze.modules.base.entity.BaseArea;
import com.jinghe.breeze.modules.base.service.IBaseAreaService;
import com.jinghe.breeze.modules.device.entity.DeviceGate;
import com.jinghe.breeze.modules.device.entity.DeviceInfo;
import com.jinghe.breeze.modules.device.service.IDeviceGateService;
import com.jinghe.breeze.modules.device.service.IDeviceInfoService;
import com.jinghe.breeze.modules.monitor.entity.CarMonitorRecord;
import com.jinghe.breeze.modules.monitor.service.ICarMonitorRecordService;
import com.jinghe.breeze.modules.monitor.vo.DataObjectVo;
import com.jinghe.breeze.modules.monitor.vo.MonitorRecordPageVo;
import com.jinghe.breeze.modules.monitor.vo.MonitorRecordVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: monitor_car_record
 * @Author: jeecg-boot
 * @Date: 2024-08-07
 * @Version: V1.0
 */
@Api(tags = "车辆进出记录")
@RestController
@RequestMapping("/monitor/car")
@Slf4j
public class CarMonitorRecordController extends JeecgController<CarMonitorRecord, ICarMonitorRecordService> {
    @Autowired
    private ICarMonitorRecordService iService;

    @Autowired
    private IDeviceInfoService deviceInfoService;

    @Autowired
    private IDeviceGateService deviceGateService;

    @Autowired
    private IBaseAreaService baseAreaService;

    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");


    /**
     * 分页列表查询
     *
     * @param monitorCarRecord
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "monitor_car_record-分页列表查询")
    @ApiOperation(value = "monitor_car_record-分页列表查询", notes = "monitor_car_record-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(CarMonitorRecord monitorCarRecord,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   @RequestParam(name = "startDate", required = false) String startDate,
                                   @RequestParam(name = "endDate", required = false) String endDate,
                                   HttpServletRequest req) {
        String deviceId = null;
        if (monitorCarRecord.getDeviceId() != null) {
            deviceId = monitorCarRecord.getDeviceId();
            monitorCarRecord.setDeviceId(null);
        }
        QueryWrapper<CarMonitorRecord> queryWrapper = QueryGenerator.initQueryWrapper(monitorCarRecord, req.getParameterMap());
        if (deviceId != null) {
            queryWrapper.eq("device_id", deviceId);
        }
        if (!StringUtils.isEmpty(startDate) && !StringUtils.isEmpty(endDate)) {
            try {
                Date start = sdf.parse(startDate + " 00:00:00");
                Date end = sdf.parse(endDate + " 23:59:59");
                queryWrapper.ge("record_time", start);
                queryWrapper.le("record_time", end);
            } catch (Exception e) {
                log.warn("illegal date string");
            }
        }
        Page<CarMonitorRecord> page = new Page<>(pageNo, pageSize);
        IPage<CarMonitorRecord> pageList = iService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param monitorCarRecord
     * @return
     */
    @AutoLog(value = "monitor_car_record-添加")
    @ApiOperation(value = "monitor_car_record-添加", notes = "monitor_car_record-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody CarMonitorRecord monitorCarRecord) {
        iService.save(monitorCarRecord);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param monitorCarRecord
     * @return
     */
    @AutoLog(value = "monitor_car_record-编辑")
    @ApiOperation(value = "monitor_car_record-编辑", notes = "monitor_car_record-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody CarMonitorRecord monitorCarRecord) {
        iService.updateById(monitorCarRecord);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "monitor_car_record-通过id删除")
    @ApiOperation(value = "monitor_car_record-通过id删除", notes = "monitor_car_record-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        iService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "monitor_car_record-批量删除")
    @ApiOperation(value = "monitor_car_record-批量删除", notes = "monitor_car_record-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        iService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "monitor_car_record-通过id查询")
    @ApiOperation(value = "monitor_car_record-通过id查询", notes = "monitor_car_record-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        CarMonitorRecord monitorCarRecord = iService.getById(id);
        if (monitorCarRecord == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(monitorCarRecord);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param monitorCarRecord
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CarMonitorRecord monitorCarRecord) {
        return super.exportXls(request, monitorCarRecord, CarMonitorRecord.class, "monitor_car_record");
    }


    @AutoLog(value = "车辆通行-列表查询")
    @ApiOperation(value = "车辆通行-列表查询", notes = "车辆通行-列表查询")
    @GetMapping(value = "/carList")
    public Result<?> queryList(CarMonitorRecord carMonitorRecord) {
        //只查询车辆闸机的
        LambdaQueryWrapper<DeviceGate> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DeviceGate::getDelFlag, DelFlagEnum.NORMAL.getType());
        lambdaQueryWrapper.eq(DeviceGate::getGateType, GateTypeEnum.CAR.getType());
        List<DeviceGate> deviceGates = deviceGateService.list(lambdaQueryWrapper);
        if (CollectionUtil.isEmpty(deviceGates)) {
            return Result.OK();
        }
        List<String> ids = deviceGates.stream().map(DeviceGate::getBaseId).collect(Collectors.toList());
        LambdaQueryWrapper<DeviceInfo> gateQueryWrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotEmpty(carMonitorRecord.getName())) {
            gateQueryWrapper.like(DeviceInfo::getName, carMonitorRecord.getName());
        }
        if (StrUtil.isNotEmpty(carMonitorRecord.getAreaId())) {
            gateQueryWrapper.eq(DeviceInfo::getAreaId, carMonitorRecord.getAreaId());
        }
        gateQueryWrapper.in(DeviceInfo::getId, ids);
        gateQueryWrapper.eq(DeviceInfo::getDelFlag, DelFlagEnum.NORMAL.getType());
        List<DeviceInfo> list = deviceInfoService.list(gateQueryWrapper);
        //按设备统计出入场数据
        List<MonitorRecordVo> recordVoList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            List<String> deviceIds = list.stream().map(DeviceInfo::getId).collect(Collectors.toList());
            QueryWrapper<CarMonitorRecord> recordQueryWrapper = new QueryWrapper<>();
            recordQueryWrapper.in("device_id", deviceIds);
            recordQueryWrapper.eq("del_flag", DelFlagEnum.NORMAL.getType());
            // 获取当天的开始时间和结束时间
            LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
            LocalDateTime endOfDay = LocalDate.now().atTime(LocalTime.MAX);
            // 设置安装时间为当天的条件（时间范围查询）
            recordQueryWrapper.between("record_time", startOfDay, endOfDay);
            List<CarMonitorRecord> recordList = iService.list(recordQueryWrapper);
            for (DeviceInfo baseDevice : list) {
                MonitorRecordVo monitorPersonRecordVo = new MonitorRecordVo();
                monitorPersonRecordVo.setId(baseDevice.getId());
                monitorPersonRecordVo.setName(baseDevice.getName());
                monitorPersonRecordVo.setInstallArea(baseDevice.getAreaId());
                List<CarMonitorRecord> collect = recordList.stream().filter(item -> item.getDeviceId().equals(baseDevice.getId())).collect(Collectors.toList());

                Long inCount = collect.stream()
                        .filter(record -> ActionEnum.IN.getType().equals(record.getAction()))
                        .map(CarMonitorRecord::getLicensePlate)
                        .distinct()
                        .count();

                Long outCount = collect.stream()
                        .filter(record -> ActionEnum.OUT.getType().equals(record.getAction()))
                        .map(CarMonitorRecord::getLicensePlate)
                        .distinct()
                        .count();

                Long totalCount = collect.stream()
                        .map(CarMonitorRecord::getLicensePlate)
                        .distinct()
                        .count();
                monitorPersonRecordVo.setInCount(inCount.intValue());
                monitorPersonRecordVo.setOutCount(outCount.intValue());
                monitorPersonRecordVo.setCount(totalCount.intValue());
                recordVoList.add(monitorPersonRecordVo);
            }
        }
        return Result.OK(recordVoList);
    }

    @AutoLog(value = "车辆通行-今日入场车辆分布情况")
    @ApiOperation(value = "车辆通行-今日入场车辆分布情况", notes = "车辆通行-今日入场车辆分布情况")
    @GetMapping(value = "/carStatisticsByArea")
    public Result<?> personStatisticsByArea() {
        QueryWrapper<DeviceInfo> gateQueryWrapper = new QueryWrapper<>();
        gateQueryWrapper.eq("del_flag", DelFlagEnum.NORMAL.getType());
        List<DeviceInfo> list = deviceInfoService.list(gateQueryWrapper);
        Map<String, String> gateArea = list.stream().collect(Collectors.toMap(DeviceInfo::getId, DeviceInfo::getAreaId));
        List<BaseArea> baseAreas = baseAreaService.list();
        //按设备统计出入场数据
        List<Map<String, Object>> result = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            List<String> ids = list.stream().map(DeviceInfo::getId).collect(Collectors.toList());
            QueryWrapper<CarMonitorRecord> recordQueryWrapper = new QueryWrapper<>();
            recordQueryWrapper.in("device_id", ids);
            recordQueryWrapper.eq("del_flag", DelFlagEnum.NORMAL.getType());
            // 获取当天的开始时间和结束时间
            LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
            LocalDateTime endOfDay = LocalDate.now().atTime(LocalTime.MAX);
            // 设置安装时间为当天的条件（时间范围查询）
            recordQueryWrapper.between("record_time", startOfDay, endOfDay);
            List<CarMonitorRecord> recordList = iService.list(recordQueryWrapper);
            for (BaseArea area : baseAreas) {
                Map<String, Object> areaItem = new HashMap<>();
                areaItem.put("title", area.getName());
                List<CarMonitorRecord> areaCol = recordList.stream().filter(item -> {
                            String areaId = gateArea.get(item.getDeviceId());
                            if (StrUtil.isNotEmpty(areaId)) {
                                return ActionEnum.IN.getType().equals(item.getAction()) && areaId.equals(area.getId());
                            } else {
                                return false;
                            }
                        })
                        .collect(Collectors.toList());
                if (areaCol.isEmpty()) {
                    areaItem.put("value", 0);
                } else {
                    Long intCount = areaCol.stream()
                            .map(CarMonitorRecord::getLicensePlate)
                            .distinct()
                            .count();
                    areaItem.put("value", intCount);
                }
                result.add(areaItem);
            }
        }
        return Result.OK(result);
    }

    @AutoLog(value = "车辆通行-近15日统计")
    @ApiOperation(value = "车辆通行-近15日统计", notes = "车辆通行-近15日统计")
    @GetMapping(value = "/carStatistics")
    public Result<?> statisticsByHalfMonth() {
        List<DataObjectVo> objects = iService.statisticsByHalfMonth();
        return Result.OK(objects);
    }

    @AutoLog(value = "车辆通行-近7日统计")
    @ApiOperation(value = "车辆通行-近7日统计", notes = "车辆通行-近7日统计")
    @GetMapping(value = "/carStatistics/{gateId}")
    public Result<?> statisticsByWeek(@PathVariable("gateId") String gateId) {
        List<DataObjectVo> objects = iService.statisticsByWeek(gateId);
        return Result.OK(objects);
    }

    @AutoLog(value = "车辆通行-最近3次进出记录")
    @ApiOperation(value = "车辆通行-最近3次进出记录", notes = "车辆通行-最近3次进出记录")
    @GetMapping(value = "/lastThirdRecord")
    public Result<?> getlastRecords(@RequestParam("gateId") String gateId) {
        QueryWrapper<CarMonitorRecord> recordQueryWrapper = new QueryWrapper<>();
        recordQueryWrapper.eq("del_flag", DelFlagEnum.NORMAL.getType());
        recordQueryWrapper.eq("device_id", gateId);
        // 按照 record_time 降序排列，并只取最新的3条数据
        recordQueryWrapper.orderByDesc("record_time");
        recordQueryWrapper.last("LIMIT 4");
        List<CarMonitorRecord> recordList = iService.list(recordQueryWrapper);
        return Result.OK(recordList);
    }

    @AutoLog(value = "车辆通行-监控信息")
    @ApiOperation(value = "车辆通行-监控信息", notes = "车辆通行-监控信息")
    @GetMapping(value = "/getMonitorInfos")
    public Result<?> getMonitorInfos(@RequestParam("gateId") String gateId) {
        LambdaQueryWrapper<DeviceGate> recordQueryWrapper = new LambdaQueryWrapper<>();
        recordQueryWrapper.eq(DeviceGate::getDelFlag, DelFlagEnum.NORMAL.getType());
        recordQueryWrapper.eq(DeviceGate::getBaseId, gateId);
        recordQueryWrapper.eq(DeviceGate::getGateType,GateTypeEnum.CAMERA.getType());
        recordQueryWrapper.orderByDesc(DeviceGate::getCreateTime);
        List<DeviceGate> recordList = deviceGateService.list(recordQueryWrapper);
        return Result.OK(recordList);
    }

    @AutoLog(value = "车辆通行-今日通行实况")
    @ApiOperation(value = "车辆通行-今日通行实况", notes = "车辆通行-今日通行实况")
    @GetMapping(value = "/attendanceRecords")
    public Result<?> getAttendanceRecords(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                          @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                          @RequestParam(name = "gateId") String gateId) {
        Page<CarMonitorRecord> page = new Page<>(pageNo, pageSize);
        IPage<MonitorRecordPageVo> pageList = iService.getAttendanceRecords(page, gateId);
        return Result.OK(pageList);
    }

    @AutoLog(value = "车辆通行-今日通行详情")
    @ApiOperation(value = "车辆通行-今日通行详情", notes = "车辆通行-今日通行详情")
    @GetMapping(value = "/attendanceDetailRecords")
    public Result<?> getAttendanceDetailRecords(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                @RequestParam(name = "gateId") String gateId,
                                                @RequestParam(name = "licensePlate") String licensePlate) {
        Page<CarMonitorRecord> page = new Page<>(pageNo, pageSize);
        QueryWrapper<CarMonitorRecord> recordQueryWrapper = new QueryWrapper<>();
        recordQueryWrapper.eq("del_flag", DelFlagEnum.NORMAL.getType());
        recordQueryWrapper.eq("device_id", gateId);
        // 获取当天的开始时间和结束时间
        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        LocalDateTime endOfDay = LocalDate.now().atTime(LocalTime.MAX);
        // 设置安装时间为当天的条件（时间范围查询）
        recordQueryWrapper.between("record_time", startOfDay, endOfDay);
        recordQueryWrapper.eq("license_plate", licensePlate);
        IPage<CarMonitorRecord> recordList = iService.page(page, recordQueryWrapper);
        return Result.OK(recordList);
    }

    @AutoLog(value = "车辆通行-今日在场总车辆")
    @ApiOperation(value = "车辆通行-今日在场总车辆", notes = "车辆通行-今日在场总车辆")
    @GetMapping(value = "/carTotalCount")
    public Result<?> carTotalCount() {
        String count = iService.carTotalCount();
        return Result.OK(count);
    }
}
