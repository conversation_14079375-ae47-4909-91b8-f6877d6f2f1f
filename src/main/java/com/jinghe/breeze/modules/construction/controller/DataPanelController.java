package com.jinghe.breeze.modules.construction.controller;

import com.jinghe.breeze.modules.construction.service.IDataPanelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Api(tags = "二维看板")
@RestController
@RequestMapping("/dataPanel")
@Slf4j
public class DataPanelController {

    @Autowired
    private IDataPanelService iDataPanelService;
    /**
     *
     *
     * @return
     */
    @AutoLog(value = "施工情况")
    @ApiOperation(value = "施工情况", notes = "施工情况")
    @GetMapping(value = "/condition")
    public Result<?> condition(@RequestParam(name = "projectId", required = false) String projectId) {
        Map<String, Object> map = iDataPanelService.condition(projectId);
        return Result.OK(map);
    }

    /**
     * 施工进度
     *
     * @param
     * @return
     */
    @AutoLog(value = "施工进度")
    @ApiOperation(value = "施工进度", notes = "进度分析")
    @GetMapping(value = "/schedule")
    public Result<?> schedule(@RequestParam(name = "pbsId", required = false) String pbsId) {
        return iDataPanelService.schedule(pbsId);
    }
}
