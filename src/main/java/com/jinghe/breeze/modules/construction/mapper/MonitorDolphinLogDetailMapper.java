package com.jinghe.breeze.modules.construction.mapper;

import com.jinghe.breeze.modules.construction.entity.MonitorDolphinLogDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: 观豚日志列表
 * @Author: jeecg-boot
 * @Date: 2024-05-11
 * @Version: V1.0
 */
public interface MonitorDolphinLogDetailMapper extends BaseMapper<MonitorDolphinLogDetail> {

    @Select("SELECT c.* FROM monitor_dolphin_log p JOIN monitor_dolphin_log_detail c ON c.watch_id = p.id WHERE DATE(p.watch_date) between #{startDate} and #{startDate}")
    List<MonitorDolphinLogDetail> getLogDetailsBetweenDate(String startDate);


}
