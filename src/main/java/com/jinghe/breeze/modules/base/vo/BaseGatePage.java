package com.jinghe.breeze.modules.base.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jinghe.breeze.modules.base.entity.BaseGateEquipment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @Description: base_gate
 * @Author: jeecg-boot
 * @Date: 2024-08-06
 * @Version: V1.0
 */
@Data
@ApiModel(value = "base_gatePage对象", description = "base_gate")
public class BaseGatePage {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 闸机名称
     */
    @Excel(name = "闸机名称", width = 15)
    @ApiModelProperty(value = "闸机名称")
    private String gateName;
    /**
     * 安装区域
     */
    @Excel(name = "安装区域", width = 15)
    @ApiModelProperty(value = "安装区域")
    private String installArea;
    /**
     * 安装位置
     */
    @Excel(name = "安装位置", width = 15)
    @ApiModelProperty(value = "安装位置")
    private String installPosition;
    /**
     * 安装日期
     */
    @Excel(name = "安装日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "安装日期")
    private Date installDate;
    /**
     * 备注
     */
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remarks;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;

    /**
     * 坐标点X轴
     */
    @ApiModelProperty(value = "坐标点X轴")
    private String positionX;
    /**
     * 坐标点Y轴
     */
    @ApiModelProperty(value = "坐标点Y轴")
    private String positionY;

    @ExcelCollection(name = "base_gate_equipment")
    @ApiModelProperty(value = "base_gate_equipment")
    private List<BaseGateEquipment> baseGateEquipmentList;

}
