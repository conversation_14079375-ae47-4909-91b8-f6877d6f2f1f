package com.jinghe.breeze.modules.device.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
 * @Description: 关注的监控信息
 * @Author: jeecg-boot
 * @Date: 2024-08-14
 * @Version: V1.0
 */
@Data
@TableName("device_star")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "device_star对象", description = "关注的监控信息")
public class DeviceStar implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
    /**
     * 关注人id
     */
    @Excel(name = "关注人id", width = 15)
    @ApiModelProperty(value = "关注人id")
    private String userId;
    /**
     * 设备id
     */
    @Excel(name = "设备id", width = 15)
    @ApiModelProperty(value = "设备id")
    private String deviceId;
    /**
     * 删除标识
     */
    @Excel(name = "删除标识", width = 15)
    @ApiModelProperty(value = "删除标识")
    private String delFlag = "0";

    /**
     * 监控编码
     */
    @Excel(name = "监控地址", width = 15)
    @ApiModelProperty(value = "监控编码")
    private String equipmentSerial;
    /**
     * 监控名称
     */
    @Excel(name = "监控名称", width = 15)
    @ApiModelProperty(value = "监控名称")
    private String name;

    /**
     * 安装区域
     */
    @Excel(name = "安装区域", width = 15)
    @ApiModelProperty(value = "安装区域")
    private String installPosition;
}
