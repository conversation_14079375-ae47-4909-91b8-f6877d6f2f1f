package com.jinghe.breeze.modules.quality.service.impl;

import com.jinghe.breeze.common.enums.DelFlagEnum;
import com.jinghe.breeze.modules.quality.entity.QualityInspectionRecords;
import com.jinghe.breeze.modules.quality.entity.QualityIssueRectifications;
import com.jinghe.breeze.modules.quality.mapper.QualityInspectionRecordsMapper;
import com.jinghe.breeze.modules.quality.service.IQualityInspectionRecordsService;
import com.jinghe.breeze.modules.quality.service.IQualityIssueRectificationsService;
import org.apache.shiro.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description: quality_inspection_records
 * @Author: jeecg-boot
 * @Date: 2025-06-20
 * @Version: V1.0
 */
@Service
public class QualityInspectionRecordsServiceImpl extends ServiceImpl<QualityInspectionRecordsMapper, QualityInspectionRecords> implements IQualityInspectionRecordsService {
    @Autowired
    private IQualityIssueRectificationsService qualityIssueRectificationsService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createOrUpdate(QualityInspectionRecords qualityInspectionRecords) {
        List<QualityIssueRectifications> newDetails = qualityInspectionRecords.getDetails();
        if (StringUtils.hasLength(qualityInspectionRecords.getId())) {
            updateById(qualityInspectionRecords);
        } else {
            save(qualityInspectionRecords);
        }
        // 2. 根据子表details找出旧的记录，并处理差异
        processDetailsDifference(qualityInspectionRecords.getId(), newDetails);
    }

    /**
     * 处理子表数据的差异（新增、更新、删除）
     */
    private void processDetailsDifference(String recordId, List<QualityIssueRectifications> newDetails) {
        // 查询现有的子表数据
        LambdaQueryWrapper<QualityIssueRectifications> queryWrapper = new LambdaQueryWrapper<QualityIssueRectifications>()
                .eq(QualityIssueRectifications::getRecordId, recordId)
                .eq(QualityIssueRectifications::getDelFlag, DelFlagEnum.NORMAL.getType());
        List<QualityIssueRectifications> existingDetails = qualityIssueRectificationsService.list(queryWrapper);
        // 如果新数据为空，删除所有现有数据
        if (newDetails == null || newDetails.isEmpty()) {
            if (!existingDetails.isEmpty()) {
                List<String> existingIds = existingDetails.stream()
                        .map(QualityIssueRectifications::getId)
                        .filter(StringUtils::hasLength)
                        .collect(Collectors.toList());
                if (!existingIds.isEmpty()) {
                    qualityIssueRectificationsService.removeByIds(existingIds);
                }
            }
            return;
        }
        // 为新数据设置recordId字段为主表id
        newDetails.forEach(detail -> detail.setRecordId(recordId));
        // 创建现有数据的ID映射
        Map<String, QualityIssueRectifications> existingMap = existingDetails.stream()
                .filter(detail -> StringUtils.hasLength(detail.getId()))
                .collect(Collectors.toMap(QualityIssueRectifications::getId, detail -> detail));
        // 分类处理新数据
        List<QualityIssueRectifications> toInsert = new ArrayList<>();  // 需要新增的
        List<QualityIssueRectifications> toUpdate = new ArrayList<>();  // 需要更新的
        Set<String> processedIds = new HashSet<>();                     // 已处理的ID
        for (QualityIssueRectifications newDetail : newDetails) {
            if (StringUtils.hasLength(newDetail.getId()) && existingMap.containsKey(newDetail.getId())) {
                // 有ID且存在于现有数据中 -> 更新
                toUpdate.add(newDetail);
                processedIds.add(newDetail.getId());
            } else {
                // 无ID或ID不存在于现有数据中 -> 新增
                newDetail.setId(null); // 确保ID为空，让数据库自动生成
                toInsert.add(newDetail);
            }
        }
        // 找出需要删除的数据（现有数据中未被处理的）
        List<String> toDeleteIds = existingDetails.stream()
                .map(QualityIssueRectifications::getId)
                .filter(id -> StringUtils.hasLength(id) && !processedIds.contains(id))
                .collect(Collectors.toList());
        // 3. 提交子表 - 执行数据库操作
        if (!toInsert.isEmpty()) {
            qualityIssueRectificationsService.saveBatch(toInsert);
        }
        if (!toUpdate.isEmpty()) {
            qualityIssueRectificationsService.updateBatchById(toUpdate);
        }
        if (!toDeleteIds.isEmpty()) {
            qualityIssueRectificationsService.removeByIds(toDeleteIds);
        }
    }

    @Override
    public void fullDetails(List<QualityInspectionRecords> list) {
        if (list == null || list.isEmpty()) {
            return;
        }
        // 1. 获取所有质量检查记录的ID
        List<String> recordIds = list.stream()
                .map(QualityInspectionRecords::getId)
                .filter(StringUtils::hasLength)
                .collect(Collectors.toList());
        if (recordIds.isEmpty()) {
            return;
        }
        // 2. 批量查询所有相关的详情数据
        LambdaQueryWrapper<QualityIssueRectifications> queryWrapper = new LambdaQueryWrapper<QualityIssueRectifications>()
                .eq(QualityIssueRectifications::getDelFlag, DelFlagEnum.NORMAL.getType())
                .in(QualityIssueRectifications::getRecordId, recordIds);
        List<QualityIssueRectifications> allDetails = qualityIssueRectificationsService.list(queryWrapper);
        // 3. 按recordId分组详情数据
        Map<String, List<QualityIssueRectifications>> detailsByRecordId = allDetails.stream()
                .collect(Collectors.groupingBy(QualityIssueRectifications::getRecordId));
        // 4. 为每个质量检查记录填充details信息
        list.forEach(record -> {
            String recordId = record.getId();
            List<QualityIssueRectifications> details = detailsByRecordId.get(recordId);
            record.setDetails(details != null ? details : new ArrayList<>());
        });
    }

}
