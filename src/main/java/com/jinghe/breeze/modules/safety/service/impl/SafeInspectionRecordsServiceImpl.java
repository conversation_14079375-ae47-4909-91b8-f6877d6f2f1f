package com.jinghe.breeze.modules.safety.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.common.enums.DelFlagEnum;
import com.jinghe.breeze.modules.safety.entity.SafeHiddenDangerRecords;
import com.jinghe.breeze.modules.safety.entity.SafeInspectionRecords;
import com.jinghe.breeze.modules.safety.mapper.SafeInspectionRecordsMapper;
import com.jinghe.breeze.modules.safety.service.ISafeHiddenDangerRecordsService;
import com.jinghe.breeze.modules.safety.service.ISafeInspectionRecordsService;
import org.apache.shiro.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 安全检查
 * @Author: jeecg-boot
 * @Date:   2025-06-24
 * @Version: V1.0
 */
@Service
public class SafeInspectionRecordsServiceImpl extends ServiceImpl<SafeInspectionRecordsMapper, SafeInspectionRecords> implements ISafeInspectionRecordsService {
@Autowired
private ISafeHiddenDangerRecordsService safeHiddenDangerRecordsService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createOrUpdate(SafeInspectionRecords records) {
        List<SafeHiddenDangerRecords> newDetails = records.getDetails();
        if (StringUtils.hasLength(records.getId())) {
            updateById(records);
        } else {
            save(records);
        }
        // 2. 根据子表details找出旧的记录，并处理差异
        processDetailsDifference(records.getId(), newDetails);
    }

    /**
     * 处理子表数据的差异（新增、更新、删除）
     */
    private void processDetailsDifference(String recordId, List<SafeHiddenDangerRecords> newDetails) {
        // 查询现有的子表数据
        LambdaQueryWrapper<SafeHiddenDangerRecords> queryWrapper = new LambdaQueryWrapper<SafeHiddenDangerRecords>()
                .eq(SafeHiddenDangerRecords::getRecordId, recordId)
                .eq(SafeHiddenDangerRecords::getDelFlag, DelFlagEnum.NORMAL.getType());
        List<SafeHiddenDangerRecords> existingDetails = safeHiddenDangerRecordsService.list(queryWrapper);
        // 如果新数据为空，删除所有现有数据
        if (newDetails == null || newDetails.isEmpty()) {
            if (!existingDetails.isEmpty()) {
                List<String> existingIds = existingDetails.stream()
                        .map(SafeHiddenDangerRecords::getId)
                        .filter(StringUtils::hasLength)
                        .collect(Collectors.toList());
                if (!existingIds.isEmpty()) {
                    safeHiddenDangerRecordsService.removeByIds(existingIds);
                }
            }
            return;
        }
        // 为新数据设置recordId字段为主表id
        newDetails.forEach(detail -> detail.setRecordId(recordId));
        // 创建现有数据的ID映射
        Map<String, SafeHiddenDangerRecords> existingMap = existingDetails.stream()
                .filter(detail -> StringUtils.hasLength(detail.getId()))
                .collect(Collectors.toMap(SafeHiddenDangerRecords::getId, detail -> detail));
        // 分类处理新数据
        List<SafeHiddenDangerRecords> toInsert = new ArrayList<>();  // 需要新增的
        List<SafeHiddenDangerRecords> toUpdate = new ArrayList<>();  // 需要更新的
        Set<String> processedIds = new HashSet<>();                     // 已处理的ID
        for (SafeHiddenDangerRecords newDetail : newDetails) {
            if (StringUtils.hasLength(newDetail.getId()) && existingMap.containsKey(newDetail.getId())) {
                // 有ID且存在于现有数据中 -> 更新
                toUpdate.add(newDetail);
                processedIds.add(newDetail.getId());
            } else {
                // 无ID或ID不存在于现有数据中 -> 新增
                newDetail.setId(null); // 确保ID为空，让数据库自动生成
                toInsert.add(newDetail);
            }
        }
        // 找出需要删除的数据（现有数据中未被处理的）
        List<String> toDeleteIds = existingDetails.stream()
                .map(SafeHiddenDangerRecords::getId)
                .filter(id -> StringUtils.hasLength(id) && !processedIds.contains(id))
                .collect(Collectors.toList());
        // 3. 提交子表 - 执行数据库操作
        if (!toInsert.isEmpty()) {
            safeHiddenDangerRecordsService.saveBatch(toInsert);
        }
        if (!toUpdate.isEmpty()) {
            safeHiddenDangerRecordsService.updateBatchById(toUpdate);
        }
        if (!toDeleteIds.isEmpty()) {
            safeHiddenDangerRecordsService.removeByIds(toDeleteIds);
        }
    }

    @Override
    public void fullDetails(List<SafeInspectionRecords> list) {
        if (list == null || list.isEmpty()) {
            return;
        }
        // 1. 获取所有质量检查记录的ID
        List<String> recordIds = list.stream()
                .map(SafeInspectionRecords::getId)
                .filter(StringUtils::hasLength)
                .collect(Collectors.toList());
        if (recordIds.isEmpty()) {
            return;
        }
        // 2. 批量查询所有相关的详情数据
        LambdaQueryWrapper<SafeHiddenDangerRecords> queryWrapper = new LambdaQueryWrapper<SafeHiddenDangerRecords>()
                .eq(SafeHiddenDangerRecords::getDelFlag, DelFlagEnum.NORMAL.getType())
                .in(SafeHiddenDangerRecords::getRecordId, recordIds);
        List<SafeHiddenDangerRecords> allDetails = safeHiddenDangerRecordsService.list(queryWrapper);
        // 3. 按recordId分组详情数据
        Map<String, List<SafeHiddenDangerRecords>> detailsByRecordId = allDetails.stream()
                .collect(Collectors.groupingBy(SafeHiddenDangerRecords::getRecordId));
        // 4. 为每个质量检查记录填充details信息
        list.forEach(record -> {
            String recordId = record.getId();
            List<SafeHiddenDangerRecords> details = detailsByRecordId.get(recordId);
            record.setDetails(details != null ? details : new ArrayList<>());
        });
    }
}
