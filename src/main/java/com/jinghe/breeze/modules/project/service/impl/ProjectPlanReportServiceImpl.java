package com.jinghe.breeze.modules.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.modules.project.entity.ConstructionPlanProcess;
import com.jinghe.breeze.modules.project.entity.ProjectPlan;
import com.jinghe.breeze.modules.project.entity.ProjectPlanReport;
import com.jinghe.breeze.modules.project.mapper.ConstructionPlanProcessMapper;
import com.jinghe.breeze.modules.project.mapper.ProjectPlanMapper;
import com.jinghe.breeze.modules.project.mapper.ProjectPlanReportMapper;
import com.jinghe.breeze.modules.project.service.IProjectPlanReportService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.oConvertUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description: project_plan_report
 * @Author: jeecg-boot
 * @Date: 2024-05-10
 * @Version: V1.0
 */
@Service
public class ProjectPlanReportServiceImpl extends ServiceImpl<ProjectPlanReportMapper, ProjectPlanReport> implements IProjectPlanReportService {
    @Autowired
    private ProjectPlanMapper planMapper;
    @Autowired
    private ConstructionPlanProcessMapper planProcessMapper;

    @Override
    public Result<?> analysis() {
        LambdaQueryWrapper<ProjectPlan> planQuery = new LambdaQueryWrapper<>();
        planQuery.orderByDesc(ProjectPlan::getVersion);
        planQuery.last("LIMIT 1");
        /*查询最后一个版本*/
        ProjectPlan one = planMapper.selectOne(planQuery);
        if (StringUtils.isEmpty(one)) {
            return Result.OK();
        }
        LambdaQueryWrapper<ProjectPlan> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectPlan::getVersion, one.getVersion());
        queryWrapper.eq(ProjectPlan::getIsKeytask, Common.commonality.ONE);
        List<ProjectPlan> projectPlans = planMapper.selectList(queryWrapper);
        if (projectPlans.size() == 0) {
            return Result.OK();
        }
        List<String> codes = projectPlans.stream().map(x -> x.getCode()).collect(Collectors.toList());
        LambdaQueryWrapper<ProjectPlanReport> reportWrapper = new LambdaQueryWrapper<>();
        reportWrapper.in(ProjectPlanReport::getBaseCode, codes);
        reportWrapper.eq(ProjectPlanReport::getDelFlag, Common.delete_flag.OK);
        List<ProjectPlanReport> list = super.list(reportWrapper);
        for (ProjectPlan projectPlan : projectPlans) {
            Optional<ProjectPlanReport> first = list.stream().filter(x -> x.getBaseCode().equals(projectPlan.getCode())).findFirst();
            if (first.isPresent()) {
                projectPlan.setProjectPlanReport(first.get());
            }
        }
        return Result.OK(projectPlans);
    }

    @Override
    public Result<?> getAll(Integer version, String name, Integer status) {
        LambdaQueryWrapper<ProjectPlan> planQuery = new LambdaQueryWrapper<>();
        if (!StringUtils.isEmpty(version)) {
            planQuery.eq(ProjectPlan::getVersion, version);
        } else {
            planQuery.orderByDesc(ProjectPlan::getVersion);
            planQuery.last("LIMIT 1");
            /*查询最后一个版本*/
            ProjectPlan one = planMapper.selectOne(planQuery);
            if (oConvertUtils.isNotEmpty(one)) {
                planQuery = new LambdaQueryWrapper<>();
                version = one.getVersion();
                planQuery.eq(ProjectPlan::getVersion, version);
            }
        }
        if (!StringUtils.isEmpty(name)) {
            planQuery.like(ProjectPlan::getName, name);
        }
        List<ProjectPlan> projectPlanList = planMapper.selectList(planQuery);
        if (projectPlanList.size() == 0) {
            return Result.OK();
        }
        List<String> codes = projectPlanList.stream().map(x -> x.getCode()).collect(Collectors.toList());
        LambdaQueryWrapper<ProjectPlanReport> reportWrapper = new LambdaQueryWrapper<>();
        reportWrapper.in(ProjectPlanReport::getBaseCode, codes);
        reportWrapper.eq(ProjectPlanReport::getDelFlag, Common.delete_flag.OK);
        if (!StringUtils.isEmpty(status)) {
            reportWrapper.eq(ProjectPlanReport::getStatus, status);
        }
        List<ProjectPlanReport> list = super.list(reportWrapper);

        LambdaQueryWrapper<ConstructionPlanProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConstructionPlanProcess::getDelFlag,Common.commonality.ZERO)
                .in(ConstructionPlanProcess::getPlanCode,codes);
        List<ConstructionPlanProcess> planProcesses = planProcessMapper.selectList(queryWrapper);
        if (!StringUtils.isEmpty(status)) {
            List<String> collect = list.stream().map(x -> x.getBaseCode()).collect(Collectors.toList());
            List<ProjectPlan> collect1 = projectPlanList.stream().filter(x -> collect.contains(x.getCode())).collect(Collectors.toList());
            for (ProjectPlan projectPlan : collect1) {
                Optional<ProjectPlanReport> first = list.stream().filter(x -> x.getBaseCode().equals(projectPlan.getCode())).findFirst();
                if (first.isPresent()) {
                    projectPlan.setProjectPlanReport(first.get());
                }

                List<ConstructionPlanProcess> planProcessList = planProcesses.stream().filter(x -> x.getPlanCode().equals(projectPlan.getCode())).collect(Collectors.toList());
                if (!planProcessList.isEmpty()){
                    projectPlan.setPlanProcessList(planProcessList);
                }
            }
            return Result.OK(collect1);
        } else {
            for (ProjectPlan projectPlan : projectPlanList) {
                Optional<ProjectPlanReport> first = list.stream().filter(x -> x.getBaseCode().equals(projectPlan.getCode())).findFirst();
                if (first.isPresent()) {
                    projectPlan.setProjectPlanReport(first.get());
                }

                List<ConstructionPlanProcess> planProcessList = planProcesses.stream().filter(x -> x.getPlanCode().equals(projectPlan.getCode())).collect(Collectors.toList());
                if (!planProcessList.isEmpty()){
                    projectPlan.setPlanProcessList(planProcessList);
                }
            }
            return Result.OK(projectPlanList);
        }
    }

    @Override
    public Result<?> schedule() {
        LambdaQueryWrapper<ProjectPlan> planQuery = new LambdaQueryWrapper<>();
        planQuery.orderByDesc(ProjectPlan::getVersion);
        planQuery.last("LIMIT 1");
        /*查询最后一个版本*/
        ProjectPlan one = planMapper.selectOne(planQuery);
        if (StringUtils.isEmpty(one)) {
            return Result.OK();
        }
        LambdaQueryWrapper<ProjectPlan> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectPlan::getVersion, one.getVersion());
        queryWrapper.eq(ProjectPlan::getLayer,Common.commonality.THREE);
        List<ProjectPlan> projectPlans = planMapper.selectList(queryWrapper);
        int sum = projectPlans.stream().mapToInt(x -> x.getPlanDay()).sum();
//        Optional<ProjectPlan> max = projectPlans.stream().max(Comparator.comparing(ProjectPlan::getEndDate));
//        Optional<ProjectPlan> min = projectPlans.stream().min(Comparator.comparing(ProjectPlan::getStartDate));
//        long msNum = max.get().getEndDate().getTime() - min.get().getStartDate().getTime();
//        //相差多少天
//        long day = msNum / (24 * 60 * 60 * 1000);
        List<String> codes = projectPlans.stream().map(x -> x.getCode()).collect(Collectors.toList());
        LambdaQueryWrapper<ProjectPlanReport> reportWrapper = new LambdaQueryWrapper<>();
        reportWrapper.in(ProjectPlanReport::getBaseCode, codes);
        reportWrapper.eq(ProjectPlanReport::getStatus,Common.commonality.TWO);
        reportWrapper.eq(ProjectPlanReport::getDelFlag, Common.delete_flag.OK);
        List<ProjectPlanReport> list = super.list(reportWrapper);
        if (list.size() == 0) {
            return Result.OK(0);
        }

        int dayNum = 0;
        for (ProjectPlanReport projectPlanReport : list) {
            if (!StringUtils.isEmpty(projectPlanReport.getActualEndDate())) {
                Optional<ProjectPlan> first = projectPlans.stream().filter(x -> x.getCode().equals(projectPlanReport.getBaseCode()) && x.getLayer() == 3).findFirst();
                if (first.isPresent()){
                    ProjectPlan projectPlan = first.get();
                    dayNum += projectPlan.getPlanDay();
//                    long msNum1 = projectPlan.getEndDate().getTime() - projectPlan.getStartDate().getTime();
//                    long day1 = msNum1 / (24 * 60 * 60 * 1000);
//                    System.out.println(BigDecimal.valueOf(projectPlan.getPlanDay()).divide(BigDecimal.valueOf(sum), 4, RoundingMode.HALF_UP).doubleValue());
                }
            }
        }
        double weight = BigDecimal.valueOf(dayNum).divide(BigDecimal.valueOf(sum), 4, RoundingMode.HALF_UP).doubleValue();
//        Optional<ProjectPlanReport> max1 = list.stream().filter(x -> !StringUtils.isEmpty(x.getActualEndDate())).max(Comparator.comparing(ProjectPlanReport::getActualEndDate));
//        if (StringUtils.isEmpty(max1.get().getActualEndDate())) {
//            if (max1.get().getActualEndDate().getTime() - new Date().getTime() == 0) {
//
//            }
//        }
//        Optional<ProjectPlanReport> min1 = list.stream().min(Comparator.comparing(ProjectPlanReport::getActualStartDate));
//        long msNum1 = max1.get().getActualEndDate().getTime() - min1.get().getActualStartDate().getTime();
//        long day1 = msNum1 / (24 * 60 * 60 * 1000);
//        System.out.println(BigDecimal.valueOf(day1).divide(BigDecimal.valueOf(day), 2, RoundingMode.HALF_UP));
        return Result.OK(weight);
    }

    @Override
    public Result<?> update(ProjectPlanReport projectPlanReport) {
        super.updateById(projectPlanReport);
        LambdaQueryWrapper<ProjectPlan> planQuery = new LambdaQueryWrapper<>();
        planQuery.eq(ProjectPlan::getCode, projectPlanReport.getBaseCode());
        planQuery.orderByDesc(ProjectPlan::getVersion);
        planQuery.last("LIMIT 1");
        /*查询最后一个版本*/
        ProjectPlan one = planMapper.selectOne(planQuery);
//        LambdaQueryWrapper<ProjectPlan> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(ProjectPlan::getCode, projectPlanReport.getBaseCode());
//        queryWrapper.eq(ProjectPlan::getVersion, one.getVersion());
//        ProjectPlan projectPlan = planMapper.selectOne(queryWrapper);
        this.updateByBase(one);
        return Result.OK("填报成功");
    }

    @Override
    public Result<?> saveByCode(ProjectPlanReport projectPlanReport) {
        super.save(projectPlanReport);
        LambdaQueryWrapper<ProjectPlan> planQuery = new LambdaQueryWrapper<>();
        planQuery.orderByDesc(ProjectPlan::getVersion);
        planQuery.last("LIMIT 1");
        /*查询最后一个版本*/
        ProjectPlan one = planMapper.selectOne(planQuery);
        LambdaQueryWrapper<ProjectPlan> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectPlan::getCode, projectPlanReport.getBaseCode());
        queryWrapper.eq(ProjectPlan::getVersion, one.getVersion());
        ProjectPlan projectPlan = planMapper.selectOne(queryWrapper);
        this.updateByBase(projectPlan);
        return Result.OK();
    }

    @Transactional
    @Override
    public void updateByBase(ProjectPlan projectPlan) {
        if (projectPlan.getLayer() == 1) {
            return;
        }
        if (!StringUtils.isEmpty(projectPlan)) {
            String pcode = projectPlan.getCode().substring(0, projectPlan.getCode().lastIndexOf("."));
            LambdaQueryWrapper<ProjectPlan> queryWrapper1 = new LambdaQueryWrapper<>();
            queryWrapper1.eq(ProjectPlan::getPid, projectPlan.getPid());
//            queryWrapper1.eq(ProjectPlan::getLayer, projectPlan.getLayer());
//            queryWrapper1.eq(ProjectPlan::getVersion, projectPlan.getVersion());
            List<ProjectPlan> projectPlans = planMapper.selectList(queryWrapper1);
            ProjectPlan projectPlan1 = planMapper.selectById(projectPlan.getPid());
            List<String> collect = projectPlans.stream().map(x -> x.getCode()).collect(Collectors.toList());
            LambdaQueryWrapper<ProjectPlanReport> reportLambdaQueryWrapper = new LambdaQueryWrapper<>();
            reportLambdaQueryWrapper.in(ProjectPlanReport::getBaseCode, collect);
            List<ProjectPlanReport> projectPlanReports = baseMapper.selectList(reportLambdaQueryWrapper);
            List<ProjectPlanReport> collect1 = projectPlanReports.stream().filter(x -> !StringUtils.isEmpty(x.getActualEndDate())).collect(Collectors.toList());
            Optional<ProjectPlanReport> max = projectPlanReports.stream().filter(x -> !StringUtils.isEmpty(x.getActualEndDate())).max(Comparator.comparing(ProjectPlanReport::getActualEndDate));
            Optional<ProjectPlanReport> min1 = projectPlanReports.stream().filter(x->!StringUtils.isEmpty(x.getActualStartDate())).min(Comparator.comparing(ProjectPlanReport::getActualStartDate));
            if (collect1.size() == collect.size()) {
                long msNum1 = max.get().getActualEndDate().getTime() - min1.get().getActualStartDate().getTime();
                Long day1 = msNum1 / (24 * 60 * 60 * 1000) + 1;
                baseMapper.updateByCode(pcode, 2, max.get().getActualEndDate(), min1.get().getActualStartDate(), Integer.parseInt(day1.toString()));
            } else if (collect1.size() < collect.size()) {
                int num = baseMapper.updateByCode(pcode, 1, null, min1.get().getActualStartDate(), null);
                if (num == 0) { //第一次填报
                    ProjectPlanReport projectPlanReport = new ProjectPlanReport();
                    projectPlanReport.setBaseCode(pcode)
                            .setActualStartDate(min1.get().getActualStartDate())
                            .setStatus(Common.commonality.ONE);
                    super.save(projectPlanReport);
                }
            }
            if (!StringUtils.isEmpty(projectPlan1)) {
                this.updateByBase(projectPlan1);
            }
        }
    }

}
