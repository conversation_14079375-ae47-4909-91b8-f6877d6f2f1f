package com.jinghe.breeze.modules.sailingcheck.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.*;
/**
 * @Description: user_sailing_record
 * @Author: jeecg-boot
 * @Date:   2024-06-18
 * @Version: V1.0
 */
@Data
@TableName("user_sailing_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="user_sailing_record对象", description="user_sailing_record")
public class UserSailingRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**标识*/
    @TableId(type = IdType.ASSIGN_ID)
    @NotNull(message = "标识不能为空!")

    @ApiModelProperty(value = "标识")
    private java.lang.String id;
    /**创建人*/

    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
    /**更新人*/

    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**更新时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
    /**delFlag*/
    @Excel(name = "delFlag", width = 15)
    @NotNull(message = "delFlag不能为空!")

    @ApiModelProperty(value = "delFlag")
    private java.lang.Integer delFlag;
    /**组织机构编码*/

    @ApiModelProperty(value = "组织机构编码")
    private java.lang.String sysOrgCode;
    /**项目id*/
    @Excel(name = "项目id", width = 15)

    @ApiModelProperty(value = "项目id")
    private java.lang.String projectId;
    /**关联人员id*/
    @Excel(name = "关联人员id", width = 15)
    @ApiModelProperty(value = "关联人员id")
    private java.lang.String userId;

    /**船舶出航记录id*/
    @Excel(name = "船舶出航记录id", width = 15)
    @ApiModelProperty(value = "船舶出航记录id")
    private java.lang.String baseId;

    /**船舶出航记录id*/
    @Excel(name = "返回船舶出航记录id", width = 15)
    @ApiModelProperty(value = "返回船舶出航记录id")
    private java.lang.String backBaseId;

    /**状态；1出航，2已返航*/
    @Excel(name = "状态；1出航，2已返航", width = 15)
    @NotNull(message = "状态；1出航，2已返航不能为空!")
    @ApiModelProperty(value = "状态；1出航，2已返航")
    private java.lang.Integer status;

    /**返航时间*/
    @Excel(name = "返航时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "返航时间")
    private java.util.Date backTime;

    @Excel(name = "用户名", width = 15)
    @ApiModelProperty(value = "用户名")
    private String userName;

    @Excel(name = "用户姓名", width = 15)
    @ApiModelProperty(value = "用户姓名")
    private String realName;

    @Excel(name = "单位ID", width = 15)
    @ApiModelProperty(value = "单位ID")
    private String departmentId;

    @Excel(name = "单位名称", width = 15)
    @ApiModelProperty(value = "单位名称")
    private String departmentName;

    @Excel(name = "船舶名称", width = 15)
    @ApiModelProperty(value = "船舶名称")
    private String shipName;

    @TableField(exist = false)
    private String phone;

    private String result;

    private String remark;

    private String disposeUserName;
}
