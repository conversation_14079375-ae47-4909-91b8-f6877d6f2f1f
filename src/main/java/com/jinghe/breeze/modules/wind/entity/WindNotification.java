package com.jinghe.breeze.modules.wind.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: wind_notification
 * @Author: jeecg-boot
 * @Date:   2024-05-22
 * @Version: V1.0
 */
@Data
@TableName("wind_notification")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="wind_notification对象", description="wind_notification")
public class WindNotification implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/

    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/

    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;

	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/

    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;

	/**删除标识*/
	@Excel(name = "删除标识", width = 15)
    @ApiModelProperty(value = "删除标识")
    private java.lang.Integer delFlag;


	/**通知编号*/
	@Excel(name = "通知编号", width = 15)
    @ApiModelProperty(value = "通知编号")
    private java.lang.String code;

	/**通知名称*/
	@Excel(name = "通知名称", width = 15)
    @ApiModelProperty(value = "通知名称")
    private java.lang.String name;

	/**计划开始日期*/
	@Excel(name = "计划开始日期", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "计划开始日期")
    private java.util.Date planStartDate;

	/**计划结束日期*/
	@Excel(name = "计划结束日期", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "计划结束日期")
    private java.util.Date planEndDate;

	/**实际结束日期*/
	@Excel(name = "实际结束日期", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "实际结束日期")
    private java.util.Date actualEndDate;

	/**截止上报日期*/
	@Excel(name = "截止上报日期", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "截止上报日期")
    private java.util.Date deadline;

	@Excel(name = "联系人信息", width = 15)
    @ApiModelProperty(value = "联系人信息")
    private java.lang.String contactInfo;


	@Excel(name = "通知人员信息", width = 15)
    @ApiModelProperty(value = "通知人员信息")
    private java.lang.String receiverInfo;

	/**避风内容*/
	@Excel(name = "避风内容", width = 15)
    @ApiModelProperty(value = "避风内容")
    @TableField(updateStrategy = FieldStrategy.IGNORED, insertStrategy = FieldStrategy.IGNORED) //忽略空值检查
    private java.lang.String content;

    /**
     * 通知状态 0草稿，1已发布，2已结束
     *
     */
    @Excel(name = "通知状态", width = 15)
    @ApiModelProperty(value = "通知状态")
    private java.lang.Integer state;

	/**附件支持pdf,图片*/
	@Excel(name = "附件支持pdf,图片", width = 15)
    @ApiModelProperty(value = "附件支持pdf,图片")
    @TableField(updateStrategy = FieldStrategy.IGNORED, insertStrategy = FieldStrategy.IGNORED) //忽略空值检查
    private java.lang.String file;
}
