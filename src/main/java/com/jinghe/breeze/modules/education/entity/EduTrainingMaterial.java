package com.jinghe.breeze.modules.education.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.jeecg.common.aspect.annotation.Dict;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.*;
/**
 * @Description: 培训资料
 * @Author: jeecg-boot
 * @Date:   2025-06-23
 * @Version: V1.0
 */
@Data
@TableName("edu_training_material")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="edu_training_material对象", description="培训资料")
public class EduTrainingMaterial implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)

    @ApiModelProperty(value = "主键")
    private String id;
	/**创建人*/

    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/

    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**所属部门*/

    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
	/**资料名称*/
	@Excel(name = "资料名称", width = 15)
    @NotNull(message = "资料名称不能为空!")

    @ApiModelProperty(value = "资料名称")
    private String name;
	/**类型字典*/
	@Excel(name = "类型字典", width = 15)

    @Dict(dicCode = "training_material_type")
    @ApiModelProperty(value = "类型字典")
    private String type;
	/**封面图片*/
	@Excel(name = "封面图片", width = 15)

    @ApiModelProperty(value = "封面图片")
    private String cover;
	/**资料附件*/
	@Excel(name = "资料附件", width = 15)

    @ApiModelProperty(value = "资料附件")
    private String file;
	/**上传单位*/
	@Excel(name = "上传单位", width = 15)

    @Dict(dictTable = "project_unit", dicText = "name", dicCode = "id")
    @ApiModelProperty(value = "上传单位")
    private String unit;

    @ApiModelProperty(value = "上传人")
    private String username;
}
