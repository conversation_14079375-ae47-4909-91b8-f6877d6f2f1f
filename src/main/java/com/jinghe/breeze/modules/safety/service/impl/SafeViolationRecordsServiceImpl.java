package com.jinghe.breeze.modules.safety.service.impl;

import com.jinghe.breeze.modules.safety.entity.SafeViolationRecords;
import com.jinghe.breeze.modules.safety.mapper.SafeViolationRecordsMapper;
import com.jinghe.breeze.modules.safety.service.ISafeViolationRecordsService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: safe_violation_records
 * @Author: jeecg-boot
 * @Date:   2025-06-22
 * @Version: V1.0
 */
@Service
public class SafeViolationRecordsServiceImpl extends ServiceImpl<SafeViolationRecordsMapper, SafeViolationRecords> implements ISafeViolationRecordsService {

}
