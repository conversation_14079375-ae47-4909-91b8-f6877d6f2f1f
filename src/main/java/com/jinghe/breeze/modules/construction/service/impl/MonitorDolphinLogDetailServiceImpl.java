package com.jinghe.breeze.modules.construction.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.common.constants.enums.Direction;
import com.jinghe.breeze.modules.construction.entity.PolarPlotRequestData;
import com.jinghe.breeze.modules.construction.entity.MonitorDolphinLogDetail;
import com.jinghe.breeze.modules.construction.mapper.MonitorDolphinLogDetailMapper;
import com.jinghe.breeze.modules.construction.service.IMonitorDolphinLogDetailService;
import com.jinghe.breeze.modules.construction.service.IMonitorDolphinLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 观豚日志列表
 * @Author: jeecg-boot
 * @Date: 2024-05-11
 * @Version: V1.0
 */
@Service
public class MonitorDolphinLogDetailServiceImpl extends ServiceImpl<MonitorDolphinLogDetailMapper, MonitorDolphinLogDetail> implements IMonitorDolphinLogDetailService {

    @Autowired
    private IMonitorDolphinLogService monitorDolphinLogService;

    private String getDirection(Double x, Double y, String position) {
        if (position == null) {
            return "-1";
        }
        //移除position中的[]空格
        position = position.replace("[", "").replace("]", "").replace(" ", "");
        String[] pos = position.split(",");
        if (pos.length != 2) {
            return "-1";
        }
        try {
            Double x1 = Double.parseDouble(pos[0]);
            Double y1 = Double.parseDouble(pos[1]);
            double angle = Math.toDegrees(Math.atan2(y1 - y, x1 - x));
            if (angle < 0) {
                angle += 360;
            }
            if (angle >= 337.5 || angle < 22.5) {
                return Direction.N.getCode();
            } else if (angle >= 22.5 && angle < 67.5) {
                return Direction.NE.getCode();
            } else if (angle >= 67.5 && angle < 112.5) {
                return Direction.E.getCode();
            } else if (angle >= 112.5 && angle < 157.5) {
                return Direction.SE.getCode();
            } else if (angle >= 157.5 && angle < 202.5) {
                return Direction.S.getCode();
            } else if (angle >= 202.5 && angle < 247.5) {
                return Direction.SW.getCode();
            } else if (angle >= 247.5 && angle < 292.5) {
                return Direction.W.getCode();
            } else {
                return Direction.NW.getCode();
            }
        } catch (NumberFormatException e) {
            return "-1";
        }
    }

    public Map<String, Integer> getPolarData(PolarPlotRequestData polarData) {


        Double lng = polarData.getLng().doubleValue();
        Double latitude = polarData.getLat().doubleValue();

        Map<String, Integer> retMap = new HashMap<>();
        for (int i = 0; i < 8; i++) {
            retMap.put(String.valueOf(i), 0);
        }

        List<MonitorDolphinLogDetail> detailList = baseMapper.selectList(new LambdaQueryWrapper<MonitorDolphinLogDetail>()
                .eq(MonitorDolphinLogDetail::getDelFlag, Common.delete_flag.OK));

        for (MonitorDolphinLogDetail detail : detailList) { //对指定的天内的每个记录
            String position = detail.getPosition();
            String direction = getDirection(lng, latitude, position); //0-7  代表东南西北顺时针8个方向 ,-1表示没有
            switch (direction) {
                case "0":
                case "1":
                case "2":
                case "3":
                case "4":
                case "5":
                case "6":
                case "7":
                    retMap.put(direction, retMap.get(direction) + 1);
                    break;
                case "-1":
                default:
                    break;
            }
        }
        return retMap;
    }


}
