package com.jinghe.breeze.modules.ship.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.common.constants.enums.ProjectFensEnum;
import com.jinghe.breeze.common.utils.MapPoint;
import com.jinghe.breeze.common.utils.MapWktUtil;
import com.jinghe.breeze.modules.data.entity.DataShipInfo;
import com.jinghe.breeze.modules.data.mapper.DataShipInfoMapper;
import com.jinghe.breeze.modules.project.entity.ProjectFence;
import com.jinghe.breeze.modules.project.entity.ProjectInfo;
import com.jinghe.breeze.modules.project.mapper.ProjectFenceMapper;
import com.jinghe.breeze.modules.project.service.IProjectInfoService;
import com.jinghe.breeze.modules.ship.entity.ShipAlarm;
import com.jinghe.breeze.modules.ship.mapper.ShipAlarmMapper;
import com.jinghe.breeze.modules.ship.service.IShipAlarmService;
import com.jinghe.breeze.modules.ship.service.IShipApiService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.oConvertUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: ship_alarm
 * @Author: jeecg-boot
 * @Date: 2024-04-23
 * @Version: V1.0
 */
@Service
public class ShipAlarmServiceImpl extends ServiceImpl<ShipAlarmMapper, ShipAlarm> implements IShipAlarmService {
    @Autowired
    private IShipApiService iShipApiService;
    @Autowired
    private ProjectFenceMapper fenceMapper;
    @Autowired
    private DataShipInfoMapper dataShipInfoMapper;
    @Resource
    private IProjectInfoService iProjectInfoService;

    @Override
    public Result<?> queryStatistics(ShipAlarm shipAlarm) {
        LambdaQueryWrapper<ShipAlarm> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShipAlarm::getAlarmDay, shipAlarm.getAlarmDay());
        List<ShipAlarm> list = super.list(queryWrapper);
        if (list.size() == 0) {
            return Result.OK();
        }
        Map<String, List<ShipAlarm>> listMap = list.stream().collect(Collectors.groupingBy(
                score -> oConvertUtils.isNotEmpty(score.getShipTypeName()) ? score.getShipTypeName() : ""
        ));
        Map<String, List<ShipAlarm>> collect = list.stream().collect(Collectors.groupingBy(
                score -> score.getBelong()
        ));
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> returnMap = new HashMap<>();
        for (String s : listMap.keySet()) {
            List<ShipAlarm> shipAlarms = listMap.get(s);
            map.put(s, shipAlarms.size());
        }
        returnMap.put("shipType", map);

        for (String s : collect.keySet()) {
            returnMap.put(s, collect.get(s).size());
        }
        return Result.OK(returnMap);
    }

    @Override
    public Result<?> queryAlarm() {
        Map<String, Object> objectMap = new HashMap();

        LambdaQueryWrapper<ProjectInfo> projectInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        projectInfoLambdaQueryWrapper.eq(ProjectInfo::getDelFlag, Common.delete_flag.OK).last("limit 1");
        ProjectInfo one = iProjectInfoService.getOne(projectInfoLambdaQueryWrapper);
        if (StringUtils.isEmpty(one) || StringUtils.isEmpty(one.getSiteRange())) {
            return Result.OK();
        }

        LambdaQueryWrapper<ProjectFence> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectFence::getDelFlag, Common.delete_flag.OK)
                .eq(ProjectFence::getFenceType, ProjectFensEnum.YJ.getCode())
                .last("limit 1");
        ProjectFence projectFence = fenceMapper.selectOne(queryWrapper);
        if (StringUtils.isEmpty(projectFence) || StringUtils.isEmpty(projectFence.getFenceRadius())) {
            return Result.error("请设置围栏信息");
        }
        objectMap.put("fenceRadius", projectFence.getFenceRadius());

        String points = MapWktUtil.points(one.getSiteRange());
        Result<?> result = iShipApiService.GetAreaShip(points);
        if (!result.isSuccess()) {
            return Result.OK(objectMap);
        }
        List data = JSONArray.parseArray(result.getResult().toString());
        if (StringUtils.isEmpty(data) || data.size() == 0) {
            return Result.OK(objectMap);
        }

        List<DataShipInfo> dataShipInfos = dataShipInfoMapper.selectList(new LambdaQueryWrapper<>());
        List<String> collect = dataShipInfos.stream().map(x -> x.getMmsi()).collect(Collectors.toList());

        List<JSONObject> inShip = new ArrayList<>();
        List<JSONObject> outShip = new ArrayList<>();
        List<JSONObject> alarmShip = new ArrayList<>();
        for (Object datum : data) {
            JSONObject jsonObject = JSON.parseObject(datum.toString());
            String mmsi = jsonObject.getString("mmsi");
            boolean pointInPolygon = MapWktUtil.isPointInPolygon(new MapPoint(Double.parseDouble(jsonObject.getString("lon")) / 1000000,
                            Double.parseDouble(jsonObject.getString("lat")) / 1000000),
                    projectFence.getFenceRadius());
            if (collect.contains(mmsi)) {
                inShip.add(jsonObject);
            } else {
                if (pointInPolygon) {
                    alarmShip.add(jsonObject);
                } else {
                    outShip.add(jsonObject);
                }
            }
        }

        objectMap.put("inShip", inShip);
        objectMap.put("alarmShip", alarmShip);
        objectMap.put("outShip", outShip);
        return Result.OK(objectMap);
    }
}
