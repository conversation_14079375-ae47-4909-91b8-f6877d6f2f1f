package com.jinghe.breeze.modules.settlement.entity;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.*;

/**
 * @Description: contract_info_detail
 * @Author: jeecg-boot
 * @Date: 2024-05-31
 * @Version: V1.0
 */
@Data
@TableName("contract_info_detail")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "contract_info_detail对象", description = "contract_info_detail")
public class ContractInfoDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 标识
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "标识")
    private String id;
    /**
     * 创建人
     */

    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新人
     */

    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    /**
     * delFlag
     */
    @Excel(name = "delFlag", width = 15)
    @ApiModelProperty(value = "delFlag")
    private Integer delFlag;
    /**
     * 组织机构编码
     */

    @ApiModelProperty(value = "组织机构编码")
    private String sysOrgCode;
    /**
     * 项目id
     */
    @Excel(name = "项目id", width = 15)

    @ApiModelProperty(value = "项目id")
    private String projectId;
    /**
     * 合同id
     */
    @Excel(name = "合同id", width = 15)

    @ApiModelProperty(value = "合同id")
    private String contractId;

    @Excel(name = "pid", width = 15)
    @ApiModelProperty(value = "pid")
    private String pid;


    /**
     * 合同编码
     */
    @Excel(name = "清单编码", width = 15)
    @NotNull(message = "清单编码不能为空!")

    @ApiModelProperty(value = "清单编码")
    private String code;
    /**
     * 合同名称
     */
    @Excel(name = "清单名称", width = 15)
    @NotNull(message = "清单名称不能为空!")
    @ApiModelProperty(value = "清单名称")
    private String name;
    /**
     * 合同单价
     */
    @Excel(name = "合同单价", width = 15)

    @ApiModelProperty(value = "合同单价")
    private BigDecimal unitMoney;
    /**
     * 合同总价
     */
    @Excel(name = "合同总价", width = 15)

    @ApiModelProperty(value = "合同总价")
    private BigDecimal totalMoney;
    /**
     * 计量单位
     */
    @Excel(name = "计量单位", width = 15)

    @ApiModelProperty(value = "计量单位")
    private String unit;
    /**
     * 数量
     */
    @Excel(name = "数量", width = 15)

    @ApiModelProperty(value = "数量")
    private Integer amount;
    /**
     * 层级
     */
    @Excel(name = "层级", width = 15)
    @NotNull(message = "层级不能为空!")

    @ApiModelProperty(value = "是否有子级")
    private Integer hasChild;

    @ApiModelProperty(value = "层级")
    private Integer layer;

    @TableField(exist = false)
    private List<ContractInfoDetail> children;

    @TableField(exist = false)
    @ApiModelProperty(value = "本次金额")
    private BigDecimal currentSettlement;

    @TableField(exist = false)
    @ApiModelProperty(value = "已结算金额")
    private BigDecimal settledMoney;

    @TableField(exist = false)
    @ApiModelProperty(value = "结算id")
    private String settlementId;

}
