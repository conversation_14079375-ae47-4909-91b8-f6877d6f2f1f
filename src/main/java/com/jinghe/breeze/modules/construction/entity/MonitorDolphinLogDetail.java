package com.jinghe.breeze.modules.construction.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 观豚日志列表
 * @Author: jeecg-boot
 * @Date: 2024-05-11
 * @Version: V1.0
 */
@Data
@TableName("monitor_dolphin_log_detail")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "watch_dolphin_log_list对象", description = "观豚日志列表")
public class MonitorDolphinLogDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
    /**
     * 出现时间
     */
    @Excel(name = "出现时间戳", width = 15)
//	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
//    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "出现时间戳")
//    private DateTime startDate;
    private Integer startDate;


    @Excel(name = "结束时间戳", width = 15)
//    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "结束时间戳")
//    private DateTime endDate;
    private Integer endDate;
    /**
     * 天气状态
     */
    @Excel(name = "天气状态", width = 15)
    @ApiModelProperty(value = "天气状态")
    @TableField(updateStrategy = FieldStrategy.IGNORED, insertStrategy = FieldStrategy.IGNORED) //忽略空值检查
    private java.lang.String weather;
    /**
     * 白海豚位置
     */
    @Excel(name = "白海豚位置", width = 15)
    @ApiModelProperty(value = "白海豚位置")
    private java.lang.String position;
    /**
     * 是否影响施工
     */
    @Excel(name = "是否影响施工", width = 15)
    @ApiModelProperty(value = "是否影响施工")
    private java.lang.String isConstructionAffected;
    /**
     * 施工状态及采取措施
     */
    @Excel(name = "施工状态及采取措施	", width = 15)
    @ApiModelProperty(value = "施工状态及采取措施	")
    private java.lang.String statusAction;
    /**
     * 图片上传
     */
    @Excel(name = "图片上传", width = 15)
    @ApiModelProperty(value = "图片上传")
    @TableField(updateStrategy = FieldStrategy.IGNORED, insertStrategy = FieldStrategy.IGNORED) //忽略空值检查
    private java.lang.String pictures;

    @Excel(name = "主表id", width = 15)
    @ApiModelProperty(value = "主表id")
    private java.lang.String watchId;

    @Excel(name = "删除标识", width = 15)
    @ApiModelProperty(value = "删除标识")
    private java.lang.Integer delFlag;
}
