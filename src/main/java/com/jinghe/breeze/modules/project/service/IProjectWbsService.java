package com.jinghe.breeze.modules.project.service;

import com.jinghe.breeze.modules.project.entity.ProjectWbs;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.exception.JeecgBootException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.util.List;

/**
 * @Description: project_wbs
 * @Author: jeecg-boot
 * @Date: 2024-04-17
 * @Version: V1.0
 */
public interface IProjectWbsService extends IService<ProjectWbs> {

	/** 根节点父ID的值 */
	public static final String ROOT_PID_VALUE = "0";

	/** 树节点有子节点状态值 */
	public static final String HASCHILD = "1";

	/** 树节点无子节点状态值 */
	public static final String NOCHILD = "0";


	/** 修改节点 */
	void editProjectWbs(ProjectWbs projectWbs) throws JeecgBootException;

	/** 删除节点 */
	void deleteProjectWbs(String id) throws JeecgBootException;

	/** 查询所有数据，无分页 */
	List<ProjectWbs> queryTreeListNoPage(QueryWrapper<ProjectWbs> queryWrapper);

	void setCanMoveUpDown(List<ProjectWbs> dataList);

	public void addProjectWbs(ProjectWbs projectWbs);

	public void moveProjectWbs(ProjectWbs wbs, boolean isUp);

}
