package com.jinghe.breeze.modules.safety.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.*;
/**
 * @Description: 隐患整改
 * @Author: jeecg-boot
 * @Date:   2025-06-24
 * @Version: V1.0
 */
@Data
@TableName("safe_hidden_danger_records")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="safe_hidden_danger_records对象", description="隐患整改")
public class SafeHiddenDangerRecords implements Serializable {
    private static final long serialVersionUID = 1L;

	/**标识*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "标识")
    private String id;
	/**创建人*/

    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

	/**delFlag*/
	@Excel(name = "delFlag", width = 15)
    @ApiModelProperty(value = "delFlag")
    private Integer delFlag;
	/**组织机构编码*/
    @ApiModelProperty(value = "组织机构编码")
    private String sysOrgCode;
	/**隐患名称*/
	@Excel(name = "隐患名称", width = 15)
    @ApiModelProperty(value = "隐患名称")
    private String name;
	/**隐患名称*/
	@Excel(name = "隐患名称", width = 15)
    @ApiModelProperty(value = "隐患名称")
    private String code;
	/**整改部位*/
	@Excel(name = "整改部位", width = 15)
    @ApiModelProperty(value = "整改部位")
    private String rectificationPart;
	/**检查工区*/
	@Excel(name = "检查工区", width = 15)
    @ApiModelProperty(value = "检查工区")
    private String workArea;
	/**隐患来源*/
	@Excel(name = "隐患来源", width = 15, dicCode = "danger_source")
	@Dict(dicCode = "danger_source")
    @ApiModelProperty(value = "隐患来源")
    private String dangerSource;
	/**隐患级别*/
	@Excel(name = "隐患级别", width = 15, dicCode = "danger_level")
	@Dict(dicCode = "danger_level")
    @ApiModelProperty(value = "隐患级别")
    private String dangerLevel;
	/**整改期限*/
	@Excel(name = "整改期限", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "整改期限")
    private Date rectificationDeadline;
	/**整改内容*/
	@Excel(name = "整改内容", width = 15)
    @ApiModelProperty(value = "整改内容")
    private String rectificationContent;
	/**整改建议*/
	@Excel(name = "整改建议", width = 15)
    @ApiModelProperty(value = "整改建议")
    private String rectificationProposal;
	/**隐患图片*/
	@Excel(name = "隐患图片", width = 15)
    @ApiModelProperty(value = "隐患图片")
    private String dangerFiles;
	/**逾期状态 逾期 yq  未逾期 wyq*/
	@Excel(name = "逾期状态", width = 15, dicCode = "overdue_status")
	@Dict(dicCode = "overdue_status")
    @ApiModelProperty(value = "逾期状态")
    private String overdueStatus;
	/**整改措施*/
	@Excel(name = "整改措施", width = 15)
    @ApiModelProperty(value = "整改措施")
    private String rectificationMeasure;
	/**整改情况*/
	@Excel(name = "整改情况", width = 15)
    @ApiModelProperty(value = "整改情况")
    private String rectificationSituation;
	/**整改完成时间*/
	@Excel(name = "整改完成时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "整改完成时间")
    private Date rectificationCompleteDate;
	/**整改状态*/
	@Excel(name = "整改状态", width = 15, dicCode = "rectification_status")
	@Dict(dicCode = "rectification_status")
    @ApiModelProperty(value = "整改状态")
    private String rectificationStatus;
	/**审批状态*/
	@Excel(name = "审批状态", width = 15)
    @ApiModelProperty(value = "审批状态")
    private String bpmStatus;
	/**上报人*/
	@Excel(name = "上报人", width = 15)
    @ApiModelProperty(value = "上报人")
    private String reporter;
	/**上报人名称*/
	@Excel(name = "上报人名称", width = 15)
    @ApiModelProperty(value = "上报人名称")
    private String reporterName;
	/**上报人电话*/
	@Excel(name = "上报人电话", width = 15)
    @ApiModelProperty(value = "上报人电话")
    private String reporterPhone;
	/**上报人部门*/
	@Excel(name = "上报人部门", width = 15)
    @ApiModelProperty(value = "上报人部门")
    private String reporterDepart;
	/**上报日期*/
	@Excel(name = "上报日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "上报日期")
    private Date reportDate;
	/**整改图片*/
	@Excel(name = "整改图片", width = 15)
    @ApiModelProperty(value = "整改图片")
    private String reportFiles;
	/**隐患整改人*/
	@Excel(name = "隐患整改人", width = 15, dictTable = "person_info", dicText = "name", dicCode = "id")
	@Dict(dictTable = "person_info", dicText = "name", dicCode = "id")
    @ApiModelProperty(value = "隐患整改人")
    private String rectifier;

    @TableField(exist = false)
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "违规时间")
    private Date startDate;

    @TableField(exist = false)
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "违规时间")
    private Date endDate;

    @ApiModelProperty(value = "主表id")
    private String recordId;
}
