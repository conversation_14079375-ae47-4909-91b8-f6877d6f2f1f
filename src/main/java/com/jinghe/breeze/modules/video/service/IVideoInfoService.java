package com.jinghe.breeze.modules.video.service;

import com.jinghe.breeze.modules.video.entity.VideoInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghe.breeze.modules.video.entity.vo.VideoFolderNode;
import org.jeecg.common.api.vo.Result;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Description: video_info
 * @Author: jeecg-boot
 * @Date: 2024-06-13
 * @Version: V1.0
 */
public interface IVideoInfoService extends IService<VideoInfo> {

    List<VideoFolderNode> getTreeData();

    Result<?> queryPageList(VideoInfo videoInfo, Integer pageNo, Integer pageSize, HttpServletRequest req);

//    void addMedia(List<String> files, String folderId);
//
//    void addMedia(String files, String folderId);
//
//    void deleteByFiles(String files, String folderId);
//
//    void deleteByFiles(List<String> files, String folderId);

    void updateVideoInfo(List<String> oldFileStringList, List<String> newFileStringList, String folderId);

    Result<?> add(List<VideoInfo> videoInfoList);
}