package com.jinghe.breeze.modules.monitor.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinghe.breeze.modules.device.entity.DeviceInfo;
import com.jinghe.breeze.modules.device.service.IDeviceInfoService;
import com.jinghe.breeze.modules.monitor.entity.AICaptureRecord;
import com.jinghe.breeze.modules.monitor.service.IAICaptureRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description: AI抓怕记录
 * @Author: jeecg-boot
 * @Date: 2024-08-07
 * @Version: V1.0
 */
@Api(tags = "AI抓拍记录")
@RestController
@RequestMapping("/monitor/AICapture")
@Slf4j
public class AICaptureRecordController extends JeecgController<AICaptureRecord, IAICaptureRecordService> {
    @Autowired
    private IAICaptureRecordService iService;
    @Autowired
    private IDeviceInfoService deviceInfoService;

    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");


    /**
     * 分页列表查询
     *
     * @param entity
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "AI抓拍记录-分页列表查询")
    @ApiOperation(value = "AI抓拍记录-分页列表查询", notes = "AI抓拍记录-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(AICaptureRecord entity,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   @RequestParam(name = "startDate", required = false) String startDate,
                                   @RequestParam(name = "endDate", required = false) String endDate,
                                   HttpServletRequest req) {
        String deviceId = null;
        if (entity.getDeviceId() != null) {
            deviceId = entity.getDeviceId();
            entity.setDeviceId(null);
        }
        QueryWrapper<AICaptureRecord> queryWrapper = QueryGenerator.initQueryWrapper(entity, req.getParameterMap());
        if (deviceId != null) {
            queryWrapper.eq("device_id", deviceId);
        }
        if (!StringUtils.isEmpty(startDate) && !StringUtils.isEmpty(endDate)) {
            try {
                Date start = sdf.parse(startDate + " 00:00:00");
                Date end = sdf.parse(endDate + " 23:59:59");
                queryWrapper.ge("capture_time", start);
                queryWrapper.le("capture_time", end);
            } catch (Exception e) {
                log.warn("illegal date string");
            }
        }
        Page<AICaptureRecord> page = new Page<>(pageNo, pageSize);
        IPage<AICaptureRecord> pageList = iService.page(page, queryWrapper);
        return Result.OK(pageList);
    }


    /**
     * 无分页 月度查询
     *
     * @param entity
     * @param req
     * @return
     */
    @AutoLog(value = "AI抓拍记录-列表查询")
    @ApiOperation(value = "AI抓拍记录-列表查询", notes = "AI抓拍记录-列表查询")
    @GetMapping(value = "/queryList")
    public Result<?> queryList(AICaptureRecord entity,
                               @RequestParam(name = "startDate", required = false) String startDate,
                               @RequestParam(name = "endDate", required = false) String endDate,
                               HttpServletRequest req) {
        String deviceId = null;
        if (entity.getDeviceId() != null) {
            deviceId = entity.getDeviceId();
            entity.setDeviceId(null);
        }
        QueryWrapper<AICaptureRecord> queryWrapper = QueryGenerator.initQueryWrapper(entity, req.getParameterMap());
        queryWrapper.orderByDesc("capture_time");
        if (deviceId != null) {
            queryWrapper.eq("device_id", deviceId);
        }
        if (!StringUtils.isEmpty(startDate) && !StringUtils.isEmpty(endDate)) {
            try {
                Date start = sdf.parse(startDate + " 00:00:00");
                Date end = sdf.parse(endDate + " 23:59:59");
                queryWrapper.ge("capture_time", start);
                queryWrapper.le("capture_time", end);
            } catch (Exception e) {
                log.warn("illegal date string");
            }
        }
        List<AICaptureRecord> aICaptureRecord = iService.list(queryWrapper);
        if (ObjectUtil.isNotEmpty(aICaptureRecord)) {
//            获取所有的设备信息
            List<DeviceInfo> list = deviceInfoService.list();
            for (AICaptureRecord a : aICaptureRecord) {
                for (DeviceInfo d : list) {
                    if (a.getDeviceId().equals(d.getId())) {
                        a.setDeviceName(d.getName());
                        break;
                    }
                }
            }
        }
        return Result.OK(aICaptureRecord);
    }

    /**
     * 添加
     *
     * @param entity
     * @return
     */
    @AutoLog(value = "AI抓拍记录-添加")
    @ApiOperation(value = "AI抓拍记录-添加", notes = "AI抓拍记录-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody AICaptureRecord entity) {
        iService.save(entity);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param entity
     * @return
     */
    @AutoLog(value = "AI抓拍记录-编辑")
    @ApiOperation(value = "AI抓拍记录-编辑", notes = "AI抓拍记录-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody AICaptureRecord entity) {
        iService.updateById(entity);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "AI抓拍记录-通过id删除")
    @ApiOperation(value = "AI抓拍记录-通过id删除", notes = "AI抓拍记录-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        iService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "AI抓拍记录-批量删除")
    @ApiOperation(value = "AI抓拍记录-批量删除", notes = "AI抓拍记录-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        iService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "AI抓拍记录-通过id查询")
    @ApiOperation(value = "AI抓拍记录-通过id查询", notes = "AI抓拍记录-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        AICaptureRecord record = iService.getById(id);
        if (record == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(record);
    }

    /**
     * 月度统计
     *
     * @return
     */
    @AutoLog(value = "AI抓拍记录-月度统计")
    @ApiOperation(value = "AI抓拍记录-月度统计", notes = "AI抓拍记录-月度统计")
    @GetMapping(value = "/monthlyStatistic")
    public Result<?> monthlyStatistic() {
        Map<String, Long> statisticMap = iService.monthStatistic();
        return Result.OK(statisticMap);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param entity
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AICaptureRecord entity) {
        return super.exportXls(request, entity, AICaptureRecord.class, "intelligence_monitor_records");
    }

}
