package com.jinghe.breeze.modules.education.service;

import com.jinghe.breeze.modules.education.entity.EduExamPaper;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;

import java.util.List;

/**
 * @Description: 试卷管理
 * @Author: jeecg-boot
 * @Date: 2025-06-25
 * @Version: V1.0
 */
public interface IEduExamPaperService extends IService<EduExamPaper> {

    /**
     * 创建试卷并生成详情快照
     *
     * @param eduExamPaper 试卷信息
     * @return 操作结果
     */
    Result<?> create(EduExamPaper eduExamPaper);

    /**
     * 更新试卷并重新生成详情快照
     *
     * @param eduExamPaper 试卷信息
     * @return 操作结果
     */
    Result<?> updatePaper(EduExamPaper eduExamPaper);

    /**
     * 删除试卷及其相关详情
     *
     * @param id 试卷ID
     * @return 操作结果
     */
    Result<?> deletePaper(String id);

    /**
     * 批量删除试卷及其相关详情
     *
     * @param ids 试卷ID列表
     * @return 操作结果
     */
    Result<?> deletePapers(List<String> ids);

    /**
     * 填充试卷的详情数据
     * 将 rules JSON 解析成 PaperRule 对象，并为每个 PaperRule 补充对应的 EduExamPaperDetail 列表
     *
     * @param eduExamPapers            试卷列表
     * @param includeAnswerAndAnalysis
     */
    void fillPaperDetails(List<EduExamPaper> eduExamPapers ,boolean includeAnswerAndAnalysis);

}
