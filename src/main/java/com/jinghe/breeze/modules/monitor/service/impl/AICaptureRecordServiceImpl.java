package com.jinghe.breeze.modules.monitor.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghe.breeze.common.utils.DateUtils;
import com.jinghe.breeze.modules.monitor.entity.AICaptureRecord;
import com.jinghe.breeze.modules.monitor.mapper.AICaptureRecordMapper;
import com.jinghe.breeze.modules.monitor.service.IAICaptureRecordService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: AI抓拍
 * @Author: jeecg-boot
 * @Date:   2024-08-12
 * @Version: V1.0
 */
@Service
public class AICaptureRecordServiceImpl extends ServiceImpl<AICaptureRecordMapper, AICaptureRecord> implements IAICaptureRecordService {

     @Override
     public Map<String, Long> monthStatistic(){
         Date monthBegin = DateUtils.getMonthBegin(new Date());
         LambdaQueryWrapper<AICaptureRecord> queryWrapper = new LambdaQueryWrapper<>();
         queryWrapper.ge(AICaptureRecord::getCaptureTime, monthBegin);
         List<AICaptureRecord> list = baseMapper.selectList(queryWrapper);
         Map<String, Long> collect = list.stream().collect(Collectors.groupingBy(AICaptureRecord::getIdentifyType, Collectors.counting()));
         return collect;
     }

}
