package com.jinghe.breeze.modules.quality.service;

import com.jinghe.breeze.modules.quality.entity.QualityCatalogueTree;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.exception.JeecgBootException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.util.List;

/**
 * @Description: quality_catalogue_tree
 * @Author: jeecg-boot
 * @Date:   2025-06-20
 * @Version: V1.0
 */
public interface IQualityCatalogueTreeService extends IService<QualityCatalogueTree> {

	/**根节点父ID的值*/
	public static final String ROOT_PID_VALUE = "0";
	
	/**树节点有子节点状态值*/
	public static final String HASCHILD = "1";
	
	/**树节点无子节点状态值*/
	public static final String NOCHILD = "0";

	/**新增节点*/
	void addQualityCatalogueTree(QualityCatalogueTree qualityCatalogueTree);
	
	/**修改节点*/
	void updateQualityCatalogueTree(QualityCatalogueTree qualityCatalogueTree) throws JeecgBootException;
	
	/**删除节点*/
	void deleteQualityCatalogueTree(String id) throws JeecgBootException;

	/**查询所有数据，无分页*/
    List<QualityCatalogueTree> queryTreeListNoPage(QueryWrapper<QualityCatalogueTree> queryWrapper);

}
