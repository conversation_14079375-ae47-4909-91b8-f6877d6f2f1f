<#assign fieldValidType = po.fieldValidType!''>
<#if po.nullable == 'N' || fieldValidType == '*'>
    @NotNull(message = "${po.filedComment}不能为空!")
</#if>
<#-- 唯一校验 -->
<#if fieldValidType == 'only'>

<#-- 6到16位数字 -->
<#elseif fieldValidType == 'n6-16'>
    @Pattern(regexp = "^\\d{6,16}$",message = "请输入6到16位数字!")
<#-- 6到16位任意字符 -->
<#elseif fieldValidType == '*6-16'>
    @Pattern(regexp = "^.{6,16}$",message = "请输入6到16位任意字符!")
<#-- 6到18位字符串 -->
<#elseif fieldValidType == 's6-18'>
    @Pattern(regexp = "^.{6,18}$",message = "请输入6到18位任意字符!")
<#-- 网址 -->
<#elseif fieldValidType == 'url'>
    @Pattern(regexp = "^((ht|f)tps?):\\/\\/[\\w\\-]+(\\.[\\w\\-]+)+([\\w\\-.,@?^=%&:\\/~+#]*[\\w\\-@?^=%&\\/~+#])?$",message = "请输入正确的网址!")
<#-- 电子邮件 -->
<#elseif fieldValidType == 'e'>
    @Pattern(regexp = "^([\\w]+\\.*)([\\w]+)@[\\w]+\\.\\w{3}(\\.\\w{2}|)$",message = "请输入正确的电子邮件!")
<#-- 手机号码 -->
<#elseif fieldValidType == 'm'>
    @Pattern(regexp = "^1[3456789]\\d{9}$",message = "请输入正确的手机号码!")
<#-- 邮政编码 -->
<#elseif fieldValidType == 'p'>
    @Pattern(regexp = "^[1-9]\\d{5}$",message = "请输入正确的邮政编码!")
<#-- 字母 -->
<#elseif fieldValidType == 's'>
    @Pattern(regexp = "^[A-Z|a-z]+$",message = "请输入字母!")
<#-- 数字 -->
<#elseif fieldValidType == 'n'>
    @Pattern(regexp = "^-?\\d+\\.?\\d*$",message = "请输入数字!")
<#-- 整数 -->
<#elseif fieldValidType == 'z'>
    @Pattern(regexp = "^-?\\d+$",message = "请输入整数!")
<#-- 金额 -->
<#elseif fieldValidType == 'money'>
    @Pattern(regexp = "^(([1-9][0-9]*)|([0]\\.\\d{0,2}|[1-9][0-9]*\\.\\d{0,2}))$",message = "请输入正确的金额!")
<#-- 正则校验 -->
<#elseif fieldValidType != '' && fieldValidType != '*'>
    @Pattern(regexp = "${fieldValidType?replace("\\","\\\\")}",message = "不符合校验规则!")
<#-- 无校验 -->
<#else>

</#if>