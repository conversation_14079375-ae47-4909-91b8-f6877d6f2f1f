package com.jinghe.breeze.modules.quickentry.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import com.jinghe.breeze.modules.quickentry.entity.QuickEntryRecord;
import com.jinghe.breeze.modules.quickentry.service.IQuickEntryRecordService;

import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: quick_entry_record
 * @Author: jeecg-boot
 * @Date: 2024-06-26
 * @Version: V1.0
 */
@Api(tags = "quick_entry_record")
@RestController
@RequestMapping("/quickentry/quickEntryRecord")
@Slf4j
public class QuickEntryRecordController extends JeecgController<QuickEntryRecord, IQuickEntryRecordService> {
    @Autowired
    private IQuickEntryRecordService quickEntryRecordService;

    /**
     *
     * @return
     */
    @AutoLog(value = "quick_entry_record-按登录用户查询")
    @ApiOperation(value = "quick_entry_record-按登录用户查询", notes = "quick_entry_record-按登录用户查询")
    @GetMapping(value = "/queryByLoginUser")
    public Result<?> queryByLoginUser() {
        return quickEntryRecordService.queryByLoginUser();
    }

    /**
     * 添加
     *
     * @param quickEntryRecord
     * @return
     */
    @AutoLog(value = "quick_entry_record-添加")
    @ApiOperation(value = "quick_entry_record-添加", notes = "quick_entry_record-添加")
    // @RequiresPermissions("quickEntryRecord:add")
    @PostMapping(value = "/add")
    public Result<?> add(@Validated @RequestBody QuickEntryRecord quickEntryRecord) {
        quickEntryRecordService.saveInfo(quickEntryRecord);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param quickEntryRecord
     * @return
     */
    @AutoLog(value = "quick_entry_record-编辑")
    @ApiOperation(value = "quick_entry_record-编辑", notes = "quick_entry_record-编辑")
    // @RequiresPermissions("quickEntryRecord:edit")
    @PutMapping(value = "/edit")
    public Result<?> edit(@Validated @RequestBody QuickEntryRecord quickEntryRecord) {
        quickEntryRecordService.saveInfo(quickEntryRecord);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "quick_entry_record-通过id删除")
    @ApiOperation(value = "quick_entry_record-通过id删除", notes = "quick_entry_record-通过id删除")
    // @RequiresPermissions("quickEntryRecord:delete")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        quickEntryRecordService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "quick_entry_record-批量删除")
    @ApiOperation(value = "quick_entry_record-批量删除", notes = "quick_entry_record-批量删除")
    // @RequiresPermissions("quickEntryRecord:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.quickEntryRecordService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "quick_entry_record-通过id查询")
    @ApiOperation(value = "quick_entry_record-通过id查询", notes = "quick_entry_record-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        QuickEntryRecord quickEntryRecord = quickEntryRecordService.getById(id);
        if (quickEntryRecord == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(quickEntryRecord);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param quickEntryRecord
     */
    // @RequiresPermissions("quickEntryRecord:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, QuickEntryRecord quickEntryRecord) {
        return super.exportXls(request, quickEntryRecord, QuickEntryRecord.class, "quick_entry_record");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    // @RequiresPermissions("quickEntryRecord:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, QuickEntryRecord.class);
    }

}
