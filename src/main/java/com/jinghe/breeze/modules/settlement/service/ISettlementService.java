package com.jinghe.breeze.modules.settlement.service;

import com.jinghe.breeze.modules.settlement.entity.ContractInfoDetail;
import com.jinghe.breeze.modules.settlement.entity.Settlement;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: settlement
 * @Author: jeecg-boot
 * @Date: 2024-06-03
 * @Version: V1.0
 */
public interface ISettlementService extends IService<Settlement> {

    List<ContractInfoDetail> getTreeData(String contractId);

    void addContractInfomation(List<Settlement> settleList);

    List<ContractInfoDetail> getChild(String code, String id);

    List<ContractInfoDetail> getBatchSettlementInfo(String contractId, String batch);

    void saveInfo(List<Settlement> settlementList, String batch, String contractId);

    void addSettlement(List<Settlement> settlements);
}
