package com.jinghe.breeze.modules.ship.controller;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.modules.ship.entity.ShipAlarmDetail;
import com.jinghe.breeze.modules.ship.service.IShipAlarmListService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: ship_alarm_list
 * @Author: jeecg-boot
 * @Date: 2024-04-23
 * @Version: V1.0
 */
@Api(tags = "ship_alarm_list")
@RestController
@RequestMapping("/ship/shipAlarmList")
@Slf4j
public class ShipAlarmDetailController extends JeecgController<ShipAlarmDetail, IShipAlarmListService> {
    @Autowired
    private IShipAlarmListService shipAlarmListService;

    /**
     * 分页列表查询
     *
     * @param shipAlarmDetail
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "ship_alarm_list-分页列表查询")
    @ApiOperation(value = "ship_alarm_list-分页列表查询", notes = "ship_alarm_list-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(ShipAlarmDetail shipAlarmDetail,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   @RequestParam(name = "startTime", required = false) String startTime,
                                   @RequestParam(name = "endTime", required = false) String endTime,
                                   HttpServletRequest req) {
        QueryWrapper<ShipAlarmDetail> queryWrapper = QueryGenerator.initQueryWrapper(shipAlarmDetail, req.getParameterMap());
        SimpleDateFormat myFmt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (oConvertUtils.isNotEmpty(startTime)){
            try {
                queryWrapper.between("alarm_date",myFmt.parse(startTime),myFmt.parse(endTime));
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        Page<ShipAlarmDetail> page = new Page<ShipAlarmDetail>(pageNo, pageSize);
        IPage<ShipAlarmDetail> pageList = shipAlarmListService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 列表查询
     *
     * @param shipAlarmDetail
     * @param
     * @param
     * @param
     * @return
     */
    @AutoLog(value = "列表查询")
    @ApiOperation(value = "列表查询", notes = "列表查询")
    @GetMapping(value = "/getList")
    public Result<?> getList(ShipAlarmDetail shipAlarmDetail) {
        LambdaQueryWrapper<ShipAlarmDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShipAlarmDetail::getAlarmDay, shipAlarmDetail.getAlarmDay());
        queryWrapper.eq(ShipAlarmDetail::getShipName, shipAlarmDetail.getMmsi());
        queryWrapper.orderByAsc(ShipAlarmDetail::getAlarmDate);
        List<ShipAlarmDetail> pageList = shipAlarmListService.list(queryWrapper);
        return Result.OK(pageList);
    }


    /**
     * 添加
     *
     * @param shipAlarmDetail
     * @return
     */
    @AutoLog(value = "ship_alarm_list-添加")
    @ApiOperation(value = "ship_alarm_list-添加", notes = "ship_alarm_list-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody ShipAlarmDetail shipAlarmDetail) {
        shipAlarmListService.save(shipAlarmDetail);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param shipAlarmDetail
     * @return
     */
    @AutoLog(value = "ship_alarm_list-编辑")
    @ApiOperation(value = "ship_alarm_list-编辑", notes = "ship_alarm_list-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody ShipAlarmDetail shipAlarmDetail) {
        shipAlarmListService.updateById(shipAlarmDetail);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "ship_alarm_list-通过id删除")
    @ApiOperation(value = "ship_alarm_list-通过id删除", notes = "ship_alarm_list-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        shipAlarmListService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "ship_alarm_list-批量删除")
    @ApiOperation(value = "ship_alarm_list-批量删除", notes = "ship_alarm_list-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.shipAlarmListService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "ship_alarm_list-通过id查询")
    @ApiOperation(value = "ship_alarm_list-通过id查询", notes = "ship_alarm_list-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        ShipAlarmDetail shipAlarmDetail = shipAlarmListService.getById(id);
        if (shipAlarmDetail == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(shipAlarmDetail);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param shipAlarmDetail
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ShipAlarmDetail shipAlarmDetail) {
        return super.exportXls(request, shipAlarmDetail, ShipAlarmDetail.class, "ship_alarm_list");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ShipAlarmDetail.class);
    }

}
