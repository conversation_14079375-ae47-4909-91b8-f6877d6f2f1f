package com.jinghe.breeze.modules.ship.service;

import com.jinghe.breeze.modules.ship.entity.ShipOutgoing;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;

/**
 * @Description: ship_outgoing
 * @Author: jeecg-boot
 * @Date:   2024-04-23
 * @Version: V1.0
 */
public interface IShipOutgoingService extends IService<ShipOutgoing> {

    Result<?> add(ShipOutgoing shipOutgoing);


}
