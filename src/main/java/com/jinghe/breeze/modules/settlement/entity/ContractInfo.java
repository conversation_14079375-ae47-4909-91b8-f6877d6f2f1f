package com.jinghe.breeze.modules.settlement.entity;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.*;

/**
 * @Description: contract_info
 * @Author: jeecg-boot
 * @Date: 2024-05-31
 * @Version: V1.0
 */
@Data
@TableName("contract_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "contract_info对象", description = "contract_info")
public class ContractInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 标识
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "标识")
    private java.lang.String id;
    /**
     * 创建人
     */

    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
    /**
     * 更新人
     */

    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
    /**
     * delFlag
     */
    @Excel(name = "delFlag", width = 15)
    @ApiModelProperty(value = "delFlag")
    private java.lang.Integer delFlag;
    /**
     * 组织机构编码
     */

    @ApiModelProperty(value = "组织机构编码")
    private java.lang.String sysOrgCode;
    /**
     * 项目id
     */
    @Excel(name = "项目id", width = 15)

    @ApiModelProperty(value = "项目id")
    private java.lang.String projectId;
    /**
     * 合同编码
     */
    @Excel(name = "合同编号", width = 15)
    @NotNull(message = "合同编号不能为空!")
    @ApiModelProperty(value = "合同编号")
    private java.lang.String code;
    /**
     * 合同名称
     */
    @Excel(name = "合同名称", width = 15)
    @NotNull(message = "合同名称不能为空!")

    @ApiModelProperty(value = "合同名称")
    private java.lang.String name;
    /**
     * 合同甲方
     */
    @Excel(name = "合同甲方", width = 15)
    @NotNull(message = "合同甲方不能为空!")

    @ApiModelProperty(value = "合同甲方")
    private java.lang.String partyA;
    /**
     * 合同乙方
     */
    @Excel(name = "合同乙方", width = 15)
    @NotNull(message = "合同乙方不能为空!")
    @ApiModelProperty(value = "合同乙方")
    private java.lang.String partyB;
    /**
     * 合同监理方
     */
    @Excel(name = "合同监理方", width = 15)
    @ApiModelProperty(value = "合同监理方")
    private java.lang.String contractSupervisor;

    /**
     * 合同总价
     */
    @Excel(name = "合同总价", width = 15)

    @ApiModelProperty(value = "合同总价")
    private java.math.BigDecimal totalMoney;
    /**
     * 签订日期到天
     */
    @Excel(name = "签订日期到天", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(updateStrategy = FieldStrategy.IGNORED, insertStrategy = FieldStrategy.IGNORED) //忽略空值检查
    @ApiModelProperty(value = "签订日期到天")
    private Date signDate;

    /**
     * 合同附件
     */
    @Excel(name = "合同附件", width = 15)
    @NotNull(message = "合同附件不能为空!")
    @ApiModelProperty(value = "合同附件")
    private String file;

    @TableField(exist = false)
    private List<ContractInfoDetail> children;

    @ApiModelProperty(value = "已结算金额")
    @TableField(exist = false)
    private BigDecimal settledMoney;
    @ApiModelProperty(value = "支付金额")
    @TableField(exist = false)
    private BigDecimal payMoney;
    @ApiModelProperty(value = "发票金额")
    @TableField(exist = false)
    private BigDecimal invoiceMoney;

    @ApiModelProperty(value = "已结算百分比")
    @TableField(exist = false)
    private BigDecimal settlePercent;

    @ApiModelProperty(value = "是否有子级")
    private Integer hasChild;

    @ApiModelProperty(value = "pid")
    private String pid;

}
