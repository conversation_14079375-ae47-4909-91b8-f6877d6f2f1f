package com.jinghe.breeze.modules.safety.controller;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.jinghe.breeze.modules.safety.entity.SafeMaintenanceRecords;
import com.jinghe.breeze.modules.safety.service.ISafeMaintenanceRecordsService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: ai设备维护记录
 * @Author: jeecg-boot
 * @Date:   2025-06-22
 * @Version: V1.0
 */
@Api(tags="ai设备维护记录")
@RestController
@RequestMapping("/safety/safeMaintenanceRecords")
@Slf4j
public class SafeMaintenanceRecordsController extends JeecgController<SafeMaintenanceRecords, ISafeMaintenanceRecordsService> {
	@Autowired
	private ISafeMaintenanceRecordsService safeMaintenanceRecordsService;
	
	/**
	 * 分页列表查询
	 *
	 * @param safeMaintenanceRecords
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "ai设备维护记录-分页列表查询")
	@ApiOperation(value="ai设备维护记录-分页列表查询", notes="ai设备维护记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(SafeMaintenanceRecords safeMaintenanceRecords,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<SafeMaintenanceRecords> queryWrapper = QueryGenerator.initQueryWrapper(safeMaintenanceRecords, req.getParameterMap());
		Page<SafeMaintenanceRecords> page = new Page<SafeMaintenanceRecords>(pageNo, pageSize);
		IPage<SafeMaintenanceRecords> pageList = safeMaintenanceRecordsService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param safeMaintenanceRecords
	 * @return
	 */
	@AutoLog(value = "ai设备维护记录-添加")
	@ApiOperation(value="ai设备维护记录-添加", notes="ai设备维护记录-添加")
	@RequiresPermissions("safeMaintenanceRecords:add")
	@PostMapping(value = "/add")
	public Result<?> add(@Validated @RequestBody SafeMaintenanceRecords safeMaintenanceRecords) {
		safeMaintenanceRecordsService.save(safeMaintenanceRecords);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param safeMaintenanceRecords
	 * @return
	 */
	@AutoLog(value = "ai设备维护记录-编辑")
	@ApiOperation(value="ai设备维护记录-编辑", notes="ai设备维护记录-编辑")
	@RequiresPermissions("safeMaintenanceRecords:edit")
	@PutMapping(value = "/edit")
	public Result<?> edit(@Validated @RequestBody SafeMaintenanceRecords safeMaintenanceRecords) {
		safeMaintenanceRecordsService.updateById(safeMaintenanceRecords);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "ai设备维护记录-通过id删除")
	@ApiOperation(value="ai设备维护记录-通过id删除", notes="ai设备维护记录-通过id删除")
	@RequiresPermissions("safeMaintenanceRecords:delete")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		safeMaintenanceRecordsService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "ai设备维护记录-批量删除")
	@ApiOperation(value="ai设备维护记录-批量删除", notes="ai设备维护记录-批量删除")
	@RequiresPermissions("safeMaintenanceRecords:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.safeMaintenanceRecordsService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "ai设备维护记录-通过id查询")
	@ApiOperation(value="ai设备维护记录-通过id查询", notes="ai设备维护记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		SafeMaintenanceRecords safeMaintenanceRecords = safeMaintenanceRecordsService.getById(id);
		if(safeMaintenanceRecords==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(safeMaintenanceRecords);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param safeMaintenanceRecords
    */
    @RequiresPermissions("safeMaintenanceRecords:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SafeMaintenanceRecords safeMaintenanceRecords) {
        return super.exportXls(request, safeMaintenanceRecords, SafeMaintenanceRecords.class, "ai设备维护记录");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("safeMaintenanceRecords:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SafeMaintenanceRecords.class);
    }

}
