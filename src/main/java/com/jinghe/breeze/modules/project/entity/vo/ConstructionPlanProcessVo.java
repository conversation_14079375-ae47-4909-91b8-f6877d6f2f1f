package com.jinghe.breeze.modules.project.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.util.List;
import java.util.Map;

@Data
public class ConstructionPlanProcessVo {
    /**计划编制code*/
    @Excel(name = "计划编制code", width = 15)

    @ApiModelProperty(value = "计划编制code")
    private String planCode;
    /**pbsID*/
    @Excel(name = "pbsID", width = 15)

    @ApiModelProperty(value = "pbsID")
    private Map<String,List<String>> pbsIds;

}
