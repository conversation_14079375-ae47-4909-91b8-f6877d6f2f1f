package com.jinghe.breeze.modules.project.service.impl;

import com.jinghe.breeze.common.constants.enums.ProjectFensEnum;
import com.jinghe.breeze.modules.project.entity.ProjectFence;
import com.jinghe.breeze.modules.project.mapper.ProjectFenceMapper;
import com.jinghe.breeze.modules.project.service.IProjectFenceService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.util.List;

import org.jeecg.common.exception.JeecgBootException;
import org.springframework.stereotype.Service;

import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: project_fence
 * @Author: jeecg-boot
 * @Date: 2024-04-17
 * @Version: V1.0
 */
@Service
public class ProjectFenceServiceImpl extends ServiceImpl<ProjectFenceMapper, ProjectFence>
        implements IProjectFenceService {
    private static final ObjectMapper mapper = new ObjectMapper();

    private void valiedateFenceRadius(String radius) {
        if (StringUtils.isEmpty(radius)) {
            throw new JeecgBootException("围栏范围不可为空");
        }
        if (radius == null || radius.trim().isEmpty()) {
            throw new JeecgBootException("围栏范围不可为空");
        }
        List<List<Double>> coordinates;
        try {
            coordinates = mapper.readValue(radius, new TypeReference<List<List<Double>>>() {
            });
        } catch (IOException e) {
            throw new JeecgBootException("围栏范围格式不正确", e);
        }

        if (coordinates.size() < 3) {
            throw new JeecgBootException("围栏范围至少需要三个坐标点");
        }

        for (List<Double> coordinate : coordinates) {
            if (coordinate.size() != 2) {
                throw new JeecgBootException("坐标格式不正确");
            }
        }
    }

    @Override
    public void editInfo(ProjectFence projectFence) {
        String id = projectFence.getId();
        if (id == null) {
            throw new JeecgBootException("缺少必要参数id");
        }
        ProjectFence entity = baseMapper.selectById(id);
        if (entity == null) {
            throw new JeecgBootException("记录不存在");
        }
        String fenceType = projectFence.getFenceType();
        if (fenceType == null) {
            throw new JeecgBootException("需要指定风机类型");
        }
        valiedateFenceRadius(projectFence.getFenceRadius());
        if (fenceType.equals(ProjectFensEnum.YJ.getCode())) {
            LambdaQueryWrapper<ProjectFence> queryWrapper = new LambdaQueryWrapper<ProjectFence>();
            queryWrapper.eq(ProjectFence::getFenceType, ProjectFensEnum.YJ.getCode())
                    .ne(ProjectFence::getId, id).last("limit 1");
            if (baseMapper.selectOne(queryWrapper) != null) {
                throw new JeecgBootException(String.format("已存在一个%s,不允许重复添加", ProjectFensEnum.YJ.getName()));
            }
        }
        baseMapper.updateById(projectFence);
    }

    @Override
    public void saveInfo(ProjectFence projectFence) {
        String fenceType = projectFence.getFenceType();
        if (fenceType == null) {
            throw new JeecgBootException("需要指定风机类型");
        }
        valiedateFenceRadius(projectFence.getFenceRadius());
        if (fenceType.equals(ProjectFensEnum.YJ.getCode())) {
            LambdaQueryWrapper<ProjectFence> queryWrapper = new LambdaQueryWrapper<ProjectFence>()
                    .eq(ProjectFence::getFenceType, ProjectFensEnum.YJ.getCode())
                    .last("limit 1");
            if (baseMapper.selectOne(queryWrapper) != null) {
                throw new JeecgBootException(String.format("已存在一个%s,不允许重复添加", ProjectFensEnum.YJ.getName()));
            }
        }
        baseMapper.insert(projectFence);
    }
}
