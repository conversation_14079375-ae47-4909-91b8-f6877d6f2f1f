package com.jinghe.breeze.modules.wind.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.*;

/**
 * @Description: wind_report
 * @Author: jeecg-boot
 * @Date: 2024-05-24
 * @Version: V1.0
 */
@Data
@TableName("wind_report")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "wind_report对象", description = "wind_report")
public class WindReport implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)

    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 创建人
     */

    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**
     * 更新人
     */

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;

    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
    /**
     * 删除标识
     */
    @Excel(name = "删除标识", width = 15)
    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;
    /**
     * 避风通知id
     */
    @Excel(name = "避风通知id", width = 15)
    @NotNull(message = "避风通知id不能为空!")

    @ApiModelProperty(value = "避风通知id")
    private String notifyId;
    /**
     * 计划开始日期
     */
//    @Excel(name = "计划开始日期", width = 20, format = "yyyy-MM-dd")
//    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @NotNull(message = "计划开始日期不能为空!")
//    @ApiModelProperty(value = "计划开始日期")
//    private Date planStartDate;
    /**
     * 计划结束日期
     */
//    @Excel(name = "计划结束日期", width = 20, format = "yyyy-MM-dd")
//    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @NotNull(message = "计划结束日期不能为空!")
//
//    @ApiModelProperty(value = "计划结束日期")
//    private Date planEndDate;
    /**
     * 截止上报时间
     */
    @Excel(name = "截止上报时间", width = 20, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "截止上报时间不能为空!")

    @ApiModelProperty(value = "截止上报日期")
    private Date deadline;
    /**
     * 联系人信息
     */
    @Excel(name = "联系人信息", width = 15)
    @NotNull(message = "联系人信息不能为空!")

    @ApiModelProperty(value = "联系人信息")
    private String contactInfo;
    /**
     * 报备人id
     */
    @Excel(name = "报备人姓名", width = 15)
    @ApiModelProperty(value = "报备人")
    private String reporter;
    /**
     * 单位id
     */
    @Excel(name = "单位名称", width = 15)
    @ApiModelProperty(value = "单位名称")
    private String unitName;

    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;


    @Excel(name = "用户id", width = 15)
    @ApiModelProperty(value = "用户id")
    private String userId;

    @TableField(exist = false)
    private List<WindReportDetail> details;

    @TableField(exist = false)
    private WindNotification windNotification;

}
