package com.jinghe.breeze.modules.wind.service;
import org.jeecg.common.api.vo.Result;
import com.jinghe.breeze.modules.wind.entity.WindNotification;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * @Description: wind_notification
 * @Author: jeecg-boot
 * @Date:   2024-05-22
 * @Version: V1.0
 */
public interface IWindNotificationService extends IService<WindNotification> {

    String addWindNotification(WindNotification windNotification);

    Result<?> editWindNotification(WindNotification windNotification);

    Result<?> releaseWindNotification(WindNotification windNotification);

    Result<?> endWindNotification(WindNotification windNotification);
    Map<String, List<WindNotification>> getRecentNotify();

    Result<?> getDetail();

}
