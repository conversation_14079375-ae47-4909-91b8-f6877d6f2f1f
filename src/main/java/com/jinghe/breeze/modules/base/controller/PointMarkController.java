package com.jinghe.breeze.modules.base.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jinghe.breeze.common.enums.DelFlagEnum;
import com.jinghe.breeze.modules.base.entity.BaseArea;
import com.jinghe.breeze.modules.base.service.IBaseAreaService;
import com.jinghe.breeze.modules.device.entity.DeviceGate;
import com.jinghe.breeze.modules.device.entity.DeviceInfo;
import com.jinghe.breeze.modules.device.service.IDeviceGateService;
import com.jinghe.breeze.modules.device.service.IDeviceInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.common.util.oConvertUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Api(tags = "点位标记")
@RestController
@RequestMapping("/base/points")
@Slf4j
public class PointMarkController {

    @Value("${jeecg.uploadFileTypes:}")
    private String uploadFileTypes;
    @Value("${jeecg.path.upload}")
    private String uploadpath;
    @Value("${jeecg.uploadType}")
    private String uploadType;
    @Value("${jeecg.minio.minio_url}")
    private String minioUrl;
    @Value("${jeecg.minio.minio_name}")
    private String minioName;
    @Value("${jeecg.minio.minio_pass}")
    private String minioPass;

    @Autowired
    private IDeviceInfoService iDeviceInfoService;

    @Autowired
    private IBaseAreaService baseAreaService;

    @Autowired
    private IDeviceGateService deviceGateService;

    @Autowired
    private IDeviceInfoService deviceInfoService;


    /**
     * 根据安装区域获取设备点位
     */
    @AutoLog(value = "点位标记-列表查询")
    @ApiOperation(value = "点位标记-列表查询", notes = "点位标记-列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryMapByArea(@RequestParam(value = "type", required = false) String type) {
        List<DeviceInfo> list;
        if (StrUtil.isNotEmpty(type)) {
            LambdaQueryWrapper<DeviceGate> deviceGateWrapper = new LambdaQueryWrapper<>();
            deviceGateWrapper.eq(DeviceGate::getDelFlag, DelFlagEnum.NORMAL.getType());
            deviceGateWrapper.eq(DeviceGate::getGateType, type);

            List<DeviceGate> deviceGates = deviceGateService.list(deviceGateWrapper);

            if (CollectionUtil.isEmpty(deviceGates)) {
                return Result.OK(Collections.emptyMap());
            }
            List<String> ids = deviceGates.stream()
                    .map(DeviceGate::getBaseId)
                    .collect(Collectors.toList());
            LambdaQueryWrapper<DeviceInfo> gateQueryWrapper = new LambdaQueryWrapper<>();
            gateQueryWrapper.in(DeviceInfo::getId, ids);
            gateQueryWrapper.eq(DeviceInfo::getDelFlag, DelFlagEnum.NORMAL.getType());

            list = deviceInfoService.list(gateQueryWrapper);
        } else {
            list = deviceInfoService.list(Wrappers.<DeviceInfo>lambdaQuery()
                    .eq(DeviceInfo::getDelFlag, DelFlagEnum.NORMAL.getType()));
        }
        Map<String, List<DeviceInfo>> gatesByArea = list.stream()
                .collect(Collectors.groupingBy(DeviceInfo::getAreaId));
        return Result.OK(gatesByArea);
    }

    @AutoLog(value = "点位标记-上传信息")
    @ApiOperation(value = "点位标记-上传信息", notes = "点位标记-上传信息")
    @PostMapping(value = "/uploadInfo")
    public Result<?> uploadInfo(@RequestParam(value = "areaId") String areaId, @RequestParam(value = "file", required = false) MultipartFile file,
                                @RequestParam(value = "positionX", required = false) String positionX,
                                @RequestParam(value = "positionY", required = false) String positionY) {
        if (StrUtil.isEmpty(areaId)) {
            return Result.error("安装区域不能为空！");
        }
//
//        去查找当前区域的数据
        BaseArea baseArea = baseAreaService.getById(areaId);
        if (ObjectUtil.isEmpty(baseArea)) {
            return Result.error("安装区域不存在！");
        }
//        if (ObjectUtil.isEmpty(file) && StrUtil.isEmpty(positionX) && StrUtil.isEmpty(positionY)) {
//            return Result.error("底图或点位不能同时为空！");
//        }
//        上传文件
        if (ObjectUtil.isNotEmpty(file)) {
            if (!ObjectUtils.isEmpty(this.uploadFileTypes)) {
                String originalFileName = file.getOriginalFilename();
                if (originalFileName.indexOf(".") == -1) {
                    return Result.error("未检测到文件类型！");
                }
                String ofileType = originalFileName.split("\\.")[1];
                String[] fileTypes = this.uploadFileTypes.split(",");
                if (!Arrays.asList(fileTypes).contains(ofileType)) {
                    return Result.error("文件类型不在白名单中！");
                }
            }
            String savePath = null;
            try {
                savePath = CommonUtils.upload(file, this.uploadpath, this.uploadType);
            } catch (Exception e) {
                log.info("上传失败！");
                return Result.error("上传失败！");
            }
            if (oConvertUtils.isNotEmpty(savePath)) {
                baseArea.setFilePath(savePath);
                baseAreaService.updateById(baseArea);
                return Result.OK(baseArea);
            } else {
                return Result.error("上传失败！");
            }
        }

        //存储点位数据
        if (ObjectUtil.isNotEmpty(positionX) && ObjectUtil.isNotEmpty(positionY)) {
            baseArea.setPositionX(positionX);
            baseArea.setPositionY(positionY);
        } else {
            baseArea.setPositionX("");
            baseArea.setPositionY("");
        }
        baseAreaService.updateById(baseArea);
        return Result.OK(baseArea);
    }
}
