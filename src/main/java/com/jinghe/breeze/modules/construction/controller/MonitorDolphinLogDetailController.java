package com.jinghe.breeze.modules.construction.controller;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.ZoneId;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.modules.construction.entity.PolarPlotRequestData;
import com.jinghe.breeze.modules.construction.entity.MonitorDolphinLog;
import com.jinghe.breeze.modules.construction.entity.vo.MonthlyStatsDTO;
import com.jinghe.breeze.modules.construction.service.IMonitorDolphinLogService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.jinghe.breeze.modules.construction.entity.MonitorDolphinLogDetail;
import com.jinghe.breeze.modules.construction.service.IMonitorDolphinLogDetailService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: 观豚日志列表
 * @Author: jeecg-boot
 * @Date: 2024-05-11
 * @Version: V1.0
 */
@Api(tags = "观豚日志列表")
@RestController
@RequestMapping("/construction/watchDolphinLogList")
@Slf4j
public class MonitorDolphinLogDetailController extends JeecgController<MonitorDolphinLogDetail, IMonitorDolphinLogDetailService> {
    @Autowired
    private IMonitorDolphinLogDetailService watchDolphinLogDetailService;

    @Autowired
    private IMonitorDolphinLogService watchDolphinLogService;

    /**
     * 分页列表查询
     *
     * @param monitorDolphinLogDetail
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "观豚日志列表-分页列表查询")
    @ApiOperation(value = "观豚日志列表-分页列表查询", notes = "观豚日志列表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(MonitorDolphinLogDetail monitorDolphinLogDetail,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<MonitorDolphinLogDetail> queryWrapper = QueryGenerator.initQueryWrapper(monitorDolphinLogDetail, req.getParameterMap());
        Page<MonitorDolphinLogDetail> page = new Page<MonitorDolphinLogDetail>(pageNo, pageSize);
        IPage<MonitorDolphinLogDetail> pageList = watchDolphinLogDetailService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

//	@AutoLog(value = "")


    /**
     * 添加
     *
     * @param monitorDolphinLogDetail
     * @return
     */
    @AutoLog(value = "观豚日志列表-添加")
    @ApiOperation(value = "观豚日志列表-添加", notes = "观豚日志列表-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody MonitorDolphinLogDetail monitorDolphinLogDetail) {
        watchDolphinLogDetailService.save(monitorDolphinLogDetail);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param monitorDolphinLogDetail
     * @return
     */
    @AutoLog(value = "观豚日志列表-编辑")
    @ApiOperation(value = "观豚日志列表-编辑", notes = "观豚日志列表-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody MonitorDolphinLogDetail monitorDolphinLogDetail) {
        watchDolphinLogDetailService.updateById(monitorDolphinLogDetail);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "观豚日志列表-通过id删除")
    @ApiOperation(value = "观豚日志列表-通过id删除", notes = "观豚日志列表-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        watchDolphinLogDetailService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "观豚日志列表-批量删除")
    @ApiOperation(value = "观豚日志列表-批量删除", notes = "观豚日志列表-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.watchDolphinLogDetailService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "观豚日志列表-通过id查询")
    @ApiOperation(value = "观豚日志列表-通过id查询", notes = "观豚日志列表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        MonitorDolphinLogDetail monitorDolphinLogDetail = watchDolphinLogDetailService.getById(id);
        if (monitorDolphinLogDetail == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(monitorDolphinLogDetail);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param monitorDolphinLogDetail
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, MonitorDolphinLogDetail monitorDolphinLogDetail) {
        return super.exportXls(request, monitorDolphinLogDetail, MonitorDolphinLogDetail.class, "观豚日志列表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, MonitorDolphinLogDetail.class);
    }

    @RequestMapping(value = "/getPolarPlotData", method = RequestMethod.POST)
    @ApiOperation(value = "获取玫瑰图数据", notes = "根据日期和观测站坐标获取玫瑰图dict返回,key:`0`-`7`,value:数量")
    public Result<?> getPolarPlotData(@RequestBody PolarPlotRequestData polarData) {
        Map<String, Integer> retMap = watchDolphinLogDetailService.getPolarData(polarData);
        return Result.OK(retMap);
    }


    @ApiOperation(value = "根据日期获取所有海豚坐标", notes = "根据日期获取所有海豚坐标")
    @RequestMapping(value = "/queryDolphinPointsByDate", method = RequestMethod.GET)
    public Result<Map<String, String>> queryDolphinPointsByDate(@RequestParam(required = true) String queryDateString) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate date = LocalDate.parse(queryDateString, formatter);
        LambdaQueryWrapper<MonitorDolphinLogDetail> queryWrapper = new LambdaQueryWrapper<>();
        LocalDateTime startTime = date.atStartOfDay();
        LocalDateTime endTime = date.atTime(23, 59, 59);
        queryWrapper.between(MonitorDolphinLogDetail::getStartDate, startTime, endTime);
        queryWrapper.eq(MonitorDolphinLogDetail::getDelFlag, Common.delete_flag.OK);
        List<MonitorDolphinLogDetail> retList = watchDolphinLogDetailService.list(queryWrapper);
        Map<String, String> retMap = new HashMap<>();
        for (MonitorDolphinLogDetail detail : retList) {
            String position = detail.getPosition();
            if (position == null) {
                continue;
            }
            position = position.replace("[", "").replace("]", "").replace(" ", "");
            String[] pos = position.split(",");
            if (pos.length != 2) {
                continue;
            }
            try {
                Double.parseDouble(pos[0]);
                Double.parseDouble(pos[1]);
            } catch (Exception e) {
                continue;
            }
            retMap.put(detail.getId(), detail.getPosition());
        }
        return Result.OK(retMap);
    }

    @ApiOperation(value = "根据详情id获取关联的日志及详情", notes = "根据详情id获取关联的日志及详情")
    @RequestMapping(value = "/queryLogByDetailId", method = RequestMethod.GET)
    public Result<?> queryLogByDetailId(String detailId) {
        QueryWrapper<MonitorDolphinLogDetail> queryWrapper = new QueryWrapper<>();
        MonitorDolphinLogDetail detail = watchDolphinLogDetailService.getOne(queryWrapper.eq("id", detailId)
                .eq("del_flag", Common.delete_flag.OK), false);
        if (detail == null) {
            return Result.error(404, "未找到查询的日志详情!");
        }
        queryWrapper.clear();
        List<MonitorDolphinLogDetail> details = watchDolphinLogDetailService.list(queryWrapper.eq("watch_id", detail.getWatchId())
                .eq("del_flag", Common.delete_flag.OK).orderByAsc("time"));
        QueryWrapper<MonitorDolphinLog> queryLogWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", detail.getWatchId());
        MonitorDolphinLog log = watchDolphinLogService.getOne(queryLogWrapper.eq("del_flag", Common.delete_flag.OK), false);
        if (log == null) {
            return Result.error(404, "未找到相关联的日志!");
        }
        log.setDetails(details);
        return Result.OK(log);
    }

    @ApiOperation(value = "获取最近1年海豚观察分析报告", notes = "获取最近1年海豚观察分析报告")
    @RequestMapping(value = "/getReport", method = RequestMethod.GET)
    public Result<?> getReport() {
        LambdaQueryWrapper<MonitorDolphinLog> queryWrapper = new LambdaQueryWrapper<>();
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime twelveMonthsAgo = now.minus(12, ChronoUnit.MONTHS);
        queryWrapper.between(MonitorDolphinLog::getWatchDate, twelveMonthsAgo, now)
                .eq(MonitorDolphinLog::getExsitDolphin,Common.commonality.ONE.toString());
        List<MonitorDolphinLog> logList = watchDolphinLogService.list(queryWrapper);
        if (logList.isEmpty()){
            return Result.OK();
        }
        List<String> ids = logList.stream().map(MonitorDolphinLog::getId).collect(Collectors.toList());
        List<MonitorDolphinLogDetail> details = watchDolphinLogDetailService.list(new LambdaQueryWrapper<MonitorDolphinLogDetail>()
                .in(MonitorDolphinLogDetail::getWatchId, ids));
        if (details.isEmpty()){
            return Result.OK();
        }
        List<MonthlyStatsDTO> monthlyStatsDTOList = new ArrayList<>();
        for (int i = 0; i < 12; i++) {
            LocalDate date = LocalDate.from(now.minusMonths(i));
            MonthlyStatsDTO monthlyStatsDTO = new MonthlyStatsDTO();
            String dateString = date.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            monthlyStatsDTO.setMonth(dateString);
            List<MonitorDolphinLog> logs = logList.stream().filter(p -> {
                LocalDate watchDate = p.getWatchDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                return dateString.equals(watchDate.format(DateTimeFormatter.ofPattern("yyyy-MM")));
            }).collect(Collectors.toList());

            List<String> idsTmp = logs.stream().map(MonitorDolphinLog::getId).collect(Collectors.toList());
            List<MonitorDolphinLogDetail> logDetails = details.stream().filter(p -> idsTmp.contains(p.getWatchId())).collect(Collectors.toList());
            //筛选观豚影响施工的次数
            List<MonitorDolphinLogDetail> detailList = logDetails.stream().filter(x -> x.getIsConstructionAffected().equals(Common.commonality.ONE.toString())).collect(Collectors.toList());
            monthlyStatsDTO.setTotalCount(logDetails.size());
            monthlyStatsDTO.setAffectedCount(detailList.size());
            monthlyStatsDTOList.add(monthlyStatsDTO);
        }
            Collections.reverse(monthlyStatsDTOList);
        return Result.OK(monthlyStatsDTOList);
    }
}
