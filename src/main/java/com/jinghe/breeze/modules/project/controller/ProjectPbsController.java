package com.jinghe.breeze.modules.project.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.*;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jinghe.breeze.modules.project.entity.ProjectPbs;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.jinghe.breeze.modules.project.service.IProjectPbsService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import org.springframework.web.servlet.ModelAndView;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

import static com.jinghe.breeze.modules.project.service.IProjectPbsService.ROOT_PID_VALUE;

/**
 * @Description: project_pbs
 * @Author: jeecg-boot
 * @Date: 2024-04-11
 * @Version: V1.0
 */
@Api(tags = "project_pbs")
@RestController
@RequestMapping("/project/projectPbs")
@Slf4j
public class ProjectPbsController extends JeecgController<ProjectPbs, IProjectPbsService> {
    @Autowired
    private IProjectPbsService projectPbsService;

    /**
     * 分页列表查询
     *
     * @param projectPbs
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "project_pbs-分页列表查询")
    @ApiOperation(value = "project_pbs-分页列表查询", notes = "project_pbs-分页列表查询")
    @GetMapping(value = "/rootList")
    public Result<?> queryPageList(ProjectPbs projectPbs,
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            HttpServletRequest req) {
        // 此处要求能搜索子级,
        String type = req.getParameter("type");
        if (type == null) {
            return Result.error(400, "未提供必要的参数type!");
        }
        QueryWrapper<ProjectPbs> queryWrapper = QueryGenerator.initQueryWrapper(projectPbs, req.getParameterMap());
        queryWrapper.orderByAsc("fan_section");
        queryWrapper.orderByAsc("code");
        List<ProjectPbs> list = projectPbsService.queryTreeListNoPage(queryWrapper);
        // 用新的排序规则，原有排序规则
        // list.sort(Comparator.comparing(ProjectPbs::getSortNo));
        // 隐藏下级，相关功能暂停
        // projectPbsService.setCanMoveUpDown(list);
        IPage<ProjectPbs> pageList = new Page<>(1, list.size(), list.size());
        pageList.setRecords(list);
        return Result.OK(pageList);
    }

    /**
     * 获取子数据
     *
     * @param projectPbs
     * @param req
     * @return
     */
    @AutoLog(value = "project_pbs-获取子数据")
    @ApiOperation(value = "project_pbs-获取子数据", notes = "project_pbs-获取子数据")
    @GetMapping(value = "/childList")
    public Result<?> queryPageList(ProjectPbs projectPbs, HttpServletRequest req) {
        // 不分页 预期传入一个pid 和 type
        String pid = projectPbs.getPid();
        if (oConvertUtils.isEmpty(pid)) {
            return Result.error(400, "未提供必要的参数pid!");
        }
        String type = projectPbs.getType();
        if (oConvertUtils.isEmpty(type)) {
            return Result.error(400, "未提供必要的参数type!");
        }
        QueryWrapper<ProjectPbs> queryWrapper = QueryGenerator.initQueryWrapper(projectPbs, req.getParameterMap());
        queryWrapper.orderByAsc("sort_no");
        List<ProjectPbs> list = projectPbsService.list(queryWrapper);
        List<ProjectPbs> allList = projectPbsService.list();
        projectPbsService.setCanMoveUpDown(list, allList);
        IPage<ProjectPbs> pageList = new Page<>(1, list.size(), list.size());
        pageList.setRecords(list);
        return Result.OK(pageList);

    }

    /**
     * 批量查询子节点
     *
     * @param parentIds 父ID（多个采用半角逗号分割）
     * @param parentIds
     * @return 返回 IPage
     * @return
     */
    @AutoLog(value = "project_pbs-批量获取子数据")
    @ApiOperation(value = "project_pbs-批量获取子数据", notes = "project_pbs-批量获取子数据")
    @GetMapping("/getChildListBatch")
    public Result<?> getChildListBatch(@RequestParam("parentIds") String parentIds) {
        List<String> parentIdList = Arrays.asList(parentIds.split(","))
                .stream()
                .map(id -> id.trim())
                .filter(id -> !oConvertUtils.isEmpty(id))
                .collect(Collectors.toList());
        List<ProjectPbs> pbsList = projectPbsService.listByIds(parentIdList);
        List<ProjectPbs> allList = projectPbsService.list();
        projectPbsService.setCanMoveUpDown(pbsList, allList);
        IPage<ProjectPbs> pageList = new Page<>(1, pbsList.size(), pbsList.size());
        pageList.setRecords(pbsList);
        return Result.OK(pageList);
    }

    /**
     * 上移
     *
     * @param projectPbs
     * @return
     */
    @AutoLog(value = "project_pbs-上移")
    @ApiOperation(value = "project_pbs-上移", notes = "project_pbs-上移")
    @PostMapping(value = "/moveUp")
    public Result<?> moveUp(@RequestBody ProjectPbs projectPbs) {
        projectPbsService.movePbs(projectPbs, true);
        return Result.OK("操作成功！");
    }

    @AutoLog(value = "project_pbs-下移")
    @ApiOperation(value = "project_pbs-下移", notes = "project_pbs-下移")
    @PostMapping(value = "/moveDown")
    public Result<?> moveDown(@RequestBody ProjectPbs projectPbs) {
        projectPbsService.movePbs(projectPbs, false);
        return Result.OK("操作成功!");
    }

    /**
     * 添加
     *
     * @param projectPbs
     * @return
     */
    @AutoLog(value = "project_pbs-添加")
    @ApiOperation(value = "project_pbs-添加", notes = "project_pbs-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody ProjectPbs projectPbs) {
        // 添加记录的时候 不信任前端传入的 layer值,layer只通过pid对应元素的layer来决定
        QueryWrapper<ProjectPbs> queryWrapper = new QueryWrapper<ProjectPbs>();
        queryWrapper.eq("code", projectPbs.getCode());
        List<ProjectPbs> projectPbsList = projectPbsService.list(queryWrapper);
        if (projectPbsList.size() > 0) {
            return Result.error("编码（字段名称）重复！");
        }
        // 查找父记录,取出父记录的layer + 1
        String pid = projectPbs.getPid();
        if (pid == null) {
            pid = IProjectPbsService.ROOT_PID_VALUE;
        }
        int layer = 0;
        if (!pid.equals(ROOT_PID_VALUE)) {
            layer = projectPbsService.getById(pid).getLayer() + 1; // parent是顶层 自身layer=0,否则 自身layer =parent.layer+1
        }
        // 根据pid查询列表
        projectPbsList = projectPbsService.list(new QueryWrapper<ProjectPbs>().eq("pid", pid));
        projectPbs.setLayer(layer);
        int maxSort = projectPbsList.stream().mapToInt(ProjectPbs::getSortNo).max().orElse(0);
        projectPbs.setSortNo(maxSort + 1);
        projectPbsService.addProjectPbs(projectPbs);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param projectPbs
     * @return
     */
    @AutoLog(value = "project_pbs-编辑")
    @ApiOperation(value = "project_pbs-编辑", notes = "project_pbs-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody ProjectPbs projectPbs) {
        // 这里不应信任前端传入的数据
        // 逻辑上禁止切换pid ,layer,sortNo 仅在添加时动态生成
        // pbs code禁止修改 name可修改
        // pid layer sortNo 字段应忽略
        ProjectPbs currentPbs = projectPbsService.getById(projectPbs.getId());
        if (currentPbs == null) {
            return Result.error(400, "该记录不存在");
        }
        projectPbs.setCode(currentPbs.getCode());
        projectPbs.setPid(currentPbs.getPid());
        projectPbs.setLayer(currentPbs.getLayer());
        projectPbs.setSortNo(currentPbs.getSortNo());
        projectPbsService.updateProjectPbs(projectPbs);
        return Result.OK("编辑成功!");
    }

    @AutoLog(value = "project_pbs-取出风机/升压站id树形结构")
    @ApiOperation(value = "project_pbs-取出风机/升压站id列表", notes = "project_pbs-取出风机/升压站id列表")
    @GetMapping(value = "/getFanStationList")
    public Result<?> getFanStationList() {
        LambdaQueryWrapper<ProjectPbs> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.and(q -> q.eq(ProjectPbs::getType, "B06A01A01").or().eq(ProjectPbs::getType, "B06A01A03"))
                .and(q -> q.isNull(ProjectPbs::getPid).or().eq(ProjectPbs::getPid, "0"));
        // 取出 B06A01A02 / B06A01A04 (风机/升压站) 且为父节点的记录
        List<ProjectPbs> projectPbsList = projectPbsService.list(queryWrapper);
        return Result.OK(projectPbsList);
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "project_pbs-通过id删除")
    @ApiOperation(value = "project_pbs-通过id删除", notes = "project_pbs-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        ProjectPbs projectPbs = projectPbsService.getById(id);
        if (projectPbs == null) {
            return Result.error("指定的PBS记录不存在!");
        }
        LambdaQueryWrapper<ProjectPbs> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectPbs::getPid, id);
        List<ProjectPbs> projectPbsList = projectPbsService.list(queryWrapper);
        if (projectPbsList.size() > 0) {
            return Result.error("存在下级节点,不能删除!");
        }
        queryWrapper.clear();
        queryWrapper.eq(ProjectPbs::getStartSection, id).or().eq(ProjectPbs::getEndSection, id);
        projectPbsList = projectPbsService.list(queryWrapper);
        if (projectPbsList.size() > 0) {
            return Result.error("请先删除相关联的海缆!");
        }
        projectPbsService.deleteProjectPbs(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "project_pbs-批量删除")
    @ApiOperation(value = "project_pbs-批量删除", notes = "project_pbs-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.projectPbsService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "project_pbs-通过id查询")
    @ApiOperation(value = "project_pbs-通过id查询", notes = "project_pbs-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        ProjectPbs projectPbs = projectPbsService.getById(id);
        if (projectPbs == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(projectPbs);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param projectPbs
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ProjectPbs projectPbs) {
        return super.exportXls(request, projectPbs, ProjectPbs.class, "project_pbs");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ProjectPbs.class);
    }

    @AutoLog(value = "project_pbs-获取底图数据")
    @ApiOperation(value = "project_pbs-获取底图数据", notes = "project_pbs-获取底图数据")

    @RequestMapping(value = "/getBaseMapData", method = RequestMethod.GET)
    public Result<?> getBaseMapData() {

        return projectPbsService.getBaseMapData();
    }
}
