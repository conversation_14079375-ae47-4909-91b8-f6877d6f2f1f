package com.jinghe.breeze.modules.project.service.impl;

import com.jinghe.breeze.modules.project.entity.ProjectEquipment;
import com.jinghe.breeze.modules.project.mapper.ProjectEquipmentMapper;
import com.jinghe.breeze.modules.project.service.IProjectEquipmentService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: project_equipment
 * @Author: jeecg-boot
 * @Date:   2024-04-17
 * @Version: V1.0
 */
@Service
public class ProjectEquipmentServiceImpl extends ServiceImpl<ProjectEquipmentMapper, ProjectEquipment> implements IProjectEquipmentService {

}
