package com.jinghe.breeze.modules.sailingcheck.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghe.breeze.modules.sailingcheck.entity.UserBoardRecord;
import org.jeecg.common.api.vo.Result;

/**
 * @Description: user_board_record
 * @Author: jeecg-boot
 * @Date:   2024-06-18
 * @Version: V1.0
 */
public interface IUserBoardRecordService extends IService<UserBoardRecord> {

    Result<?> statistics();

    Result<?> queryPageList(Integer pageNo, Integer pageSize, String startDate, String endDate);
}
