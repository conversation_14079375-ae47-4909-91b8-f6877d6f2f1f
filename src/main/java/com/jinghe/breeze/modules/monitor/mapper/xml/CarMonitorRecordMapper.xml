<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghe.breeze.modules.monitor.mapper.CarMonitorRecordMapper">

    <select id="statisticsByHalfMonth" resultType="com.jinghe.breeze.modules.monitor.vo.DataObjectVo">
        SELECT
            DATE( record_time ) AS day,
            action,
            COUNT( DISTINCT license_plate ) AS count
        FROM
            car_monitor_records
        WHERE
            record_time >= CURDATE() - INTERVAL 14 DAY
          AND del_flag = 0
        GROUP BY
            DATE( record_time ),action
        ORDER BY
            day
    </select>
    <select id="statisticsByWeek" resultType="com.jinghe.breeze.modules.monitor.vo.DataObjectVo">
        SELECT
            DATE( record_time ) AS day,
            COUNT( DISTINCT license_plate ) AS count
        FROM
            car_monitor_records
        WHERE
            record_time >= CURDATE() - INTERVAL 6 DAY
          AND del_flag = 0 AND device_id = #{gateId}
        GROUP BY
            DATE( record_time )
        ORDER BY
            day
    </select>
    <select id="getAttendanceRecords" resultType="com.jinghe.breeze.modules.monitor.vo.MonitorRecordPageVo">
        SELECT
            license_plate AS licensePlate,
            file AS path,
            MIN( CASE WHEN action = 'in' THEN record_time END ) AS inTime,
            MAX( CASE WHEN action = 'out' THEN record_time END ) AS outTime
        FROM
            car_monitor_records
        WHERE
            DATE( record_time ) = CURDATE() AND
            del_flag = 0 AND device_id = #{gateId}
        GROUP BY
            license_plate,
            DATE( record_time )
        ORDER BY
            record_time DESC
    </select>
    <select id="carTotalCount" resultType="com.jinghe.breeze.modules.monitor.vo.DataObjectVo">
        SELECT
            DATE( record_time ) AS day,
            COUNT( DISTINCT license_plate ) AS count
        FROM
            car_monitor_records
        WHERE
            DATE( record_time ) = CURDATE()
          AND del_flag = 0
        GROUP BY
            DATE( record_time )
        ORDER BY
            day;
    </select>
</mapper>
