package com.jinghe.breeze.modules.project.service.impl;

import com.jinghe.breeze.modules.project.entity.ProjectCamera;
import com.jinghe.breeze.modules.project.mapper.ProjectCameraMapper;
import com.jinghe.breeze.modules.project.service.IProjectCameraService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: project_camera
 * @Author: jeecg-boot
 * @Date:   2024-04-17
 * @Version: V1.0
 */
@Service
public class ProjectCameraServiceImpl extends ServiceImpl<ProjectCameraMapper, ProjectCamera> implements IProjectCameraService {

}
