package com.jinghe.breeze.modules.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jinghe.breeze.modules.system.entity.CheckVo;
import org.jeecg.modules.system.entity.SysDict;

public interface ProjectSysDictMapper extends BaseMapper<SysDict> {

    Long duplicateCheckCountSql(CheckVo duplicateCheckVo);

    Long duplicateCheckCountSqlNoDataId(CheckVo duplicateCheckVo);

}
