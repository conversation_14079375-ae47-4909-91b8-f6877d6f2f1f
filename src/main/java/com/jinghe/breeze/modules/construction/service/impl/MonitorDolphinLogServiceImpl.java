package com.jinghe.breeze.modules.construction.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.common.constants.enums.VideoFolderEnum;
import com.jinghe.breeze.modules.construction.entity.MonitorDolphinLog;
import com.jinghe.breeze.modules.construction.entity.MonitorDolphinLogDetail;
import com.jinghe.breeze.modules.construction.mapper.MonitorDolphinLogMapper;
import com.jinghe.breeze.modules.construction.service.IMonitorDolphinLogDetailService;
import com.jinghe.breeze.modules.construction.service.IMonitorDolphinLogService;
import com.jinghe.breeze.modules.sailingcheck.entity.ShipSailingRecord;
import com.jinghe.breeze.modules.sailingcheck.entity.UserSailingRecord;
import com.jinghe.breeze.modules.sailingcheck.service.IShipSailingRecordService;
import com.jinghe.breeze.modules.sailingcheck.service.IUserSailingRecordService;
import com.jinghe.breeze.modules.ship.entity.ShipAlarmDetail;
import com.jinghe.breeze.modules.ship.entity.ShipInfo;
import com.jinghe.breeze.modules.ship.mapper.ShipAlarmListMapper;
import com.jinghe.breeze.modules.ship.mapper.ShipInfoMapper;
import com.jinghe.breeze.modules.video.service.IVideoInfoService;
import jodd.util.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 观豚日志
 * @Author: jeecg-boot
 * @Date: 2024-05-11
 * @Version: V1.0
 */
@Service
public class MonitorDolphinLogServiceImpl extends ServiceImpl<MonitorDolphinLogMapper, MonitorDolphinLog>
        implements IMonitorDolphinLogService {

    @Autowired
    private IMonitorDolphinLogDetailService monitorDolphinLogDetailService;
    @Autowired
    private IVideoInfoService videoInfoService;
    @Autowired
    private IUserSailingRecordService userSailingRecordService;
    @Autowired
    private IShipSailingRecordService shipSailingRecordService;
    @Autowired
    private ShipAlarmListMapper shipAlarmListMapper;
    @Autowired
    private ShipInfoMapper shipInfoMapper;

    /**
     * 将观测日期(年月日) 和 日志详情的时间戳(时分秒部分)合并为新的时间戳,
     *
     * @param date
     * @param timestamp
     * @return
     */
    public int combineDateAndTime(Date date, long timestamp) {
        Calendar dateCalendar = Calendar.getInstance();
        dateCalendar.setTime(date);

        Calendar timeCalendar = Calendar.getInstance();
        timeCalendar.setTimeInMillis(timestamp * 1000);

        dateCalendar.set(Calendar.HOUR_OF_DAY, timeCalendar.get(Calendar.HOUR_OF_DAY));
        dateCalendar.set(Calendar.MINUTE, timeCalendar.get(Calendar.MINUTE));
        dateCalendar.set(Calendar.SECOND, timeCalendar.get(Calendar.SECOND));
        dateCalendar.set(Calendar.MILLISECOND, timeCalendar.get(Calendar.MILLISECOND));

        return (int) (dateCalendar.getTimeInMillis() / 1000);
    }

    /**
     * 更新一个日志的,所有详情中的时间错
     *
     * @param monitorDolphinLog
     */
    public void updateTimestamp(MonitorDolphinLog monitorDolphinLog, MonitorDolphinLogDetail detail) {
        int startTimestamp = combineDateAndTime(monitorDolphinLog.getWatchDate(), detail.getStartDate());
        int endTimestamp = combineDateAndTime(monitorDolphinLog.getWatchDate(), detail.getEndDate());
        detail.setStartDate(startTimestamp);
        detail.setEndDate(endTimestamp);
    }

    @Transactional(rollbackFor = Exception.class)
    public Result<?> editLog(MonitorDolphinLog monitorDolphinLog) {
        for (MonitorDolphinLogDetail detail : monitorDolphinLog.getDetails()) {
            if (!StringUtils.isEmpty(detail.getPosition()) && !validatePosition(detail.getPosition())) {
                return Result.error("坐标格式不正确,请检查");
            }
        }
        MonitorDolphinLog oldMonitorDolphinLog = baseMapper.selectById(monitorDolphinLog.getId());
        if (oldMonitorDolphinLog == null) {
            return Result.error("未找到指定记录");
        }
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String username = sysUser.getUsername();
        if (!ObjectUtil.equal(oldMonitorDolphinLog.getCreateBy(), username)) {
            return Result.error("不能编辑他人的记录");
        }

        MonitorDolphinLog sameDateEntity = baseMapper.selectOne(new LambdaQueryWrapper<MonitorDolphinLog>()
                .eq(MonitorDolphinLog::getCreateBy, username)
                .ne(MonitorDolphinLog::getId, oldMonitorDolphinLog.getId())
                .eq(MonitorDolphinLog::getWatchDate, monitorDolphinLog.getWatchDate())
                .last("limit 1"));
        if (sameDateEntity != null) {
            return Result.error("填报的日期,已经存在记录,请勿重复填报");
        }
        baseMapper.updateById(monitorDolphinLog);
        String id = monitorDolphinLog.getId();
        List<String> newFileStringList = monitorDolphinLog.getDetails().stream()
                .map(MonitorDolphinLogDetail::getPictures).collect(Collectors.toList());
        List<MonitorDolphinLogDetail> detailList = monitorDolphinLogDetailService
                .list(new LambdaQueryWrapper<MonitorDolphinLogDetail>()
                        .eq(MonitorDolphinLogDetail::getWatchId, id));// 当前的detail列表
        List<String> oldFileStringList = detailList.stream().map(MonitorDolphinLogDetail::getPictures)
                .collect(Collectors.toList());
        if (Objects.equals(0, monitorDolphinLog.getExsitDolphin())) { // 全部删除的情形
            monitorDolphinLogDetailService.remove(new LambdaQueryWrapper<MonitorDolphinLogDetail>()
                    .eq(MonitorDolphinLogDetail::getWatchId, id)); // 改为不存在时,删除所有记录
            videoInfoService.updateVideoInfo(oldFileStringList, new ArrayList<>(), VideoFolderEnum.DOLPHIN.getId());
            return Result.OK("编辑成功!");
        }
        List<String> oldIdList = detailList.stream().map(MonitorDolphinLogDetail::getId).collect(Collectors.toList());
        List<String> newIdList = monitorDolphinLog.getDetails().stream().map(MonitorDolphinLogDetail::getId)
                .collect(Collectors.toList());
        List<String> toRemoveList = new ArrayList<>(CollectionUtils.subtract(oldIdList, newIdList));
        List<String> toAddList = new ArrayList<>(CollectionUtils.subtract(newIdList, oldIdList));
        List<String> toMonifyList = new ArrayList<>(CollectionUtils.intersection(oldIdList, newIdList));
        monitorDolphinLogDetailService.removeByIds(toRemoveList);
        for (MonitorDolphinLogDetail logDetail : monitorDolphinLog.getDetails()) {
            if (toMonifyList.contains(logDetail.getId())) {
                updateTimestamp(monitorDolphinLog, logDetail); // 合并日期
                monitorDolphinLogDetailService.updateById(logDetail);
                continue;
            }
            if (toAddList.contains(logDetail.getId())) {
                updateTimestamp(monitorDolphinLog, logDetail);
                logDetail.setWatchId(id);
                monitorDolphinLogDetailService.save(logDetail);
            }
        }
        videoInfoService.updateVideoInfo(oldFileStringList, newFileStringList, VideoFolderEnum.DOLPHIN.getId());
        return Result.OK("编辑成功!");
    }

    public Result<?> getSaillingShipName(String dateStr) {
        // SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        // Date date = null;
        // try {
        // date = sdf.parse(dateStr);
        // } catch (Exception e) {
        // return Result.error("日期格式不正确");
        // }
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // String userid = sysUser.getId();
        // .ge(UserSailingRecord::getCreateTime, sdf.format(date) + " 00:00:00")
        // .le(UserSailingRecord::getCreateTime, sdf.format(date) + " 23:59:59")
        UserSailingRecord userSailingRecord = userSailingRecordService
                .getOne(new LambdaQueryWrapper<UserSailingRecord>()
                        .eq(UserSailingRecord::getUserName, sysUser.getUsername())
                        .eq(UserSailingRecord::getStatus, Common.commonality.ONE)
                        .orderByDesc(UserSailingRecord::getCreateTime)
                        .last("limit 1"), false);
        if (userSailingRecord == null) {
            return Result.error("未获取到船只");
        }
        String baseId = userSailingRecord.getBaseId();
        if (StringUtil.isEmpty(baseId)) {
            return Result.error("未获取到船只");
        }
        ShipSailingRecord shipSailingRecord = shipSailingRecordService
                .getOne(new LambdaQueryWrapper<ShipSailingRecord>()
                        .eq(ShipSailingRecord::getId, baseId), false);
        if (shipSailingRecord == null) {
            return Result.error("未获取到船只");
        }
        Map<String, String> map = new HashMap<>();
        map.put("shipName", shipSailingRecord.getShipName());
        map.put("shipId", shipSailingRecord.getShipId());
        return Result.OK(map);
    }

    private boolean validatePosition(String position) {
        // postion为空则返回true, 否则必须为两个小数用逗号隔开的形式
        position = position.replace(" ", "");
        if (StringUtils.isEmpty(position)) {
            return true;
        }
        String[] split = position.split(",");
        if (split.length != 2) {
            return false;
        }
        for (String s : split) {
            try {
                Float.parseFloat(s);
            } catch (Exception e) {
                return false;
            }
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> addLog(MonitorDolphinLog monitorDolphinLog) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String username = sysUser.getUsername();
        // 查询本 当前用户 今天的填报记录数
        LambdaQueryWrapper<MonitorDolphinLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MonitorDolphinLog::getCreateBy, username).eq(MonitorDolphinLog::getWatchDate,
                monitorDolphinLog.getWatchDate());
        int count = baseMapper.selectCount(wrapper);
        if (count > 0) {
            return Result.error("当前日期已有重复日志");
        }
        Integer existDolphin = monitorDolphinLog.getExsitDolphin();
        super.save(monitorDolphinLog);
        if (existDolphin.equals(0)) {
            return Result.OK();
        }
        String watchId = monitorDolphinLog.getId();
        List<String> toAddFileStringList = new ArrayList<>();
        for (MonitorDolphinLogDetail logDetail : monitorDolphinLog.getDetails()) {
            String postion = logDetail.getPosition();
            if (!StringUtils.isEmpty(postion) && !validatePosition(postion)) {
                String errMsg = String.format("坐标`%s`格式不正确,正确格式:110.11,230.22", logDetail.getPosition());
                return Result.error(errMsg);
            }
            //没有填位置 默认查询位置
            if (StringUtils.isEmpty(postion)) {
                ShipInfo shipInfo = shipInfoMapper.selectById(monitorDolphinLog.getShipId());

                ShipAlarmDetail shipAlarmDetail = shipAlarmListMapper.selectByCreateDate(logDetail.getCreateTime(),shipInfo.getMmsi());
                if (!StringUtils.isEmpty(shipAlarmDetail)) {
                    logDetail.setPosition(shipAlarmDetail.getLon() + "," + shipAlarmDetail.getLat());
                }
            }

            int startTimestamp = combineDateAndTime(monitorDolphinLog.getWatchDate(),
                    logDetail.getStartDate());
            int endTimestamp = combineDateAndTime(monitorDolphinLog.getWatchDate(),
                    logDetail.getEndDate());
            logDetail.setStartDate(startTimestamp);
            logDetail.setEndDate(endTimestamp);
            logDetail.setWatchId(watchId);
            String pictures = logDetail.getPictures();
            toAddFileStringList.add(pictures);
            monitorDolphinLogDetailService.save(logDetail);
        }
        videoInfoService.updateVideoInfo(new ArrayList<>(), toAddFileStringList, VideoFolderEnum.DOLPHIN.getId());
        return Result.OK();
    }

    public List<MonitorDolphinLog> queryByMonth(String monthString) {
        List<MonitorDolphinLog> list = baseMapper.selectList(new LambdaQueryWrapper<MonitorDolphinLog>()
                .like(MonitorDolphinLog::getWatchDate, monthString));
        list.forEach(monitorDolphinLog -> {
            List<MonitorDolphinLogDetail> detailList = monitorDolphinLogDetailService
                    .list(new LambdaQueryWrapper<MonitorDolphinLogDetail>()
                            .eq(MonitorDolphinLogDetail::getWatchId, monitorDolphinLog.getId())
                            .eq(MonitorDolphinLogDetail::getDelFlag, Common.delete_flag.OK));
            monitorDolphinLog.setDetails(detailList);
        });
        return list;
        // //由于APP端 detail中坐标不是只读,需要做过滤
        // for (MonitorDolphinLog log : list) {
        // List<MonitorDolphinLogDetail> details = log.getDetails();
        // details = details.stream().filter(detail -> {
        // String position = detail.getPosition();
        // return !StringUtils.isEmpty(position) && validatePosition(position);
        // }).collect(Collectors.toList());
        // log.setDetails(details);
        // }
        // return list;
    }

}
