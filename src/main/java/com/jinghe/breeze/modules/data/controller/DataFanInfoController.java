package com.jinghe.breeze.modules.data.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.jinghe.breeze.modules.data.entity.DataFanInfo;
import com.jinghe.breeze.modules.data.service.IDataFanInfoService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: data_fan_info
 * @Author: jeecg-boot
 * @Date: 2024-05-17
 * @Version: V1.0
 */
@Api(tags = "data_fan_info")
@RestController
@RequestMapping("/data/dataFanInfo")
@Slf4j
public class DataFanInfoController extends JeecgController<DataFanInfo, IDataFanInfoService> {
    @Autowired
    private IDataFanInfoService dataFanInfoService;

    /**
     * 分页列表查询
     *
     * @param dataFanInfo
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "data_fan_info-分页列表查询")
    @ApiOperation(value = "data_fan_info-分页列表查询", notes = "data_fan_info-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(DataFanInfo dataFanInfo,
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            HttpServletRequest req) {
        QueryWrapper<DataFanInfo> queryWrapper = QueryGenerator.initQueryWrapper(dataFanInfo, req.getParameterMap());
        Page<DataFanInfo> page = new Page<DataFanInfo>(pageNo, pageSize);
        IPage<DataFanInfo> pageList = dataFanInfoService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param dataFanInfo
     * @return
     */
    @AutoLog(value = "data_fan_info-添加")
    @ApiOperation(value = "data_fan_info-添加", notes = "data_fan_info-添加")
    @RequiresPermissions("dataFanInfo:add")
    @PostMapping(value = "/add")
    public Result<?> add(@Validated @RequestBody DataFanInfo dataFanInfo) {
        // public Result<?> add(@RequestBody DataFanInfo dataFanInfo) {
        dataFanInfoService.save(dataFanInfo);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param dataFanInfo
     * @return
     */
    @AutoLog(value = "data_fan_info-编辑")
    @ApiOperation(value = "data_fan_info-编辑", notes = "data_fan_info-编辑")
    @RequiresPermissions("dataFanInfo:edit")
    @PutMapping(value = "/edit")
    public Result<?> edit(@Validated @RequestBody DataFanInfo dataFanInfo) {
        dataFanInfoService.updateById(dataFanInfo);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "data_fan_info-通过id删除")
    @ApiOperation(value = "data_fan_info-通过id删除", notes = "data_fan_info-通过id删除")
    @RequiresPermissions("dataFanInfo:delete")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        dataFanInfoService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "data_fan_info-批量删除")
    @ApiOperation(value = "data_fan_info-批量删除", notes = "data_fan_info-批量删除")
    @RequiresPermissions("dataFanInfo:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.dataFanInfoService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "data_fan_info-通过id查询")
    @ApiOperation(value = "data_fan_info-通过id查询", notes = "data_fan_info-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        DataFanInfo dataFanInfo = dataFanInfoService.getById(id);
        if (dataFanInfo == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(dataFanInfo);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param dataFanInfo
     */
    @RequiresPermissions("dataFanInfo:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DataFanInfo dataFanInfo) {
        return super.exportXls(request, dataFanInfo, DataFanInfo.class, "data_fan_info");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("dataFanInfo:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DataFanInfo.class);
    }

}
