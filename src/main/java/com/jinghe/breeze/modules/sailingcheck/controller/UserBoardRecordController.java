package com.jinghe.breeze.modules.sailingcheck.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.modules.sailingcheck.entity.UserBoardRecord;
import com.jinghe.breeze.modules.sailingcheck.service.IUserBoardRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

@Api(tags="上下船记录")
@RestController
@RequestMapping("/sailingcheck/userBoardRecord")
@Slf4j
public class UserBoardRecordController {

    @Autowired
    private IUserBoardRecordService iService;

    @AutoLog(value = "分页列表查询")
    @ApiOperation(value="分页列表查询", notes="分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                   @RequestParam(name = "startDate") String startDate,
                                   @RequestParam(name = "endDate") String endDate) {
        return iService.queryPageList(pageNo,pageSize,startDate,endDate);
    }

    @AutoLog(value = "个人通行记录7天")
    @ApiOperation(value="个人通行记录7天", notes="个人通行记录7天")
    @GetMapping(value = "/myHis")
    public Result<?> myHis(HttpServletRequest req) {
        LambdaQueryWrapper<UserBoardRecord> queryWrapper = new LambdaQueryWrapper<>();
        String userName = JwtUtil.getUserNameByToken(req);
        queryWrapper.eq(UserBoardRecord::getUserName, userName);
        queryWrapper.gt(UserBoardRecord::getCreateTime, new Date(System.currentTimeMillis()-86400000*7));
        queryWrapper.orderByDesc(UserBoardRecord::getCreateTime);
        List<UserBoardRecord> list = iService.list(queryWrapper);
        return Result.OK(list);
    }

    @AutoLog(value = "出航次数")
    @ApiOperation(value="出航次数", notes="出航次数")
    @GetMapping(value = "/count")
    public Result<?> count(HttpServletRequest req) {
        LambdaQueryWrapper<UserBoardRecord> queryWrapper = new LambdaQueryWrapper<>();
        String userName = JwtUtil.getUserNameByToken(req);
        queryWrapper.eq(UserBoardRecord::getUserName, userName);
        queryWrapper.gt(UserBoardRecord::getCreateTime, new Date(System.currentTimeMillis()-86400000*7));
        queryWrapper.eq(UserBoardRecord::getType, Common.commonality.ONE);
        int count = iService.count(queryWrapper);
        return Result.OK(count);
    }
    @AutoLog(value = "出海记录统计")
    @ApiOperation(value = "出海记录统计", notes = "出海记录统计")
    @GetMapping(value = "/statistics")
    public Result<?> statistics() {
        return iService.statistics();
    }
}
