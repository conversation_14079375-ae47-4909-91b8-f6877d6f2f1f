package com.jinghe.breeze.modules.staff.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
 * @Description: 人员考勤
 * @Author: jeecg-boot
 * @Date: 2024-11-04
 * @Version: V1.0
 */
@Data
@TableName("staff_attendance")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "staff_attendance对象", description = "项目人员")
public class StaffAttendance implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
    /**
     * 删除状态（0，正常，1已删除）
     */
    @ApiModelProperty(value = "删除状态（0，正常，1已删除）")
    private String delFlag;
    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;
    /**
     * 考勤日期
     */
    @ApiModelProperty(value = "考勤日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date clockDate;
    /**
     * 第一次打卡时间（上班时间）
     */
    @ApiModelProperty(value = "第一次打卡时间（上班时间）")
    private String firstTime;

    /**
     * 最后一次打卡时间（下班时间）
     */
    @ApiModelProperty(value = "最后一次打卡时间（下班时间）")
    private String lastTime;

    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
    private String identityCard;
    /**
     * 人员姓名
     */
    @ApiModelProperty(value = "人员姓名")
    private String name;
    /**
     * 分包商名
     */
    @ApiModelProperty(value = "分包商名")
    private String subcontractorName;
    /**
     * 上班考勤照片
     */
    @ApiModelProperty(value = "上班考勤照片")
    private String clockInPicture;
    /**
     * 下班考勤照片
     */
    @ApiModelProperty(value = "下班考勤照片")
    private String clockOutPicture;
}
