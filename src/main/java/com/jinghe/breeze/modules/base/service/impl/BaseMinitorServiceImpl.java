package com.jinghe.breeze.modules.base.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghe.breeze.modules.base.entity.BaseMinitor;
import com.jinghe.breeze.modules.base.mapper.BaseMinitorMapper;
import com.jinghe.breeze.modules.base.service.IBaseMinitorService;
import org.springframework.stereotype.Service;

/**
 * @Description: 监控设备
 * @Author: jeecg-boot
 * @Date: 2024-08-06
 * @Version: V1.0
 */
@Service
public class BaseMinitorServiceImpl extends ServiceImpl<BaseMinitorMapper, BaseMinitor> implements IBaseMinitorService {

}
