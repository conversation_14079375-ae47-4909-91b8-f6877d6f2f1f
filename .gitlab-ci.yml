image: maven:3.6.3-jdk-8

variables:
  MAVEN_OPTS: "-Dmaven.repo.local=/cache/maven.repository"

stages:
  - build
  - deploy

cache:
  key:
    files:
      - pom.xml
  paths:
    - /root/.m2/repository

build:
  only:
    - test
  stage: build
  tags:
    - java
  script:
    - echo "开始打包部署2"
    - echo $CI_BRANCH
    - echo "$CI_BRANCH"
    - mvn clean package
    - echo "打包部署结束"
  artifacts:
    paths:
      - ./target/jinghe-amlf-1.0.0.jar


deploy:
  only:
    - test
  stage: deploy
  tags:
    - java
  before_script:
    # - 'command -v ssh-agent >/dev/null || ( apt-get update -y && apt-get install openssh-client -y )'
    - 'which ssh-agent || (yum update update -y && yum install openssh-client git -y )'
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh

    - ssh-keyscan ************** >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    - echo "拷贝jar开始"
    - scp target/jinghe-amlf-1.0.0.jar root@**************:/data/data2/app/amlf/api/
    - echo "拷贝jar结束"
    #- echo "构建镜像开始"
    #- ssh -tt  root@************** "cd /data/data2/app/mdg/api; docker build -t mdgapi ."
    #- echo "构建镜像结束"
    - echo "重新启动容器----------"
    - ssh -tt root@************** "docker restart amlf-api"
    #- ssh -tt root@************** "if $(docker ps -q -f name=mdg-api) ; then docker restart mdg-api; fi"


    # 此处直接用ssh命令，并且将后面要重启jar包的命令放在和ssh同一行执行
