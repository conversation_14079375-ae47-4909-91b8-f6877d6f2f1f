package com.jinghe.breeze.modules.settlement.controller;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.modules.settlement.entity.ContractInfo;
import com.jinghe.breeze.modules.settlement.entity.ContractInfoDetail;
import com.jinghe.breeze.modules.settlement.service.IContractInfoDetailService;
import com.jinghe.breeze.modules.settlement.service.IContractInfoService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.jinghe.breeze.modules.settlement.entity.Settlement;
import com.jinghe.breeze.modules.settlement.service.ISettlementService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: settlement
 * @Author: jeecg-boot
 * @Date: 2024-06-03
 * @Version: V1.0
 */
@Api(tags = "settlement")
@RestController
@RequestMapping("/settlement/settlement")
@Slf4j
public class SettlementController extends JeecgController<Settlement, ISettlementService> {
    @Autowired
    private ISettlementService settlementService;

    @Autowired
    private IContractInfoService contractInfoService;

    @Autowired
    private IContractInfoDetailService contractInfoDetailService;

    /**
     * 分页列表查询
     *
     * @param settlement
     * @param pageNo
     * @param pageSize
     * @param startTime
     * @param endTime
     * @param contractName
     * @return
     */
    @AutoLog(value = "settlement-分页列表查询")
    @ApiOperation(value = "settlement-分页列表查询", notes = "settlement-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(Settlement settlement,
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(name = "startTime", required = false) String startTime,
            @RequestParam(name = "endTime", required = false) String endTime,
            @RequestParam(name = "contractName", required = false) String contractName,
            HttpServletRequest req) {
        QueryWrapper<Settlement> queryWrapper = QueryGenerator.initQueryWrapper(settlement, req.getParameterMap());
        queryWrapper.groupBy("batch");
        SimpleDateFormat sdf = new SimpleDateFormat(Common.date.SECONDS);
        if (oConvertUtils.isNotEmpty(startTime)) {
            try {
                Date start = sdf.parse(startTime + " 00:00:00");
                Date end = sdf.parse(endTime + " 23:59:59");
                queryWrapper.between("settle_date", start, end);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        } // 项目经理要求 合同名称变更之后,按最新的合同名称搜索
        Page<Settlement> page = new Page<Settlement>(pageNo, pageSize);
        IPage<Settlement> pageList = settlementService.page(page, queryWrapper);
        List<Settlement> settleList = pageList.getRecords();
        settlementService.addContractInfomation(settleList);
        if (!oConvertUtils.isEmpty(contractName)) {
            // 查询参数中有合同名称 ,过滤保留包含合同名称的
            settleList = settleList.stream().filter(p -> {
                ContractInfo info = p.getContractInfo();
                return info == null || info.getName().contains(contractName);
            }).collect(Collectors.toList());
        }
        pageList.setRecords(settleList);
        return Result.OK(pageList);
    }

    /**
     * 结算登记页面 编辑时的搜索接口
     *
     * @param settlement
     * @return
     */

    @AutoLog(value = "contract_info_detail-获取结算信息")
    @ApiOperation(value = "contract_info_detail-获取结算信息", notes = "contract_info_detail-获取结算信息")
    @GetMapping(value = "/queryContractInfoSettlement")
    public Result<?> queryContractInfoSettlement(Settlement settlement, String name) {
        String contractId = settlement.getContractId();
        String code = settlement.getCode();
        String batch = settlement.getBatch();
        List<ContractInfoDetail> details = contractInfoDetailService.queryContractInfoSettlement(contractId, code,
                batch, name);
        return Result.OK(details);
    }

    /**
     * 添加
     *
     * @param settlements
     * @return
     */
    @AutoLog(value = "settlement-添加")
    @ApiOperation(value = "settlement-添加", notes = "settlement-添加")
    @RequiresPermissions("settlement:add")
    @PostMapping(value = "/add")
    public Result<?> add(@Validated @RequestBody List<Settlement> settlements) {
        settlementService.addSettlement(settlements);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param settlements
     * @return
     */
    @AutoLog(value = "settlement-编辑")
    @ApiOperation(value = "settlement-编辑", notes = "settlement-编辑")
    @RequiresPermissions("settlement:edit")
    @Transactional(rollbackFor = Exception.class)
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestParam(name = "batch", required = true) String batch,
            @RequestParam(name = "contractId", required = true) String contractId,
            @Validated @RequestBody List<Settlement> settlements) {
        settlementService.saveInfo(settlements, batch, contractId);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param batch
     * @return
     */
    @AutoLog(value = "settlement-通过id删除")
    @ApiOperation(value = "settlement-通过id删除", notes = "settlement-通过id删除")
    @RequiresPermissions("settlement:delete")
    @DeleteMapping(value = "/delete")
    @Transactional(rollbackFor = Exception.class)
    public Result<?> delete(@RequestParam(name = "batch", required = true) String batch) {
        List<Settlement> settleList = settlementService.list(new LambdaQueryWrapper<Settlement>()
                .eq(Settlement::getBatch, batch));
        List<ContractInfoDetail> details = settleList.stream()
                .map(p -> contractInfoDetailService.getById(p.getContractInfoDetailId()))
                .collect(Collectors.toList());
        List<String> idList = settleList.stream().map(Settlement::getId).collect(Collectors.toList());
        settlementService.removeByIds(idList);
        contractInfoDetailService.setSettledInfo(details);// 更新已结算信息
        return Result.OK("删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "settlement-通过id查询")
    @ApiOperation(value = "settlement-通过id查询", notes = "settlement-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        Settlement settlement = settlementService.getById(id);
        if (settlement == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(settlement);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param settlement
     */
    @RequiresPermissions("settlement:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Settlement settlement) {
        return super.exportXls(request, settlement, Settlement.class, "settlement");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("settlement:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Settlement.class);
    }

    @GetMapping(value = "/getTreeData")
    public Result<?> getTreeData(@RequestParam(name = "contractId", required = true) String contractId) {
        return Result.OK(settlementService.getTreeData(contractId));
    }

    @GetMapping(value = "/getBatchSettlementInfo")
    public Result<?> getBatchSettlementInfo(@RequestParam(name = "contractId", required = true) String contractId,
            @RequestParam(name = "batch", required = true) String batch) {
        return Result.OK(settlementService.getBatchSettlementInfo(contractId, batch));
    }
}
