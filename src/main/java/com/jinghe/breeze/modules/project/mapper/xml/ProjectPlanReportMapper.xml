<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghe.breeze.modules.project.mapper.ProjectPlanReportMapper">

    <update id="updateByCode">
      update construction_plan_report
        set status = #{status}
        <if test="actualEndDate!=null">
            ,actual_end_date=#{actualEndDate}
        </if>
        <if test="actualEndDate==null">
            ,actual_end_date=null
        </if>
        <if test="actualStartDate!=null">
            ,actual_start_date=#{actualStartDate}
        </if>
        <if test="actualNum!=null">
            ,actual_num=#{actualNum}
        </if>
        where base_code = #{pcode}
    </update>
</mapper>