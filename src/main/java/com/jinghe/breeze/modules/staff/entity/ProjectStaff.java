package com.jinghe.breeze.modules.staff.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
 * @Description: 项目人员
 * @Author: jeecg-boot
 * @Date: 2024-11-04
 * @Version: V1.0
 */
@Data
@TableName("project_staff")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "project_staff对象", description = "项目人员")
public class ProjectStaff implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
    /**
     * 删除状态（0，正常，1已删除）
     */
    @ApiModelProperty(value = "删除状态（0，正常，1已删除）")
    private String delFlag;
    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;
    /**
     * 人员姓名
     */
    @ApiModelProperty(value = "员工ID")
    private String staffId;
    /**
     * 人员姓名
     */
    @ApiModelProperty(value = "人员姓名")
    private String name;
    /**
     * 人员证件号码
     */
    @ApiModelProperty(value = "人员证件号码")
    private String idCard;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String sexName;

    /**
     * 所属单位
     */
    @ApiModelProperty(value = "所属单位")
    private String subName;
    /**
     * 人员状态
     */
    @ApiModelProperty(value = "人员状态")
    private String curState;
    /**
     * 进场时间
     */
    @ApiModelProperty(value = "进场时间")
    private String comingDate;
    /**
     * 工种
     */
    @ApiModelProperty(value = "工种")
    private String positionName;
    /**
     * 身份证照片
     */
    @ApiModelProperty(value = "身份证照片")
    private String pictureIdentity;
    /**
     * 现场采集照片
     */
    @ApiModelProperty(value = "现场采集照片")
    private String pictureScene;
    /**
     * 退场时间
     */
    @ApiModelProperty(value = "退场时间")
    private String outDate;
    /**
     * 民族
     */
    @ApiModelProperty(value = "民族")
    private String ethnicGroup;
    /**
     * 籍贯
     */
    @ApiModelProperty(value = "籍贯")
    private String province;

    /**
     * 住址
     */
    @ApiModelProperty(value = "住址")
    private String address;
}
