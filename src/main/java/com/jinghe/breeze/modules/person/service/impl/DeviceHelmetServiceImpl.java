package com.jinghe.breeze.modules.person.service.impl;

import com.jinghe.breeze.common.constants.enums.ProjectFensEnum;
import com.jinghe.breeze.common.enums.DelFlagEnum;
import com.jinghe.breeze.common.enums.SafeEducationStatusEnum;
import com.jinghe.breeze.modules.person.entity.DeviceHelmet;
import com.jinghe.breeze.modules.person.entity.PersonInfo;
import com.jinghe.breeze.modules.person.mapper.DeviceHelmetMapper;
import com.jinghe.breeze.modules.person.service.IDeviceHelmetService;
import com.jinghe.breeze.modules.person.service.IPersonInfoService;
import com.jinghe.breeze.modules.person.dto.LocationDataDTO;
import com.jinghe.breeze.modules.person.dto.EnrichedLocationDataDTO;
import com.jinghe.breeze.modules.project.entity.ProjectFence;
import com.jinghe.breeze.modules.project.service.IProjectFenceService;
import com.jinghe.breeze.modules.safety.entity.SafeViolationRecords;
import com.jinghe.breeze.modules.safety.service.ISafeViolationRecordsService;
import com.jinghe.breeze.common.utils.MapPoint;
import com.jinghe.breeze.common.utils.MapWktUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;
import java.util.ArrayList;
import java.util.Set;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

/**
 * @Description: 设备信息表
 * @Author: chenliang
 * @Date: 2022-08-09
 * @Version: V1.0
 */
@Slf4j
@Service
public class DeviceHelmetServiceImpl extends ServiceImpl<DeviceHelmetMapper, DeviceHelmet> implements IDeviceHelmetService {
    @Autowired
    private IPersonInfoService personInfoService;
    @Autowired
    private IProjectFenceService projectFenceService;
    @Autowired
    private ISafeViolationRecordsService safeViolationRecordsService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    private static final String REDIS_KEY_PREFIX = "deviceHelmet:location:";
    private static final long REDIS_EXPIRE_TIME = 600; // 10分钟

    // 围栏缓存相关
    private volatile List<String> cachedSafetyFences = null;  // 安全防护围栏缓存
    private volatile List<String> cachedConstructionFences = null;  // 施工围栏缓存
    private volatile long lastFenceUpdateTime = 0;
    private static final long FENCE_CACHE_EXPIRE_TIME = 300000; // 5分钟缓存过期时间

    @Override
    public void deviceBootable() {

    }

    /**
     * 获取缓存的围栏数据，如果缓存过期则重新加载
     */
    private void loadFenceDataIfNeeded() {
        long currentTime = System.currentTimeMillis();

        // 检查缓存是否过期
        if (cachedSafetyFences == null || cachedConstructionFences == null ||
            (currentTime - lastFenceUpdateTime) > FENCE_CACHE_EXPIRE_TIME) {
            synchronized (this) {
                // 双重检查锁定
                if (cachedSafetyFences == null || cachedConstructionFences == null ||
                    (currentTime - lastFenceUpdateTime) > FENCE_CACHE_EXPIRE_TIME) {
                    try {
                        // 获取安全防护围栏数据
                        List<ProjectFence> safetyFenceList = projectFenceService.list(new LambdaQueryWrapper<ProjectFence>()
                                .eq(ProjectFence::getDelFlag, DelFlagEnum.NORMAL.getType())
                                .eq(ProjectFence::getFenceType, ProjectFensEnum.YJ.getCode()));

                        cachedSafetyFences = safetyFenceList.stream()
                                .map(ProjectFence::getFenceRadius)
                                .filter(radius -> radius != null && !radius.trim().isEmpty())
                                .collect(Collectors.toList());

                        // 获取施工围栏数据
                        List<ProjectFence> constructionFenceList = projectFenceService.list(new LambdaQueryWrapper<ProjectFence>()
                                .eq(ProjectFence::getDelFlag, DelFlagEnum.NORMAL.getType())
                                .eq(ProjectFence::getFenceType, ProjectFensEnum.SG.getCode()));

                        cachedConstructionFences = constructionFenceList.stream()
                                .map(ProjectFence::getFenceRadius)
                                .filter(radius -> radius != null && !radius.trim().isEmpty())
                                .collect(Collectors.toList());

                        lastFenceUpdateTime = currentTime;

                        log.info("围栏数据已更新到缓存 - 安全围栏: {} 个, 施工围栏: {} 个",
                                cachedSafetyFences.size(), cachedConstructionFences.size());

                    } catch (Exception e) {
                        log.error("获取围栏数据失败: {}", e.getMessage(), e);
                        cachedSafetyFences = new ArrayList<>();
                        cachedConstructionFences = new ArrayList<>();
                    }
                }
            }
        }
    }

    /**
     * 检测位置是否在安全防护围栏内
     */
    private boolean checkLocationInSafetyFence(Double longitude, Double latitude) {
        if (longitude == null || latitude == null) {
            return false;
        }

        loadFenceDataIfNeeded();

        if (cachedSafetyFences == null || cachedSafetyFences.isEmpty()) {
            return false;
        }

        try {
            MapPoint currentPoint = new MapPoint(longitude, latitude);
            // 只要在任意一个安全围栏内就返回true
            for (String fenceData : cachedSafetyFences) {
                if (MapWktUtil.isPointInPolygon(currentPoint, fenceData)) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            log.error("安全围栏检测失败，经度: {}, 纬度: {}, 错误: {}", longitude, latitude, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检测位置是否在施工围栏内
     */
    private boolean checkLocationInWorkFence(Double longitude, Double latitude) {
        if (longitude == null || latitude == null) {
            return false;
        }

        loadFenceDataIfNeeded();

        if (cachedConstructionFences == null || cachedConstructionFences.isEmpty()) {
            return false;
        }

        try {
            MapPoint currentPoint = new MapPoint(longitude, latitude);
            // 只要在任意一个施工围栏内就返回true
            for (String fenceData : cachedConstructionFences) {
                if (MapWktUtil.isPointInPolygon(currentPoint, fenceData)) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            log.error("施工围栏检测失败，经度: {}, 纬度: {}, 错误: {}", longitude, latitude, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 手动刷新围栏缓存
     * 当围栏数据更新时可以调用此方法立即刷新缓存
     */
    public void refreshFenceCache() {
        synchronized (this) {
            cachedSafetyFences = null;
            cachedConstructionFences = null;
            lastFenceUpdateTime = 0;
            log.info("围栏缓存已手动清除，下次访问时将重新加载");
        }
        // 立即加载新的围栏数据
        loadFenceDataIfNeeded();
    }

    @Override
    public void receiveLocationData(List<LocationDataDTO> locationDataList) {
        log.info("接收到位置数据，数量: {}", locationDataList.size());

        for (LocationDataDTO locationData : locationDataList) {
            try {
                // 进行围栏检测
                boolean isInSafetyFence = checkLocationInSafetyFence(locationData.getLongitude(), locationData.getLatitude());
                boolean isInConstructionFence = checkLocationInWorkFence(locationData.getLongitude(), locationData.getLatitude());
                // 创建增强的位置数据对象
                EnrichedLocationDataDTO enrichedData = new EnrichedLocationDataDTO();
                BeanUtils.copyProperties(locationData, enrichedData);
                enrichedData.setIsInSafetyFence(isInSafetyFence);
                enrichedData.setIsInWorkFence(isInConstructionFence);

                String redisKey = REDIS_KEY_PREFIX + locationData.getPuname();
                // 存储增强的数据到Redis并设置过期时间
                redisUtil.set(redisKey, enrichedData, REDIS_EXPIRE_TIME);

                log.debug("位置数据已存储到Redis，设备编号: {}, 安全围栏内: {}, 施工围栏内: {}, Key: {}",
                        locationData.getPuname(), isInSafetyFence, isInConstructionFence, redisKey);

                // 如果设备不在任何围栏内，记录警告日志
                if (!isInSafetyFence && !isInConstructionFence) {
                    log.warn("设备 {} 不在任何围栏内: 经度={}, 纬度={}",
                            locationData.getPuname(), locationData.getLongitude(), locationData.getLatitude());
                }

            } catch (Exception e) {
                log.error("处理位置数据失败，设备编号: {}, 错误: {}", locationData.getPuname(), e.getMessage(), e);

                // 即使围栏检测失败，也要存储原始数据
                try {
                    String redisKey = REDIS_KEY_PREFIX + locationData.getPuname();
                    redisUtil.set(redisKey, locationData, REDIS_EXPIRE_TIME);
                    log.debug("原始位置数据已存储到Redis，设备编号: {}", locationData.getPuname());
                } catch (Exception redisException) {
                    log.error("存储原始位置数据到Redis失败，设备编号: {}, 错误: {}",
                            locationData.getPuname(), redisException.getMessage(), redisException);
                }
            }
        }
    }

    @Override
    public List<EnrichedLocationDataDTO> getAllPersonnelLocations() {
        List<EnrichedLocationDataDTO> result = new ArrayList<>();

        try {
            // 查找所有匹配的Redis键
            Set<String> keys = redisTemplate.keys(REDIS_KEY_PREFIX + "*");
            if (!keys.isEmpty()) {
                for (String key : keys) {
                    try {
                        Object value = redisUtil.get(key);
                        if (value instanceof EnrichedLocationDataDTO) {
                            // 如果已经是增强数据对象，直接使用
                            EnrichedLocationDataDTO enrichedData = (EnrichedLocationDataDTO) value;
                            result.add(enrichedData);
                        } else if (value instanceof LocationDataDTO) {
                            // 兼容旧数据格式，转换为增强数据对象
                            LocationDataDTO locationData = (LocationDataDTO) value;
                            EnrichedLocationDataDTO enrichedData = new EnrichedLocationDataDTO();
                            BeanUtils.copyProperties(locationData, enrichedData);
                            // 对于旧数据，需要重新检测围栏
                            boolean isInSafetyFence = checkLocationInSafetyFence(locationData.getLongitude(), locationData.getLatitude());
                            boolean isInConstructionFence = checkLocationInWorkFence(locationData.getLongitude(), locationData.getLatitude());
                            enrichedData.setIsInSafetyFence(isInSafetyFence);
                            enrichedData.setIsInWorkFence(isInConstructionFence);
                            result.add(enrichedData);
                        }
                    } catch (Exception e) {
                        log.error("处理Redis数据失败，Key: {}, 错误: {}", key, e.getMessage(), e);
                    }
                }
            }
            log.info("查询到人员位置数据，数量: {}", result.size());
        } catch (Exception e) {
            log.error("查询人员位置数据失败: {}", e.getMessage(), e);
        }
        enrichLocationDataDTO(result);
        return result;
    }


    /**
     *
     */
    private void enrichLocationDataDTO(List<EnrichedLocationDataDTO> lst) {
        if (lst.isEmpty()) {
            return;
        }
        List<String> deviceCodeList = lst.stream().map(EnrichedLocationDataDTO::getPuname).collect(Collectors.toList());
        if (deviceCodeList.isEmpty()) {
            return;
        }
        //通过设备编号获取所有绑定的人员信息,
        //通过人员信息获得所有最新的违规记录
        //补全lst中每条记录的personId,latestViolation,passCheck 没有的为null
        List<PersonInfo> personInfoList = personInfoService.list(new LambdaQueryWrapper<PersonInfo>()
                .in(PersonInfo::getDeviceCode, deviceCodeList)
                .eq(PersonInfo::getDelFlag, DelFlagEnum.NORMAL.getType()));
        List<String> personIdList = personInfoList.stream().map(PersonInfo::getId).collect(Collectors.toList());
        List<SafeViolationRecords> safeViolationRecordsList = new ArrayList<>();
        if (!personIdList.isEmpty()) {
            safeViolationRecordsList = safeViolationRecordsService.list(new LambdaQueryWrapper<SafeViolationRecords>()
                    .in(SafeViolationRecords::getPerson, personIdList)
                    .eq(SafeViolationRecords::getDelFlag, DelFlagEnum.NORMAL.getType())
                    .orderByDesc(SafeViolationRecords::getViolationTime)
            );
        }

        // 创建设备编号到人员信息的映射
        Map<String, PersonInfo> deviceCodeToPersonMap = personInfoList.stream()
                .collect(Collectors.toMap(PersonInfo::getDeviceCode, person -> person,
                        (existing, replacement) -> existing));
        // 创建人员ID到最新违规记录的映射
        Map<String, SafeViolationRecords> personToLatestViolationMap = new HashMap<>();
        for (SafeViolationRecords violation : safeViolationRecordsList) {
            String personId = violation.getPerson();
            if (!personToLatestViolationMap.containsKey(personId)) {
                personToLatestViolationMap.put(personId, violation);
            }
        }

        // 补全每条记录的信息
        for (EnrichedLocationDataDTO enrichedData : lst) {
            String deviceCode = enrichedData.getPuname();
            PersonInfo personInfo = deviceCodeToPersonMap.get(deviceCode);

            // 如果围栏检测结果为空，进行补充检测（兼容旧数据）
            if (enrichedData.getIsInSafetyFence() == null) {
                boolean isInSafetyFence = checkLocationInSafetyFence(enrichedData.getLongitude(), enrichedData.getLatitude());
                enrichedData.setIsInSafetyFence(isInSafetyFence);
                log.debug("补充安全围栏检测 - 设备 {}: 安全围栏内={}", deviceCode, isInSafetyFence);
            }

            if (enrichedData.getIsInWorkFence() == null) {
                boolean isInConstructionFence = checkLocationInWorkFence(enrichedData.getLongitude(), enrichedData.getLatitude());
                enrichedData.setIsInWorkFence(isInConstructionFence);
                log.debug("补充施工围栏检测 - 设备 {}: 施工围栏内={}", deviceCode, isInConstructionFence);
            }
            if (personInfo != null) {
                // 设置人员信息
                enrichedData.setPersonId(personInfo.getId());
                enrichedData.setPersonName(personInfo.getName());
                // 设置是否登记（有人员信息即为已登记）
                enrichedData.setIsRegistered(true);
                // 设置最新违规记录
                SafeViolationRecords latestViolation = personToLatestViolationMap.get(personInfo.getId());
                enrichedData.setLatestViolation(latestViolation);
                String isAssessed = personInfo.getSafeEducation();
                if (isAssessed == null) {
                    enrichedData.setIsAssessed(null);
                } else {
                    enrichedData.setIsAssessed(SafeEducationStatusEnum.OK.type.equals(isAssessed));
                }
            } else {
                // 没有绑定人员信息
                enrichedData.setPersonId(null);
                enrichedData.setIsRegistered(false);
                enrichedData.setLatestViolation(null);
                enrichedData.setIsAssessed(null);
            }
        }
    }
}
