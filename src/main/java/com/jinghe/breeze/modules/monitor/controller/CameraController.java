package com.jinghe.breeze.modules.monitor.controller;

import com.jinghe.breeze.common.thirdPart.ArtemisUtil;
import com.jinghe.breeze.modules.device.service.IDeviceInfoService;
import com.jinghe.breeze.modules.monitor.entity.CarMonitorRecord;
import com.jinghe.breeze.modules.monitor.service.ICarMonitorRecordService;
import com.jinghe.breeze.modules.monitor.vo.HikParamVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description: 摄像头管理
 * @Author: jeecg-boot
 * @Date: 2024-08-27
 * @Version: V1.0
 */
@Api(tags = "摄像头控制")
@RestController
@RequestMapping("/admin/monitor")
@Slf4j
public class CameraController extends JeecgController<CarMonitorRecord, ICarMonitorRecordService> {
    @Autowired
    private ArtemisUtil artemisUtil;
    @Autowired
    private IDeviceInfoService deviceInfoService;


    /**
     * 代理调用
     *
     * @param param
     * @return
     */
    @AutoLog(value = "代理调用")
    @ApiOperation(value = "代理调用", notes = "摄像头列表查询")
    @PostMapping(value = "/execute")
    public String execute(@RequestBody HikParamVo param) throws Exception {
        return artemisUtil.execute(param.getPath(), param.getParam().toJSONString());
    }


    /**
     * 列表查询
     *
     * @param req
     * @return
     */
    @AutoLog(value = "摄像头列表查询")
    @ApiOperation(value = "摄像头列表查询", notes = "摄像头列表查询")
    @GetMapping(value = "/list")
    public String queryList(HttpServletRequest req) throws Exception {
        return artemisUtil.cameraList();
    }

    /**
     * 预览
     *
     * @param code
     * @return
     */
    @AutoLog(value = "预览")
    @ApiOperation(value = "预览", notes = "预览实时录像")
    @GetMapping(value = "/preView")
    public String preView(@RequestParam(name = "code", required = true) String code) throws Exception {
        String s = artemisUtil.viewCamera(code);
        return s;
    }

    /**
     * 回放
     *
     * @param code
     * @param start
     * @param end
     * @return
     */
    @AutoLog(value = "摄像头回放")
    @ApiOperation(value = "摄像头回放", notes = "查看历史录像")
    @GetMapping(value = "/reView")
    public String reView(@RequestParam(name = "code", required = true) String code,
                         @RequestParam(name = "start", required = false) String start,
                         @RequestParam(name = "end", required = false) String end) throws Exception {
        //"2017-06-15T00:00:00.000+08:00"
        return artemisUtil.reviewCamera(code, start, end);
    }

    /**
     * 控制
     *
     * @param code
     * @param action
     * @param cmd
     * @param speed
     * @return
     */
    @AutoLog(value = "操控摄像头")
    @ApiOperation(value = "操控摄像头", notes = "转向，变焦等操作")
    @GetMapping(value = "/control")
    public String option(@RequestParam(name = "code", required = true) String code,
                         @RequestParam(name = "action", defaultValue = "1") Integer action,
                         @RequestParam(name = "cmd", required = true) String cmd,
                         @RequestParam(name = "speed", defaultValue = "50") Integer speed) throws Exception {
        //"2017-06-15T00:00:00.000+08:00"
        return artemisUtil.controlCamera(code, action, cmd, speed);
    }
}
