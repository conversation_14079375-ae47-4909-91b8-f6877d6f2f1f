package com.jinghe.breeze.modules.sailingcheck.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description: user_sailing_record
 * @Author: jeecg-boot
 * @Date:   2024-06-18
 * @Version: V1.0
 */
@Data
@TableName("user_board_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="user_board_record对象", description="user_board_record")
public class UserBoardRecord implements Serializable {
    private static final long serialVersionUID = 1L;

	/**标识*/
	@TableId(type = IdType.ASSIGN_ID)
    @NotNull(message = "标识不能为空!")

    @ApiModelProperty(value = "标识")
    private String id;
	/**创建人*/

    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**更新人*/

    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
	/**delFlag*/
	@Excel(name = "delFlag", width = 15)
    @NotNull(message = "delFlag不能为空!")

    @ApiModelProperty(value = "delFlag")
    private Integer delFlag;
	/**组织机构编码*/

    @ApiModelProperty(value = "组织机构编码")
    private String sysOrgCode;
	/**项目id*/
	@Excel(name = "项目id", width = 15)

    @ApiModelProperty(value = "项目id")
    private String projectId;
	/**关联人员id*/
	@Excel(name = "关联人员id", width = 15)
    @ApiModelProperty(value = "关联人员id")
    private String userId;

	/**船舶出航记录id*/
	@Excel(name = "船舶出航记录id", width = 15)
    @ApiModelProperty(value = "船舶出航记录id")
    private String baseId;

    /**船舶id*/
    @Excel(name = "船舶id", width = 15)
    @ApiModelProperty(value = "船舶id")
    private String shipId;

	/**类型；1登船，2下船*/
	@Excel(name = "类型；1登船，2下船", width = 15)
    @NotNull(message = "类型；1登船，2下船")
    @ApiModelProperty(value = "类型；1登船，2下船")
    private Integer type;

    @Excel(name = "用户名", width = 15)
    @ApiModelProperty(value = "用户名")
    private String userName;

    @Excel(name = "用户姓名", width = 15)
    @ApiModelProperty(value = "用户姓名")
    private String realName;

    @Excel(name = "单位ID", width = 15)
    @ApiModelProperty(value = "单位ID")
    private String departmentId;

    @Excel(name = "单位名称", width = 15)
    @ApiModelProperty(value = "单位名称")
    private String departmentName;

    @Excel(name = "船舶名称", width = 15)
    @ApiModelProperty(value = "船舶名称")
    private String shipName;

    @Excel(name = "mmsi", width = 15)
    @ApiModelProperty(value = "mmsi")
    private String mmsi;

    @TableField(exist = false)
    private String phone;
}
