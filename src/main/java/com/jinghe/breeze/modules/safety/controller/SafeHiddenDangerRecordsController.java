package com.jinghe.breeze.modules.safety.controller;

import com.jinghe.breeze.common.constants.Common;
import org.apache.shiro.authz.annotation.RequiresPermissions;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.jinghe.breeze.modules.safety.entity.SafeHiddenDangerRecords;
import com.jinghe.breeze.modules.safety.service.ISafeHiddenDangerRecordsService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: 隐患整改
 * @Author: jeecg-boot
 * @Date: 2025-06-24
 * @Version: V1.0
 */
@Api(tags = "隐患整改")
@RestController
@RequestMapping("/safety/safeHiddenDangerRecords")
@Slf4j
public class SafeHiddenDangerRecordsController extends JeecgController<SafeHiddenDangerRecords, ISafeHiddenDangerRecordsService> {
    @Autowired
    private ISafeHiddenDangerRecordsService safeHiddenDangerRecordsService;

    /**
     * 分页列表查询
     *
     * @param safeHiddenDangerRecords
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "隐患整改-分页列表查询")
    @ApiOperation(value = "隐患整改-分页列表查询", notes = "隐患整改-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(SafeHiddenDangerRecords safeHiddenDangerRecords,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<SafeHiddenDangerRecords> queryWrapper = QueryGenerator.initQueryWrapper(safeHiddenDangerRecords, req.getParameterMap());
        if (!ObjectUtils.isEmpty(safeHiddenDangerRecords.getStartDate())){
            queryWrapper.between("report_date",safeHiddenDangerRecords.getStartDate(),safeHiddenDangerRecords.getEndDate());
        }
        Page<SafeHiddenDangerRecords> page = new Page<SafeHiddenDangerRecords>(pageNo, pageSize);
        IPage<SafeHiddenDangerRecords> pageList = safeHiddenDangerRecordsService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param safeHiddenDangerRecords
     * @return
     */
    @AutoLog(value = "隐患整改-添加")
    @ApiOperation(value = "隐患整改-添加", notes = "隐患整改-添加")
    @RequiresPermissions("safeHiddenDangerRecords:add")
    @PostMapping(value = "/add")
    public Result<?> add(@Validated @RequestBody SafeHiddenDangerRecords safeHiddenDangerRecords) {
//        safeHiddenDangerRecords.setBpmStatus("1");
//        if (){
//
//        }
        safeHiddenDangerRecords.setRectificationStatus(Common.RECTIFICATION_STATUS.inProgress);
        if (safeHiddenDangerRecords.getRectificationDeadline()
                .compareTo(safeHiddenDangerRecords.getRectificationCompleteDate()) < 0) {
            safeHiddenDangerRecords.setOverdueStatus(Common.OVERDUE_STATUS.yq);
        } else {
            safeHiddenDangerRecords.setOverdueStatus(Common.OVERDUE_STATUS.wyq);
        }
        safeHiddenDangerRecordsService.save(safeHiddenDangerRecords);
        return Result.OK(safeHiddenDangerRecords);
    }

    /**
     * 编辑
     *
     * @param safeHiddenDangerRecords
     * @return
     */
    @AutoLog(value = "隐患整改-编辑")
    @ApiOperation(value = "隐患整改-编辑", notes = "隐患整改-编辑")
    @RequiresPermissions("safeHiddenDangerRecords:edit")
    @PutMapping(value = "/edit")
    public Result<?> edit(@Validated @RequestBody SafeHiddenDangerRecords safeHiddenDangerRecords) {
        safeHiddenDangerRecordsService.updateById(safeHiddenDangerRecords);
        return Result.OK(safeHiddenDangerRecords);
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "隐患整改-通过id删除")
    @ApiOperation(value = "隐患整改-通过id删除", notes = "隐患整改-通过id删除")
    @RequiresPermissions("safeHiddenDangerRecords:delete")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        safeHiddenDangerRecordsService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "隐患整改-批量删除")
    @ApiOperation(value = "隐患整改-批量删除", notes = "隐患整改-批量删除")
    @RequiresPermissions("safeHiddenDangerRecords:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.safeHiddenDangerRecordsService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "隐患整改-通过id查询")
    @ApiOperation(value = "隐患整改-通过id查询", notes = "隐患整改-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        SafeHiddenDangerRecords safeHiddenDangerRecords = safeHiddenDangerRecordsService.getById(id);
        if (safeHiddenDangerRecords == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(safeHiddenDangerRecords);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param safeHiddenDangerRecords
     */
    @RequiresPermissions("safeHiddenDangerRecords:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SafeHiddenDangerRecords safeHiddenDangerRecords) {
        return super.exportXls(request, safeHiddenDangerRecords, SafeHiddenDangerRecords.class, "隐患整改");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("safeHiddenDangerRecords:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SafeHiddenDangerRecords.class);
    }

}
