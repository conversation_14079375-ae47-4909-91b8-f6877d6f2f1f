package com.jinghe.breeze.modules.settlement.entity;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.*;

/**
 * @Description: settlement
 * @Author: jeecg-boot
 * @Date: 2024-06-03
 * @Version: V1.0
 */
@Data
@TableName("settlement")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "settlement_对象", description = "settlement")
public class Settlement implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 标识
     */
    @TableId(type = IdType.ASSIGN_ID)
    @NotNull(message = "标识不能为空!")

    @ApiModelProperty(value = "标识")
    private String id;
    /**
     * 创建人
     */

    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新人
     */

    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    /**
     * delFlag
     */
    @Excel(name = "delFlag", width = 15)
    @NotNull(message = "delFlag不能为空!")

    @ApiModelProperty(value = "delFlag")
    private Integer delFlag;
    /**
     * 组织机构编码
     */

    @ApiModelProperty(value = "组织机构编码")
    private String sysOrgCode;
    /**
     * 项目id
     */
    @Excel(name = "项目id", width = 15)
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 结算名称
     */
    @Excel(name = "结算名称", width = 15)
    @NotNull(message = "结算名称不能为空!")
    @ApiModelProperty(value = "结算名称")
    private String settleName;

    /**
     * 本次结算金额  允许更新插入null值
     */
    @TableField(value = "current_settlement")
    @Excel(name = "本次结算金额", width = 15)
    @ApiModelProperty(value = "本次结算金额")
    private BigDecimal currentSettlement;

    /**
     * 合同id
     */
    @Excel(name = "合同id", width = 15)
    @ApiModelProperty(value = "合同id")
    private String contractId;

    /**
     * 清单Id
     */
    @Excel(name = "清单Id", width = 15)
    @ApiModelProperty(value = "清单Id")
    private String contractInfoDetailId;

    @Excel(name = "修改批次", width = 15)
    @ApiModelProperty(value = "修改批次")
    private String batch;

    @Excel(name = "附件", width = 20)
    @ApiModelProperty(value = "附件")
    private String file;

    /**
     * 结算日期
     */
    @Excel(name = "结算日期", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结算日期")
    private Date settleDate;

    @TableField(exist = false)
    @ApiModelProperty(value = "合同详细信息")
    private ContractInfoDetail contractInfoDetail;


    @TableField(exist = false)
    @ApiModelProperty(value = "合同信息")
    private ContractInfo contractInfo;

    @TableField(exist = false)
    @ApiModelProperty(value = "批次结算总额")
    private BigDecimal batchTotal;


    @ApiModelProperty(value = "合同子项编码")
    private String code;

    @Excel(name = "备注", width = 20)
    @ApiModelProperty(value = "备注")
    private String remark;

    @Excel(name = "支付金额", width = 20)
    @ApiModelProperty(value = "支付金额")
    private BigDecimal payMoney;

    @Excel(name = "发票金额", width = 20)
    @ApiModelProperty(value = "发票金额")
    private BigDecimal invoiceMoney;
}
