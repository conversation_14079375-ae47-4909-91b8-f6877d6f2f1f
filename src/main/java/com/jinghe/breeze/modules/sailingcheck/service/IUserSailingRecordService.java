package com.jinghe.breeze.modules.sailingcheck.service;

import com.jinghe.breeze.modules.sailingcheck.entity.UserSailingRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;

/**
 * @Description: user_sailing_record
 * @Author: jeecg-boot
 * @Date:   2024-06-18
 * @Version: V1.0
 */
public interface IUserSailingRecordService extends IService<UserSailingRecord> {

    Result<?> unitInfo(String id, int type);
}
