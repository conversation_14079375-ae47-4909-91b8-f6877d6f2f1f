package com.jinghe.breeze.modules.person.dto;

import com.jinghe.breeze.modules.safety.entity.SafeViolationRecords;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jeecg.common.aspect.annotation.Dict;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description: 增强的位置数据传输对象
 * @Author: system
 * @Date: 2025-01-02
 * @Version: V1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EnrichedLocationDataDTO", description = "增强的位置数据传输对象")
public class EnrichedLocationDataDTO extends LocationDataDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "是否通过考核")
    private Boolean isAssessed;

    @ApiModelProperty(value = "是否登记")
    private Boolean isRegistered;

    @ApiModelProperty(value = "人员id")
    @Dict(dictTable = "person_info", dicText = "name", dicCode = "id")
    private String personId;

    @ApiModelProperty(value = "人员名称")
    private String personName;


    @ApiModelProperty(value = "是否在安全防护围栏内")
    private Boolean isInSafetyFence;

    @ApiModelProperty(value = "是否在施工围栏内")
    private Boolean isInWorkFence;

    @ApiModelProperty(value = "工作区域")
    private String position;


}
