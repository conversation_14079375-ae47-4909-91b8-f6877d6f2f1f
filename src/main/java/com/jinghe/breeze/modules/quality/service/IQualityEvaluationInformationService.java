package com.jinghe.breeze.modules.quality.service;

import com.jinghe.breeze.modules.quality.entity.QualityEvaluationInformationDetail;
import com.jinghe.breeze.modules.quality.entity.QualityEvaluationInformation;
import com.baomidou.mybatisplus.extension.service.IService;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 验评资料
 * @Author: jeecg-boot
 * @Date:   2025-06-27
 * @Version: V1.0
 */
public interface IQualityEvaluationInformationService extends IService<QualityEvaluationInformation> {

	/**
	 * 添加一对多
	 * 
	 */
	public void saveMain(QualityEvaluationInformation qualityEvaluationInformation,List<QualityEvaluationInformationDetail> qualityEvaluationInformationDetailList) ;
	
	/**
	 * 修改一对多
	 * 
	 */
	public void updateMain(QualityEvaluationInformation qualityEvaluationInformation,List<QualityEvaluationInformationDetail> qualityEvaluationInformationDetailList);
	
	/**
	 * 删除一对多
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);
	
}
