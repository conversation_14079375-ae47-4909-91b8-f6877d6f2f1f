package com.jinghe.breeze.modules.person.mapper;

import java.util.List;
import com.jinghe.breeze.modules.person.entity.PersonPosFenceDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 人员定位电子围栏经纬度子表
 * @Author:
 * @Date:   2022-08-09
 * @Version: V1.0
 */
public interface PersonPosFenceDetailMapper extends BaseMapper<PersonPosFenceDetail> {

	public boolean deleteByMainId(@Param("mainId") String mainId);
    
	public List<PersonPosFenceDetail> selectByMainId(@Param("mainId") String mainId);
}
