package com.jinghe.breeze.modules.monitor.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghe.breeze.modules.monitor.entity.DeviceOnlineCountRecord;
import com.jinghe.breeze.modules.monitor.mapper.DeviceOnlineCountRecordMapper;
import com.jinghe.breeze.modules.monitor.service.IDeviceOnlineCountRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * @Description: DeviceOnlineCountRecord
 * @Author: jeecg-boot
 * @Date: 2024-09-05
 * @Version: V1.0
 */
@Service
public class DeviceOnlineCountRecordServiceImpl extends ServiceImpl<DeviceOnlineCountRecordMapper, DeviceOnlineCountRecord> implements IDeviceOnlineCountRecordService {



    @Override
    public List<DeviceOnlineCountRecord> viewHis() {
        LambdaQueryWrapper<DeviceOnlineCountRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(DeviceOnlineCountRecord::getRecordTime);
        queryWrapper.last("limit 12");
        return baseMapper.selectList(queryWrapper);
    }
}
