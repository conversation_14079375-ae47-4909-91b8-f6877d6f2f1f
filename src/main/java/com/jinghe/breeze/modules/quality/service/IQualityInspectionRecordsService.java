package com.jinghe.breeze.modules.quality.service;

import com.jinghe.breeze.modules.quality.entity.QualityInspectionRecords;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: quality_inspection_records
 * @Author: jeecg-boot
 * @Date:   2025-06-20
 * @Version: V1.0
 */
public interface IQualityInspectionRecordsService extends IService<QualityInspectionRecords> {

    void createOrUpdate(QualityInspectionRecords qualityInspectionRecords);

    void fullDetails(List<QualityInspectionRecords> list);
}
