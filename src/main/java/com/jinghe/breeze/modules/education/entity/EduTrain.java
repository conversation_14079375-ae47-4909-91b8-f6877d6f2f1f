package com.jinghe.breeze.modules.education.entity;

import java.io.Serializable;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.*;
/**
 * @Description: 培训管理
 * @Author: jeecg-boot
 * @Date:   2025-06-25
 * @Version: V1.0
 */
@Data
@TableName("edu_train")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="edu_train对象", description="培训管理")
public class EduTrain implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)

    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/

    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/

    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/

    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**删除标识*/
	@Excel(name = "删除标识", width = 15)

    @ApiModelProperty(value = "删除标识")
    private java.lang.String delFlag;
	/**培训主题*/
	@Excel(name = "培训主题", width = 15)
    @NotNull(message = "培训主题不能为空!")

    @ApiModelProperty(value = "培训主题")
    private java.lang.String subject;


	/**接受培训单位*/
	@Excel(name = "接受培训单位", width = 15)
    @NotNull(message = "接受培训单位不能为空!")
    @Dict(dictTable = "project_unit", dicText = "name", dicCode = "id")
    @ApiModelProperty(value = "接受培训单位多个")
    private java.lang.String unit;
	/**培训方式*/
	@Excel(name = "培训方式", width = 15, dicCode = "train_method")
	@Dict(dicCode = "train_method")
    @NotNull(message = "培训方式不能为空!")

    @ApiModelProperty(value = "培训方式")
    private java.lang.String trainMethod;
	/**培训地点*/
	@Excel(name = "培训地点", width = 15)
    @NotNull(message = "培训地点不能为空!")

    @ApiModelProperty(value = "培训地点")
    private java.lang.String location;
	/**开始时间*/
	@Excel(name = "开始时间", width = 15)
    @NotNull(message = "开始时间不能为空!")

    @ApiModelProperty(value = "开始时间")
    private java.lang.String startTime;
	/**结束时间*/
	@Excel(name = "结束时间", width = 15)
    @NotNull(message = "结束时间不能为空!")

    @ApiModelProperty(value = "结束时间")
    private java.lang.String endTime;

	/**考试方式*/
	@Excel(name = "考试方式", width = 15)
    @Dict(dicCode = "exam_method")
    @NotNull(message = "考试方式不能为空!")
    @ApiModelProperty(value = "考试方式")
    private java.lang.String examMethod;

	/**培训类型*/
	@Excel(name = "培训类型", width = 15, dicCode = "train_type")
	@Dict(dicCode = "train_type")
    @NotNull(message = "培训类型不能为空!")

    @ApiModelProperty(value = "培训类型")
    private java.lang.String trainType;
	/**选择试卷*/
	@Excel(name = "选择试卷", width = 15)
    @ApiModelProperty(value = "选择试卷")
    private java.lang.String paperId;

	/**关联资料*/
	@Excel(name = "关联资料", width = 15, dictTable = "edu_training_material", dicText = "name", dicCode = "id")
	@Dict(dictTable = "edu_training_material", dicText = "name", dicCode = "id")

    @ApiModelProperty(value = "关联资料")
    private java.lang.String trainingMaterial;
	/**发送考试人员*/
	@Excel(name = "发送考试人员", width = 15, dictTable = "person_info", dicText = "name", dicCode = "id")
	@Dict(dictTable = "person_info", dicText = "name", dicCode = "id")

    @ApiModelProperty(value = "发送考试人员")
    private java.lang.String personId;
	/**培训附件*/
	@Excel(name = "培训附件", width = 15)

    @ApiModelProperty(value = "培训附件")
    private java.lang.String file;
	/**培训简介*/
	@Excel(name = "培训简介", width = 15)

    @ApiModelProperty(value = "培训简介")
    private java.lang.String overview;
	/**培训记录*/
	@Excel(name = "培训记录", width = 15)

    @TableField(exist = false)
    @ApiModelProperty(value = "培训记录")
    private List<EduTrainDetail> details;
}
