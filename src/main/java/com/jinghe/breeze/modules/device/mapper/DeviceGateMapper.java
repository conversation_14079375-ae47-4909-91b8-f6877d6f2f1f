package com.jinghe.breeze.modules.device.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jinghe.breeze.modules.device.entity.DeviceGate;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 闸机设备信息
 * @Author: jeecg-boot
 * @Date: 2024-08-13
 * @Version: V1.0
 */
public interface DeviceGateMapper extends BaseMapper<DeviceGate> {

    public boolean deleteByMainId(@Param("mainId") String mainId);

    public List<DeviceGate> selectByMainId(@Param("mainId") String mainId);
}
