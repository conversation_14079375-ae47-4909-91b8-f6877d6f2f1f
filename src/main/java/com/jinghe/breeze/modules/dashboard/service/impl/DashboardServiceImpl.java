package com.jinghe.breeze.modules.dashboard.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.common.constants.enums.ProjectPbsEnum;
import com.jinghe.breeze.modules.construction.entity.ConstructionLog;
import com.jinghe.breeze.modules.construction.entity.MonitorDolphinLogDetail;
import com.jinghe.breeze.modules.construction.service.IConstructionLogService;
import com.jinghe.breeze.modules.construction.service.IMonitorDolphinLogDetailService;
import com.jinghe.breeze.modules.dashboard.service.IDashboardService;
import com.jinghe.breeze.modules.person.entity.PersonInfo;
import com.jinghe.breeze.modules.person.service.IPersonInfoService;
import com.jinghe.breeze.modules.project.entity.ProjectCamera;
import com.jinghe.breeze.modules.project.entity.ProjectPbs;
import com.jinghe.breeze.modules.project.entity.ProjectProcess;
import com.jinghe.breeze.modules.project.entity.vo.PbsVo;
import com.jinghe.breeze.modules.project.service.IProjectCameraService;
import com.jinghe.breeze.modules.project.service.IProjectInfoService;
import com.jinghe.breeze.modules.project.service.IProjectPbsService;
import com.jinghe.breeze.modules.project.service.IProjectProcessService;
import com.jinghe.breeze.modules.sailingcheck.entity.ShipSailingRecord;
import com.jinghe.breeze.modules.sailingcheck.entity.UserSailingRecord;
import com.jinghe.breeze.modules.sailingcheck.service.IShipSailingRecordService;
import com.jinghe.breeze.modules.sailingcheck.service.IUserSailingRecordService;
import com.jinghe.breeze.modules.sea.entity.ExtremeWeather;
import com.jinghe.breeze.modules.sea.entity.SeaWeatherLive;
import com.jinghe.breeze.modules.sea.service.IExtremeWeatherService;
import com.jinghe.breeze.modules.sea.service.ISeaEnvironmentHourService;
import com.jinghe.breeze.modules.sea.service.ISeaWeatherLiveService;
import com.jinghe.breeze.modules.ship.entity.ShipInfo;
import com.jinghe.breeze.modules.ship.entity.ShipOutgoing;
import com.jinghe.breeze.modules.ship.service.IShipAlarmService;
import com.jinghe.breeze.modules.ship.service.IShipInfoService;
import com.jinghe.breeze.modules.ship.service.IShipOutgoingService;
import com.jinghe.breeze.modules.wind.entity.WindNotification;
import com.jinghe.breeze.modules.wind.service.IWindNotificationService;
import jodd.util.StringUtil;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.DateUtils;
import org.jeecg.modules.system.entity.SysDict;
import org.jeecg.modules.system.entity.SysDictItem;
import org.jeecg.modules.system.service.ISysDictItemService;
import org.jeecg.modules.system.service.ISysDictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import org.jeecg.common.api.vo.Result;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class DashboardServiceImpl implements IDashboardService {
    @Autowired
    private IWindNotificationService windNotificationService;
    @Autowired
    private IShipAlarmService shipAlarmService;
    @Autowired
    private IExtremeWeatherService extremeWeatherService;
    @Autowired
    private ISeaWeatherLiveService seaWeatherLiveService;

    @Autowired
    private IProjectInfoService projectInfoService;

    @Autowired
    private IUserSailingRecordService userSailingRecordService;

    @Autowired
    private ISysDictService sysDictService;

    @Autowired
    private ISysDictItemService sysDictItemService;
    @Autowired
    private IPersonInfoService personInfoService;

    @Autowired
    private IShipInfoService shipInfoService;
    @Autowired
    private IShipOutgoingService shipOutgoingService;
    @Autowired
    private IProjectCameraService ********************;
    @Autowired
    private ISeaEnvironmentHourService seaEnvironmentHourService;

    @Autowired
    private IProjectPbsService projectPbsService;

    @Autowired
    private IShipSailingRecordService shipSailingRecordService;

    @Autowired
    private IConstructionLogService constructionLogService;

    @Autowired
    private IProjectProcessService projectProcessService;

    @Autowired
    private IMonitorDolphinLogDetailService watchDolphinLogDetailService;


    @Override
    public WindNotification getWindNotification() {
        WindNotification notify = windNotificationService.getOne(new LambdaQueryWrapper<WindNotification>()
                //避风通知消息，发布中的显示，避风结束后就不显示
                .eq(WindNotification::getState, 1)
                .gt(WindNotification::getPlanEndDate, new Date()) //取未截止的 记录
                .orderByDesc(WindNotification::getUpdateTime)
                .last("limit 1"), false);
        return notify;
    }

    @Override
    public Result<?> getShipAlarm() {
        return shipAlarmService.queryAlarm();
    }

    @Override
    public Result<?> getExtremeWeather() {
        // 获取24小时之前的时间
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime oneDayAgo = now.minus(1, ChronoUnit.DAYS);
        List<ExtremeWeather> list = extremeWeatherService.list(new LambdaQueryWrapper<ExtremeWeather>()
                .ge(ExtremeWeather::getReleaseDate, oneDayAgo)
                .orderByDesc(ExtremeWeather::getReleaseDate)
                .orderByDesc(ExtremeWeather::getAlertStatus));
        return Result.OK(list);
    }

    @Override
    public Result<?> getRealTimeWeather() {
        SeaWeatherLive seaWeatherLive = seaWeatherLiveService.getOne(new LambdaQueryWrapper<SeaWeatherLive>()
                .orderByDesc(SeaWeatherLive::getCreateTime)
                .last("limit 1"), false);
        return Result.OK(seaWeatherLive);
    }

    @Override
    public Map<String, String> getProjectInfo() {
        return projectInfoService.getProjectInfo();
    }

    @Override
    public Map<String, String> getDictMap(String dictCode, String dicriptName) {
        if (StringUtil.isEmpty(dictCode)) {
            throw new JeecgBootException("字典编码不能为空");
        }
        SysDict sysDict = sysDictService.getOne(new LambdaQueryWrapper<SysDict>().eq(SysDict::getDictCode, dictCode));
        if (sysDict == null) {
            throw new JeecgBootException(String.format("请先配置%s数据字典", dicriptName));
        }
        List<SysDictItem> sysDictItems = sysDictItemService.selectItemsByMainId(sysDict.getId());
        Map<String, String> retMap = new HashMap<>();
        sysDictItems.forEach(c -> retMap.put(c.getItemValue(), c.getItemText())); // key 是 B12A02 value 是9#路由
        return retMap;
    }

    @Override
    public Map<String, Object> getConstructInfo() {
        // return projectPbsService.getBaseMapData();
        Map<String, Object> map = new HashMap<>();
        LambdaQueryWrapper<ProjectPbs> pbsQuery = new LambdaQueryWrapper<>();
        pbsQuery.eq(ProjectPbs::getDelFlag, Common.delete_flag.OK);
        pbsQuery.eq(ProjectPbs::getLayer, Common.commonality.ZERO);
        pbsQuery.in(ProjectPbs::getType, new String[]{ProjectPbsEnum.FJ.getCode(), ProjectPbsEnum.HL.getCode(),
                ProjectPbsEnum.SYZ.getCode()});
        List<ProjectPbs> projectPbsList = projectPbsService.list(pbsQuery);

//        List<ConstructionLog> logList = constructionLogService.list(new LambdaQueryWrapper<ConstructionLog>()
//                .eq(ConstructionLog::getDelFlag, Common.delete_flag.OK)
//                .isNotNull(ConstructionLog::getEndTime));
        // 获取所有海缆
        ProjectPbs syzPbs = projectPbsList.stream().filter(c -> c.getType().equals(ProjectPbsEnum.SYZ.getCode()))
                .findFirst().orElse(null);
        if (syzPbs == null) {
            throw new JeecgBootException("未找到升压站");
        }
        List<ProjectPbs> hlList = projectPbsList.stream().filter(c -> c.getType().equals(ProjectPbsEnum.HL.getCode()))
                .collect(Collectors.toList());
        List<ProjectPbs> fjList = projectPbsList.stream().filter(c -> c.getType().equals(ProjectPbsEnum.FJ.getCode()))
                .collect(Collectors.toList());
        List<String> routeList = projectPbsList.stream().map(ProjectPbs::getRoute).distinct()
                .collect(Collectors.toList()); // 路由
        Map<String, ProjectPbs> allMap = new HashMap<>();
        Map<String, ProjectPbs> hlMap = new HashMap<>();
        Map<String, ProjectPbs> fjMap = new HashMap<>();
        hlList.forEach(c -> hlMap.put(c.getId(), c));
        fjList.forEach(c -> fjMap.put(c.getId(), c));
        projectPbsList.forEach(c -> allMap.put(c.getId(), c));

        Map<String, String> routeMap = getDictMap("B12", "海缆路由");
        for (String route : routeList) {
            List<Object> fanNameList = getRouteList(syzPbs, route, hlList, fjMap, allMap);
            String text = routeMap.getOrDefault(route, "");
            map.put(text, fanNameList);
        }
        Map<String, Object> retMap = new HashMap<>();
        retMap.put("route", map);
        retMap.put("pbsList", projectPbsList);
        // 下面开始获取每个pbs的完成信息
        Map<String, Object> dataPanelMap = condition(null);
        retMap.put("dataPanelMap", dataPanelMap);
        return retMap;
    }

    @Override
    public Map<String, Object> condition(String projectId) {
        // 整体思路 1查询出所有pbs 风机海缆 通过类型区分分组 2 查询出所有类型的工序 工序按照类型在分组 查询出所有日志
        // 根据pbsid分组然后判断pbs完成的工序长度与实际工序长度是否一致
        LambdaQueryWrapper<ProjectPbs> pbsWrapper = new LambdaQueryWrapper<>();
        pbsWrapper.eq(ProjectPbs::getDelFlag, Common.delete_flag.OK).eq(ProjectPbs::getFanSection,
                Common.commonality.ZERO);
        if (!StringUtils.isEmpty(projectId)) {
            pbsWrapper.eq(ProjectPbs::getProjectId, projectId);
        }
        List<ProjectPbs> projectPbs = projectPbsService.list(pbsWrapper);
        Set<String> processTypes = projectPbs.stream().map(x -> x.getApplicableProcess()).collect(Collectors.toSet());
        List<String> pbsIds = projectPbs.stream().map(x -> x.getId()).collect(Collectors.toList());
        LambdaQueryWrapper<ConstructionLog> logWrapper = new LambdaQueryWrapper<>();
        logWrapper.in(ConstructionLog::getPbsId, pbsIds).isNotNull(ConstructionLog::getEndTime);
        List<ConstructionLog> logList = constructionLogService.list(logWrapper);
        LambdaQueryWrapper<ProjectProcess> processWrapper = new LambdaQueryWrapper<>();
        processWrapper.eq(ProjectProcess::getDelFlag, Common.delete_flag.OK)
                .in(ProjectProcess::getType, processTypes);
        List<ProjectProcess> projectProcesses = projectProcessService.list(processWrapper);
        Map<String, Object> pbsMap = new HashMap<>();
        Map<String, List<ProjectPbs>> collect = projectPbs.stream().collect(Collectors.groupingBy(ProjectPbs::getType));
        Map<String, List<ProjectProcess>> processtype = projectProcesses.stream()
                .collect(Collectors.groupingBy(ProjectProcess::getType));
        Map<String, List<ConstructionLog>> logs = logList.stream()
                .collect(Collectors.groupingBy(ConstructionLog::getPbsId));
        List<String> fjnotStartedIds = new ArrayList<>();
        List<Map<String, Object>> fjunderwayIds = new ArrayList<>();
        List<String> fjcompleteIds = new ArrayList<>();
        List<String> hlnotStartedIds = new ArrayList<>();
        List<Map<String, Object>> hlunderwayIds = new ArrayList<>();
        List<String> hlcompleteIds = new ArrayList<>();

        for (String key : collect.keySet()) {
            if (key.equals(ProjectPbsEnum.SYZ.getCode())) {
                continue;
            }
            List<ProjectPbs> projectPbs1 = collect.get(key);
            if (key.equals(ProjectPbsEnum.FJ.getCode())) {
                pbsMap.put("fjSum", projectPbs1.size());
                for (ProjectPbs pbs : projectPbs1) {
                    List<ConstructionLog> list = logs.get(pbs.getId());
                    List<ProjectProcess> projectProcesses1 = processtype.get(pbs.getApplicableProcess());
                    if (StringUtils.isEmpty(list)) { // 未开始
                        fjnotStartedIds.add(pbs.getId());
                    } else if (list.size() < projectProcesses1.size()) {
                        Map<String, Object> underwayMap = new HashMap<>();
                        double totalWeights = getTotalWeights(list, projectProcesses1);
                        underwayMap.put("id", pbs.getId());
                        underwayMap.put("weights", totalWeights);
                        fjunderwayIds.add(underwayMap);
                    } else {
                        fjcompleteIds.add(pbs.getId());
                    }
                }
            } else if (key.equals(ProjectPbsEnum.HL.getCode())) {
                pbsMap.put("hlSum", projectPbs1.size());
                for (ProjectPbs pbs : projectPbs1) {
                    List<ConstructionLog> list = logs.get(pbs.getId());
                    List<ProjectProcess> projectProcesses1 = processtype.get(pbs.getApplicableProcess());
                    if (StringUtils.isEmpty(list)) { // 未开始
                        hlnotStartedIds.add(pbs.getId());
                    } else if (list.size() < projectProcesses1.size()) {
                        Map<String, Object> underwayMap = new HashMap<>();
                        double totalWeights = getTotalWeights(list, projectProcesses1);
                        underwayMap.put("id", pbs.getId());
                        underwayMap.put("weights", totalWeights);
                        hlunderwayIds.add(underwayMap);
                    } else {
                        hlcompleteIds.add(pbs.getId());
                    }
                }
            }
        }
        pbsMap.put("fjnotStartedIds", fjnotStartedIds);
        pbsMap.put("fjunderwayIds", fjunderwayIds);
        pbsMap.put("fjcompleteIds", fjcompleteIds);
        pbsMap.put("hlnotStartedIds", hlnotStartedIds);
        pbsMap.put("hlunderwayIds", hlunderwayIds);
        pbsMap.put("hlcompleteIds", hlcompleteIds);
        return pbsMap;
    }

    private double getTotalWeights(List<ConstructionLog> logs, List<ProjectProcess> processes) {
        double totalWeights = 0;
        for (ConstructionLog log : logs) {
            ProjectProcess process = processes.stream().filter(x -> x.getId().equals(log.getProcessId())).findFirst()
                    .orElse(null);
            if (process == null) {
                continue;
            }
            totalWeights += process.getWeight();
        }
        return totalWeights;

    }

    private List<Object> getRouteList(ProjectPbs syzPbs,
                                      String route,
                                      List<ProjectPbs> hlList,
                                      Map<String, ProjectPbs> fjMap,
                                      Map<String, ProjectPbs> map) {
        // Map<String, ProjectPbs> map = new HashMap<>();
        // Map<String, ProjectPbs> hlMap = new HashMap<>();
        // Map<String, ProjectPbs> fjMap = new HashMap<>();
        // hlList.forEach(c -> hlMap.put(c.getId(), c));
        // fjList.forEach(c -> fjMap.put(c.getId(), c));
        // pbsList.forEach(c -> map.put(c.getId(), c));
        List<Object> retList = new ArrayList<>();
        Map<String, Integer> disdinct = new HashMap<>();
        ProjectPbs next = syzPbs;
        while (next != null) {
            String id = next.getId();// 当前结束段的id
            ProjectPbs cable = hlList.stream().filter(c -> { // 寻找结束段为目标id的海缆
                return c.getEndSection().equals(id) && c.getRoute().equals(route); // 取出
            }).findFirst().orElse(null);
            if (cable != null) {// next此时为找到的海缆
                ProjectPbs endPbs = map.get(cable.getEndSection());
                ProjectPbs startPbs = map.get(cable.getStartSection());
                if (fjMap.containsKey(endPbs.getId()) && !disdinct.containsKey(endPbs.getId())) { // 如果是风机则加入
                    retList.add(endPbs.getId());// 在末尾插入
                    disdinct.put(endPbs.getId(), 1);
                }
                retList.add(cable.getId()); // 插入海缆信息
                if (!disdinct.containsKey(startPbs.getId())) {
                    retList.add(startPbs.getId());
                    disdinct.put(startPbs.getId(), 1);
                }
                next = startPbs;
            } else {
                break;
            }
        }
        return retList;
    }

    @Override
    public List<ProjectCamera> getCameraInfo() {
        return ********************.list(new LambdaQueryWrapper<ProjectCamera>()
                .eq(ProjectCamera::getDelFlag, 0));
    }

    @Override
    public Map getWeather7days() {
        Map map = seaEnvironmentHourService.dailyWeather7day(0);
        map.remove("hours");
        return map;
    }

    private int getRemainPersonCount(List<PersonInfo> list, Map<String, UserSailingRecord> latestRecordMap) {
        int total = 0;
        List<String> ids = list.stream().map(PersonInfo::getUserId).collect(Collectors.toList());
        for (String userId : ids) {
            UserSailingRecord latestRecord = latestRecordMap.get(userId);
            if (latestRecord == null) {
                total = total + 1;// 没有出行记录算作在场
                continue;
            }
            if (Objects.equals(latestRecord.getStatus(), 2)) { // 表示返航人员
                total = total + 1;
            }
        }
        return total;
    }

    private Map<String, Integer> classifyByAgeRange(List<PersonInfo> list) {
        Map<String, Integer> map = new HashMap<>();
        map.put("20-29", 0);
        map.put("30-39", 0);
        map.put("40-49", 0);
        map.put("50-59", 0);
        map.put("60+", 0);
        for (PersonInfo personInfo : list) {
            Integer age = personInfo.getAge();
            String key = "";
            if (age >= 20 && age <= 29) {
                key = "20-29";
            } else if (age >= 30 && age <= 39) {
                key = "30-39";
            } else if (age >= 40 && age <= 49) {
                key = "40-49";
            } else if (age >= 50 && age <= 59) {
                key = "50-59";
            } else if (age >= 60) {
                key = "60+";
            } else {
                continue;
            }
            map.put(key, map.get(key) + 1);
        }
        return map;
    }

    @Override
    public Map<String, Object> getPersonInfo() {
        // person_type 字典字段
        SysDict sysDict = sysDictService
                .getOne(new LambdaQueryWrapper<SysDict>().eq(SysDict::getDictCode, "person_type"));
        if (sysDict == null) {
            throw new JeecgBootException("请先配置人员数据字典");
        }
        List<SysDictItem> sysDictItems = sysDictItemService.selectItemsByMainId(sysDict.getId());
        LambdaQueryWrapper<PersonInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNotNull(PersonInfo::getEnterDate).isNull(PersonInfo::getLeaveDate);
        List<PersonInfo> personInfoList = personInfoService.list(queryWrapper);
//        List<String> userIds = personInfoList.stream().map(PersonInfo::getUserId).collect(Collectors.toList());
//        List<UserSailingRecord> recordList = userSailingRecordService.list(new LambdaQueryWrapper<UserSailingRecord>() // 一次读取完成,避免多次访问数据库
//                .in(UserSailingRecord::getUserId, userIds)
//                .orderByDesc(UserSailingRecord::getCreateTime));
//        Map<String, UserSailingRecord> latestRecordMap = recordList.stream()
//                .collect(Collectors.toMap(UserSailingRecord::getUserId, record -> record,
//                        (existing, replacement) -> existing));

        Map<String, List<PersonInfo>> personInfoDict = new HashMap<>(); // 先将人员按类型名分类
        Map<String, String> personTypeMap = new HashMap<>();
        for (SysDictItem item : sysDictItems) {
            String personType = item.getItemValue();
            personTypeMap.put(personType, item.getItemText());
            List<PersonInfo> tmpPersonInfoList = personInfoList.stream().filter(personInfo -> personInfo.getPersonType()
                    .equals(personType)).collect(Collectors.toList());
            personInfoDict.put(personType, tmpPersonInfoList);
        }
        Map<String, Object> resMap = new HashMap<>();
        resMap.put("total", personInfoList.size());
        for (String key : personInfoDict.keySet()) {
            // 在场人数
            List<PersonInfo> tmpList = personInfoDict.get(key); // 每个类型的人员列表;
            int remainPersonCount = tmpList.size();//   getRemainPersonCount(tmpList, latestRecordMap); // 这个类型在场的人数
            Map<String, Integer> ageInfo = classifyByAgeRange(personInfoDict.get(key));
            Map<String, Object> category = new HashMap<>();
            category.put("remainPersonCount", remainPersonCount);
            category.put("ageInfo", ageInfo);
            resMap.put(personTypeMap.get(key), category);
        }
        return resMap;
    }

    private int getRemainShipCount(List<ShipInfo> list, Map<String, ShipOutgoing> latestRecordMap) {
        int total = 0;
        List<String> ids = list.stream().map(ShipInfo::getId).collect(Collectors.toList());
        for (String shipDataId : ids) {
            ShipOutgoing latestRecord = latestRecordMap.get(shipDataId);
            if (latestRecord != null && Objects.equals(latestRecord.getIsOut(), "0")) { // 表示在场 1表示离场
                total = total + 1;
            }
        }
        return total;
    }

    @Override
    public Map<String, Object> getShipInfo() {
        // ship_typ_code
        SysDict sysDict = sysDictService
                .getOne(new LambdaQueryWrapper<SysDict>().eq(SysDict::getDictCode, "ship_typ_code"));
        if (sysDict == null) {
            throw new JeecgBootException("请先配置船舶类型数据字典");
        }
        List<SysDictItem> sysDictItems = sysDictItemService.selectItemsByMainId(sysDict.getId());
        List<ShipInfo> shipInfoList = shipInfoService.list();
        List<String> shipIds = shipInfoList.stream().map(ShipInfo::getId).collect(Collectors.toList());
        List<ShipOutgoing> shipOutgoingList = shipOutgoingService.list(new LambdaQueryWrapper<ShipOutgoing>()
                .in(ShipOutgoing::getShipDataId, shipIds)
                .orderByDesc(ShipOutgoing::getCreateTime));
        Map<String, ShipOutgoing> latestRecordMap = shipOutgoingList.stream()
                .collect(Collectors.toMap(ShipOutgoing::getShipDataId, record -> record,
                        (existing, replacement) -> existing));
        Map<String, List<ShipInfo>> shipInfoDict = new HashMap<>(); // 先将船舶按类型分类
        Map<String, String> shipTypeMap = new HashMap<>();
        for (SysDictItem item : sysDictItems) {
            String shipType = item.getItemValue();
            shipTypeMap.put(shipType, item.getItemText());
            List<ShipInfo> tmpShipInfoList = shipInfoList.stream().filter(shipInfo -> shipInfo.getTypeValue()
                    .equals(shipType)).collect(Collectors.toList());
            shipInfoDict.put(shipType, tmpShipInfoList); // 存放每个类型的船舶
        }
        Map<String, Object> resMap = new HashMap<>(); // 存放返回数据
        for (String key : shipInfoDict.keySet()) {
            // 在场船舶
            List<ShipInfo> tmpList = shipInfoDict.get(key); // 每个类型的船舶列表
            int remainShipCount = getRemainShipCount(tmpList, latestRecordMap); // 这个类型在场的船舶
            resMap.put(shipTypeMap.get(key), remainShipCount);
        }
        return resMap;
    }

    @Override
    public List<Map<String, Object>> getCurrentShipSailingInfo() {
        // 带上时间 出发多久了
        List<ShipSailingRecord> shipList = shipSailingRecordService.list(new LambdaQueryWrapper<ShipSailingRecord>()
                .eq(ShipSailingRecord::getStatus, Common.commonality.ONE));
        List<String> shipIds = shipList.stream().map(ShipSailingRecord::getId).collect(Collectors.toList());
        List<UserSailingRecord> userList;
        if (shipIds.isEmpty()) {
            userList = new ArrayList<>();
        } else {
            userList = userSailingRecordService.list(new LambdaQueryWrapper<UserSailingRecord>()
                    .in(UserSailingRecord::getBaseId, shipIds));
        }
        List<Map<String, Object>> retList = new ArrayList<>();
        shipList.forEach(p -> {
            long userCount = userList.stream().filter(u -> u.getBaseId().equals(p.getId())).count();
            Map<String, Object> map = new HashMap<>();
            map.put("name", p.getShipName());
            map.put("count", userCount);
            map.put("createTime", p.getCreateTime());
            retList.add(map);
        });
        return retList;
    }


    @Override
    public List<String> bigScreenPictures() {
        //项目实况，取最新的5张施工日志图片
        LambdaQueryWrapper<ConstructionLog> logWrapper = new LambdaQueryWrapper<>();
        logWrapper.eq(ConstructionLog::getConstructionState, Common.commonality.TWO);
        logWrapper.isNotNull(ConstructionLog::getFile);
        logWrapper.orderByDesc(ConstructionLog::getCreateTime);
        logWrapper.last(" limit 5");
        List<ConstructionLog> logList = constructionLogService.list(logWrapper);
        List<String> images = new ArrayList<>();
        if (logList.isEmpty()) {
            //TODO 没有施工日志，取项目信息图片
        } else {
            for (ConstructionLog log : logList) {
                images.add(log.getFile());
            }
        }
        return images;
    }

    @Override
    public Map<String, List<Integer>> bigScreenWeekWork() {
        List<Integer> workerVolume = new ArrayList<>();
        List<Integer> shipVolume = new ArrayList<>();
        Long weekago = System.currentTimeMillis() - 86400000 * 7;
        LambdaQueryWrapper<UserSailingRecord> userSailingWrapper = new LambdaQueryWrapper<>();
        userSailingWrapper.eq(UserSailingRecord::getDelFlag, Common.delete_flag.OK);
        userSailingWrapper.orderByAsc(UserSailingRecord::getCreateTime);
        userSailingWrapper.gt(UserSailingRecord::getCreateTime, new Date(weekago));
        List<UserSailingRecord> userSailings = userSailingRecordService.list(userSailingWrapper);
        if (userSailings.isEmpty()) {
            for (int i = 0; i < 7; i++) {
                workerVolume.add(0);
            }
        } else {
            Integer dayCount = 0;
            Long daypoint = weekago + 86400000;
            int i = 0;
            int j = 0;
            while (i < userSailings.size() && j < 7) {
                Long lastRecordPoint = userSailings.get(i).getCreateTime().getTime();
                if (lastRecordPoint < daypoint) {
                    dayCount++;
                    i++;
                } else {
                    workerVolume.add(dayCount);
                    dayCount = 0;
                    daypoint = daypoint + 86400000;
                    j++;
                }
            }
            while (workerVolume.size() < 7) {
                workerVolume.add(dayCount);
                dayCount = 0;
            }
        }
        LambdaQueryWrapper<ShipSailingRecord> shipSailingWrapper = new LambdaQueryWrapper<>();
        shipSailingWrapper.eq(ShipSailingRecord::getDelFlag, Common.delete_flag.OK);
        shipSailingWrapper.orderByAsc(ShipSailingRecord::getCreateTime);
        shipSailingWrapper.gt(ShipSailingRecord::getCreateTime, new Date(weekago));
        List<ShipSailingRecord> shipSailings = shipSailingRecordService.list(shipSailingWrapper);
        if (shipSailings.isEmpty()) {
            for (int i = 0; i < 7; i++) {
                shipVolume.add(0);
            }
        } else {
            Integer dayCount = 0;
            Long daypoint = weekago + 86400000;
            int i = 0;
            int j = 0;
            while (i < shipSailings.size() && j < 7) {
                Long lastRecordPoint = shipSailings.get(i).getCreateTime().getTime();
                if (lastRecordPoint < daypoint) {
                    dayCount++;
                    i++;
                } else {
                    shipVolume.add(dayCount);
                    dayCount = 0;
                    daypoint = daypoint + 86400000;
                    j++;
                }
            }
            while (shipVolume.size() < 7) {
                shipVolume.add(dayCount);
                dayCount = 0;
            }
        }
        Map<String, List<Integer>> weekState = new HashMap<>();
        weekState.put("worker", workerVolume);
        weekState.put("ship", shipVolume);
        return weekState;
    }

    @Override
    public Map<String, Integer> bigScreenDolphinInfo() {
        Date monthStart = DateUtils.getMonthStart();
        LambdaQueryWrapper<MonitorDolphinLogDetail> dolphinWrapper = new LambdaQueryWrapper<>();
        dolphinWrapper.eq(MonitorDolphinLogDetail::getDelFlag, Common.delete_flag.OK);
        dolphinWrapper.orderByDesc(MonitorDolphinLogDetail::getCreateTime);
        dolphinWrapper.gt(MonitorDolphinLogDetail::getCreateTime, monthStart);
        List<MonitorDolphinLogDetail> list = watchDolphinLogDetailService.list(dolphinWrapper);
        long count = list.stream().filter(logDetail -> "1".equals(logDetail.getIsConstructionAffected())).count();
        Map<String, Integer> dolphinInfo = new HashMap<>();
        dolphinInfo.put("total", list.size());
        dolphinInfo.put("able", (int) count);
        dolphinInfo.put("unable", list.size() - (int) count);
        return dolphinInfo;
    }

    @Override
    public Map bigScreenWeatherInfo() {
        //气象趋势
        Map weather7day = seaEnvironmentHourService.dailyWeather7day(0);
        return weather7day;
    }

    @Override
    public List<ExtremeWeather> bigScreenWeatherAlarm() {
        List<ExtremeWeather> extremeWeathers = extremeWeatherService.list(new LambdaQueryWrapper<ExtremeWeather>()
                .ge(ExtremeWeather::getReleaseDate, new Date(System.currentTimeMillis() - 86400000))
                .orderByDesc(ExtremeWeather::getReleaseDate)
                .orderByDesc(ExtremeWeather::getAlertStatus));
        return extremeWeathers;
    }

    @Override
    public Map<String, Object> bigScreenSchedule() {
        Map<String, Object> result = new HashMap<>();

        //c.getType().equals(ProjectPbsEnum.FJ.getCode()判断海缆风机
        //进度情况
        Map<String, String> projectInfo = projectInfoService.getProjectInfo();

        List<ProjectPbs> pbsList = projectPbsService.list(new LambdaQueryWrapper<ProjectPbs>()
                .eq(ProjectPbs::getDelFlag, Common.delete_flag.OK).eq(ProjectPbs::getIsDisplay, Common.commonality.ONE.toString()));
        List<ProjectProcess> processList = projectProcessService.list(new LambdaQueryWrapper<ProjectProcess>()
                .eq(ProjectProcess::getDelFlag, Common.delete_flag.OK));
        Map<String, ProjectProcess> processMap = processList.stream().collect(Collectors.toMap(ProjectProcess::getId, v -> v));
        Map<String, Map<String, Integer>> processInfoMap = processList.stream().collect(Collectors.groupingBy(ProjectProcess::getType, Collectors.toMap(ProjectProcess::getId, ProjectProcess::getWeight)));
        List<ConstructionLog> constructionLogs = constructionLogService.list(new LambdaQueryWrapper<ConstructionLog>()
                .eq(ConstructionLog::getDelFlag, Common.delete_flag.OK));
        Map<String, Map<String, ConstructionLog>> constructionLogMap = constructionLogs.stream().collect(Collectors.groupingBy(ConstructionLog::getPbsId, Collectors.toMap(ConstructionLog::getProcessId, v -> v, (v1, v2) -> v2)));

        Map<String, List<ConstructionLog>> constructionLogList = constructionLogs.stream().collect(Collectors.groupingBy(ConstructionLog::getPbsId, Collectors.toList()));
        Integer fjComplete = 0;
        Integer fjTotle = 0;
        Integer hlComplete = 0;
        Integer hlTotle = 0;
        List<PbsVo> fjList = new ArrayList<>();
        List<PbsVo> hlList = new ArrayList<>();
        for (ProjectPbs pbs : pbsList) {
            PbsVo vo = new PbsVo(pbs);
            vo.setCompletedProcess(new ArrayList<>());
            Map<String, Integer> weightMap = processInfoMap.get(pbs.getApplicableProcess());
            Map<String, ConstructionLog> stringConstructionLogMap = constructionLogMap.get(pbs.getId());
            List<ConstructionLog> completeProcess = constructionLogList.get(pbs.getId());
            if (completeProcess != null) {
                for (ConstructionLog logItem : completeProcess) {
                    ProjectProcess projectProcess = processMap.get(logItem.getProcessId());
                    if (projectProcess != null) {
                        logItem.setModelSign(projectProcess.getModelSign());
                    }
                }
                vo.setCompletedProcess(completeProcess);
            }
            Integer pbsComplete = 0;
            Integer pbsTotal = 0;
            if (weightMap != null && stringConstructionLogMap != null) {
                for (Map.Entry<String, Integer> entry : weightMap.entrySet()) {
                    if (entry.getValue() != null) {
                        pbsTotal += entry.getValue();
                    }
                    ConstructionLog processLog = stringConstructionLogMap.get(entry.getKey());
                    if (processLog != null && Common.commonality.TWO.equals(processLog.getConstructionState())) {
                        if (entry.getValue() != null) {
                            pbsComplete += entry.getValue();
                        }
                    }
                }
                boolean isCompleted = false;
                vo.setPbsComplete(pbsComplete);
                if (pbsComplete.equals(Common.commonality.ZERO)) {
                    vo.setConstructionStatus(Common.commonality.ZERO);
                } else if (pbsComplete < pbsTotal) {
                    vo.setConstructionStatus(Common.commonality.ONE);
                } else if (pbsComplete.equals(pbsTotal)) {
                    vo.setConstructionStatus(Common.commonality.TWO);
                    isCompleted = true;
                }
                vo.setIsCompleted(isCompleted);

            }else if (weightMap != null) {
                for (Map.Entry<String, Integer> entry : weightMap.entrySet()) {
                    if (entry.getValue() != null) {
                        pbsTotal += entry.getValue();
                    }
                }
            }
            if (ProjectPbsEnum.FJ.getCode().equals(pbs.getType())) {
                fjComplete += pbsComplete;
                fjTotle += pbsTotal;
                fjList.add(vo);
            } else if (ProjectPbsEnum.HL.getCode().equals(pbs.getType())) {
                hlList.add(vo);
                hlComplete += pbsComplete;
                hlTotle += pbsTotal;
            }
        }
        if (hlTotle > 0) {
            BigDecimal percents = new BigDecimal(hlComplete).multiply(new BigDecimal(100)).divide(new BigDecimal(hlTotle), RoundingMode.CEILING).setScale(0, RoundingMode.HALF_UP);
            projectInfo.put("hlPercent", percents.toString());
        } else {
            projectInfo.put("hlPercent", "0");
        }
        if (fjTotle > 0) {
            BigDecimal percents = new BigDecimal(fjComplete).multiply(new BigDecimal(100)).divide(new BigDecimal(fjTotle), RoundingMode.CEILING).setScale(0, RoundingMode.HALF_UP);
            projectInfo.put("fjPercent", percents.toString());
        } else {
            projectInfo.put("fjPercent", "0");
        }
        //项目总体进度情况
        result.put("projectInfo", projectInfo);
        //各pbs施工情况
        result.put("hlList", hlList);
        result.put("fjList", fjList);
        return result;
    }

    @Override
    public Map<String, Object> getHomeInfo() {
        Map<String, Object> result = new HashMap<>();

        return result;
    }


    @Override
    public Result<?> schedule(String id) {
        Map<String, Object> objectMap = new HashMap<>();

        LambdaQueryWrapper<ConstructionLog> logWrapper = new LambdaQueryWrapper<>();
        logWrapper.eq(ConstructionLog::getPbsId, id);
        List<ConstructionLog> constructionLogs = constructionLogService.list(logWrapper);

        ProjectPbs projectPbs = projectPbsService.getById(id);

        LambdaQueryWrapper<ProjectProcess> processQueryWrapper = new LambdaQueryWrapper<>();
        processQueryWrapper.eq(ProjectProcess::getType, projectPbs.getApplicableProcess());
        processQueryWrapper.orderByDesc(ProjectProcess::getProcessCode);
        List<ProjectProcess> projectProcesses = projectProcessService.list(processQueryWrapper);

        if (constructionLogs.isEmpty() && projectProcesses.isEmpty()) {
            objectMap.put("nextProcess", projectProcesses.get(projectProcesses.size() - 1).getName());
            objectMap.put("totalUseTime", 0);
            return Result.OK(objectMap);
        }
        BigDecimal reduce = constructionLogs.stream().map(ConstructionLog::getUseTime).reduce(BigDecimal.ZERO, BigDecimal::add);
        Map<String, ConstructionLog> listMap = constructionLogs.stream().collect(Collectors.toMap(ConstructionLog::getProcessId, Function.identity()));

        ProjectProcess lastProcess = null;
        for (ProjectProcess projectProcess : projectProcesses) {

            ConstructionLog constructionLog = listMap.get(projectProcess.getId());
            if (!ObjectUtils.isEmpty(constructionLog)) {
                objectMap.put("nowProcess", projectProcess.getName());
                objectMap.put("nowTheoryHours", projectProcess.getTheoryHours());
                objectMap.put("nowActualHours", constructionLog.getUseTime());
                objectMap.put("totalUseTime", reduce.doubleValue());
                if (ObjectUtils.isEmpty(lastProcess)) {
                    objectMap.put("nextProcess", projectProcess.getName());
                } else {
                    objectMap.put("nextProcess", lastProcess.getName());
                }
                return Result.OK(objectMap);
            }
            lastProcess = projectProcess;
        }

        return Result.OK();
    }
}
