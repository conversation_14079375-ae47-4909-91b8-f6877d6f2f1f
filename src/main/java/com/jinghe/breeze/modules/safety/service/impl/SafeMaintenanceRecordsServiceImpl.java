package com.jinghe.breeze.modules.safety.service.impl;

import com.jinghe.breeze.modules.safety.entity.SafeMaintenanceRecords;
import com.jinghe.breeze.modules.safety.mapper.SafeMaintenanceRecordsMapper;
import com.jinghe.breeze.modules.safety.service.ISafeMaintenanceRecordsService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: ai设备维护记录
 * @Author: jeecg-boot
 * @Date:   2025-06-22
 * @Version: V1.0
 */
@Service
public class SafeMaintenanceRecordsServiceImpl extends ServiceImpl<SafeMaintenanceRecordsMapper, SafeMaintenanceRecords> implements ISafeMaintenanceRecordsService {

}
