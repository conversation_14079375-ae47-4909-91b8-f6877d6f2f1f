package com.jinghe.breeze.modules.wind.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.common.constants.enums.WindNotifyRecordEnum;
import com.jinghe.breeze.common.utils.MapWktUtil;
import com.jinghe.breeze.modules.person.entity.PersonInfo;
import com.jinghe.breeze.modules.person.service.IPersonInfoService;
import com.jinghe.breeze.modules.project.entity.ProjectFence;
import com.jinghe.breeze.modules.project.entity.ProjectUnit;
import com.jinghe.breeze.modules.project.service.IProjectFenceService;
import com.jinghe.breeze.modules.project.service.IProjectUnitService;
import com.jinghe.breeze.modules.ship.entity.ShipInfo;
import com.jinghe.breeze.modules.ship.mapper.ShipInfoMapper;
import com.jinghe.breeze.modules.ship.service.IShipApiService;
import com.jinghe.breeze.modules.wind.entity.WindNotifRecord;
import com.jinghe.breeze.modules.wind.entity.WindNotification;
import com.jinghe.breeze.modules.wind.entity.WindReport;
import com.jinghe.breeze.modules.wind.entity.WindReportDetail;
import com.jinghe.breeze.modules.wind.entity.vo.WindReportDepartmentVo;
import com.jinghe.breeze.modules.wind.entity.vo.WindReportShipVo;
import com.jinghe.breeze.modules.wind.mapper.WindNotificationMapper;
import com.jinghe.breeze.modules.wind.service.IWindNotifRecordService;
import com.jinghe.breeze.modules.wind.service.IWindNotificationService;
import com.jinghe.breeze.modules.wind.service.IWindReportDetailService;
import com.jinghe.breeze.modules.wind.service.IWindReportService;
import jodd.util.StringUtil;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.system.entity.SysAnnouncement;
import org.jeecg.modules.system.entity.SysAnnouncementSend;
import org.jeecg.modules.system.service.ISysAnnouncementSendService;
import org.jeecg.modules.system.service.ISysAnnouncementService;
import org.jeecg.modules.system.service.ISysDepartService;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static org.junit.Assert.assertEquals;

/**
 * @Description: wind_notification
 * @Author: jeecg-boot
 * @Date: 2024-05-22
 * @Version: V1.0
 */
@Service
public class WindNotificationServiceImpl extends ServiceImpl<WindNotificationMapper, WindNotification> implements IWindNotificationService {
    @Autowired
    private IWindReportService windReportService;
    @Autowired
    private ISysAnnouncementSendService sysAnnouncementSendService;
    @Autowired
    private ISysAnnouncementService sysAnnouncementService;
    @Autowired
    private IWindNotifRecordService windRecordService;

    @Autowired
    private IProjectFenceService fenceService;
    @Autowired
    private IWindReportDetailService windReportDetailService;
    @Autowired
    private IWindNotifRecordService notifRecordService;
    @Autowired
    private IPersonInfoService personService;
    @Autowired
    private ISysDepartService departService;
    @Autowired
    private IProjectUnitService iProjectUnitService;
    @Autowired(required = false)
    private ShipInfoMapper shipInfoMapper;
    @Autowired
    private IShipApiService shipApiService;

    private List<Map<String, String>> getReceiverMap(String receiverString) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.readValue(receiverString, new TypeReference<List<Map<String, String>>>() {
            });
        } catch (Exception e) {
            throw new RuntimeException("解析联系人失败");
        }

    }

    @Test
    public void test() {
        List<Map<String, String>> res = getReceiverMap("[{\"id\":\"1797511724510711810\",\"name\":\"jeecg\",\"phone\":\"18611111111\",\"userId\":\"a75d45a015c44384a04449ee80dc3503\"},{\"id\":\"1797511563252305922\",\"name\":\"管理员\",\"phone\":\"18611788525\",\"userId\":\"e9ca23d68d884d4ebb19d07889727dae\"}]");
        assertEquals(2, res.size());
    }

    /**
     * 添加接口,发布通知需要新增通知记录  站内信
     *
     * @param windNotification
     * @return
     */

    @Transactional(rollbackFor = Exception.class)
    public String addWindNotification(WindNotification windNotification) {
        String contactString = windNotification.getContactInfo();
        String receiverString = windNotification.getReceiverInfo();
        if (contactString == null || receiverString == null) {
            throw new JeecgBootException("参数不完整");
        }
        if (!validateContact(contactString)) {
            throw new JeecgBootException("联系人格式不正确");
        }
        List<String> recieveUserIdList = getReceiverMap(receiverString).stream().map(map -> map.get("userId")).collect(Collectors.toList());
        //通知人id列表
        if (recieveUserIdList.isEmpty()) {
            throw new JeecgBootException("通知人员不能为空!");
        }
        windNotification.setState(0);
        baseMapper.insert(windNotification);
        return windNotification.getId();
    }

    private Boolean validateContact(String contactString) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            Map<String, String> map = mapper.readValue(contactString, Map.class);
            return true;
        } catch (Exception e) {
            return false;
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public Result<?> editWindNotification(WindNotification windNotification) {
        WindNotification entity = baseMapper.selectById(windNotification.getId());
        if (entity == null) {
            return Result.error(404, "记录不存在!");
        }
        if (entity.getState() != 0) {
            return Result.error(400, "非草稿状态,禁止编辑");
        }
        baseMapper.updateById(windNotification);
        return Result.OK("编辑成功!");
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> releaseWindNotification(WindNotification windNotification) {
        String idTmp = windNotification.getId();
        if (StringUtil.isEmpty(idTmp)) {
            //这里是app的接口  如果id为空则为直接上传  否则为存草稿
            idTmp = addWindNotification(windNotification);
        }
        WindNotification entity = baseMapper.selectById(idTmp);
        if (entity == null) {
            return Result.error(404, "记录不存在!");
        }
        entity = windNotification; //如果记录存在则使用前端传进来的数据
        if (!Objects.equals(entity.getState(), 0)) {
            return Result.error(400, "非草稿状态,无法发布");
        }
        List<String> recieveUserIdList = getReceiverMap(entity.getReceiverInfo()).stream().map(map -> map.get("userId")).collect(Collectors.toList());
        //通知人id列表
        if (recieveUserIdList.size() == 0) {
            return Result.error(404, "通知人员不能为空!");
        }
        List<WindNotification> latestNotifyCationList = baseMapper.selectList(new LambdaQueryWrapper<WindNotification>()
                .eq(WindNotification::getState, 1));
        Optional<WindNotification> latestNotifyCation = latestNotifyCationList.stream().filter(notify -> {
            return notify.getPlanEndDate().compareTo(new Date()) >= 0; //未超期的
        }).findFirst();

        if (latestNotifyCation.isPresent()) {
            return Result.error(400, "存在未结束的避风通知,无法发布新的通知");
        }
        String notifyId = windNotification.getId();
        //对于每个接收人, 生成有一条避风通知记录  同时增加一条站内信
        List<WindNotifRecord> recordList = new ArrayList<>();
        recieveUserIdList.forEach(id -> {
            WindNotifRecord record = new WindNotifRecord();
            record.setNotificationId(notifyId);
            record.setUserId(id);
            record.setState(WindNotifyRecordEnum.NOTDONE.getCode());
            record.setWindNotification(windNotification);
            recordList.add(record);
        });
        windRecordService.saveBatch(recordList);
        addAnnounceMent(windNotification, recieveUserIdList);
        entity.setState(1);
        baseMapper.updateById(entity);
        return Result.OK("发布成功!");
    }

    @Override
    public Result<?> endWindNotification(WindNotification windNotification) {
        WindNotification entity = baseMapper.selectById(windNotification.getId());
        if (entity == null) {
            return Result.error(404, "记录不存在!");
        }
        if (entity.getState() != 1) {
            return Result.error(400, "发布中的通知才能结束");
        }

        entity.setState(2);
        baseMapper.updateById(entity);
        return Result.OK("操作成功!");
    }

    private void addAnnounceMent(WindNotification windNotification, List<String> ids) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        SysAnnouncement announce = new SysAnnouncement();
        announce.setMsgType("USER"); //指定用户
        announce.setDelFlag(CommonConstant.DEL_FLAG_0.toString()); //
        announce.setSendStatus("1"); //已发布
        announce.setSender(sysUser.getUsername());
        announce.setSendTime(new Date());
        announce.setUserIds(windNotification.getReceiverInfo());
        announce.setMsgContent(windNotification.getContent());
        announce.setTitile(windNotification.getName());
        announce.setMsgAbstract(windNotification.getName());
        sysAnnouncementService.save(announce);
        List<SysAnnouncementSend> sendList = new ArrayList<>();
        ids.forEach(
                id -> {
                    SysAnnouncementSend send = new SysAnnouncementSend();
                    send.setAnntId(announce.getId());
                    send.setUserId(id);
                    send.setReadFlag("0");
                    sendList.add(send);
                }
        );
        sysAnnouncementSendService.saveBatch(sendList);
    }

    public Map<String, List<WindNotification>> getRecentNotify() {
        final List<WindNotification> publishedList = new ArrayList<>();
        final List<WindNotification> unPublishedList = new ArrayList<>();
        final List<WindNotification> hasEndedList = new ArrayList<>();
        List<WindNotification> notifyList = baseMapper.selectList(new LambdaQueryWrapper<WindNotification>()
                .ge(WindNotification::getCreateTime, new Date(System.currentTimeMillis() - 30L * 24 * 3600 * 1000))
                .orderByDesc(WindNotification::getCreateTime)); //最新的在前
        notifyList.forEach(x -> {
            if (x.getState().equals(0)) {
                unPublishedList.add(x);
            } else if (x.getState().equals(1)) {
                publishedList.add(x);
            } else if (x.getState().equals(2)) {
                hasEndedList.add(x);
            }
        });
        Map<String, List<WindNotification>> map = new HashMap<>();
        map.put("publishedList", publishedList);
        map.put("unPublishedList", unPublishedList);
        map.put("hasEndedList", hasEndedList);
        return map;
    }

    @Override
    public Result<?> getDetail() {
        List<WindNotification> notifyList = baseMapper.selectList(new LambdaQueryWrapper<WindNotification>()
                .eq(WindNotification::getState, 1)
                .orderByDesc(WindNotification::getCreateTime));
        if (notifyList.isEmpty()) {
            return Result.OK("暂无避风通知!");
        }

        Map result = new HashMap();
        WindNotification notify = notifyList.get(0);
        result.put("notify", notify);
        List<WindReportDepartmentVo> reportDetail = new ArrayList<>();
        LambdaQueryWrapper<WindNotifRecord> notiWrapper = new LambdaQueryWrapper<>();
        notiWrapper.eq(WindNotifRecord::getNotificationId, notify.getId());
        notiWrapper.eq(WindNotifRecord::getState, WindNotifyRecordEnum.NOTDONE.getCode());
        List<WindNotifRecord> notifRecords = notifRecordService.list(notiWrapper);
        Set<String> departments = new HashSet<>();
        for (WindNotifRecord item : notifRecords) {
            LambdaQueryWrapper<PersonInfo> personWrapper = new LambdaQueryWrapper<>();
            personWrapper.eq(PersonInfo::getUserId, item.getUserId());
            personWrapper.eq(PersonInfo::getDelFlag, Common.delete_flag.OK);
            List<PersonInfo> list = personService.list(personWrapper);
            if (!list.isEmpty()) {
                ProjectUnit byId = iProjectUnitService.getById(list.get(0).getDepartment());
                if (byId != null) {
                    departments.add(byId.getName());
                }
            }
        }
        for (String item : departments) {
            WindReportDepartmentVo vo = new WindReportDepartmentVo();
            vo.setName(item);
            vo.setState(0);
            reportDetail.add(vo);
        }
        LambdaQueryWrapper<WindReport> reportWrapper = new LambdaQueryWrapper<>();
        reportWrapper.eq(WindReport::getNotifyId, notify.getId());
        List<WindReport> reports = windReportService.list(reportWrapper);
        if (reports.isEmpty()) {
            result.put("reportDetail", reportDetail);
            return Result.OK(result);
        } else {
            Set<String> fenceIds = new HashSet<>();
            for (WindReport report : reports) {
                WindReportDepartmentVo vo = new WindReportDepartmentVo();
                vo.setName(report.getUnitName());
                vo.setState(1);
                LambdaQueryWrapper<WindReportDetail> detailWrapper = new LambdaQueryWrapper<>();
                detailWrapper.eq(WindReportDetail::getReportId, report.getId());
                List<WindReportDetail> details = windReportDetailService.list(detailWrapper);
                if (details.isEmpty()) {
                    reportDetail.add(vo);
                } else {
                    List<WindReportShipVo> shipVos = new ArrayList<>();
                    for (WindReportDetail item : details) {
                        fenceIds.add(item.getFenceId());
                        WindReportShipVo shipVo = new WindReportShipVo();
                        BeanUtils.copyProperties(item, shipVo);
                        shipVo.setDepartment(report.getUnitName());
                        ShipInfo shipInfo = shipInfoMapper.selectById(item.getShipId());
                        if (shipInfo != null) {
                            shipVo.setShipName(shipInfo.getName());
                        }
                        shipVos.add(shipVo);
                    }
                    vo.setShips(shipVos);
                    reportDetail.add(vo);
                }
            }
            if (!fenceIds.isEmpty()) {
                LambdaQueryWrapper<ProjectFence> fenceWrapper = new LambdaQueryWrapper<>();
                fenceWrapper.in(ProjectFence::getId, fenceIds);
                List<ProjectFence> fences = fenceService.list(fenceWrapper);
                result.put("fences", fences);
                Map<String, JSONObject> shipMMSIList = new HashMap(0);
                for (ProjectFence fence : fences) {
                    //String fenceRadius = fence.getFenceRadius();
                    //[[108.40965,21.68835],[108.36983,21.65196],[108.34854,21.55652],[108.38425,21.53592],[108.40004,21.59772],[108.43506,21.66981],[108.40965,21.68835]]
                    //String xy = fenceRadius.replaceAll("\\[\\[", "").replaceAll("]]", "").replaceAll("],\\[", "-");
                    String xy = MapWktUtil.points(fence.getFenceRadius());
                    Result<?> result1 = shipApiService.GetAreaShip(xy);
                    JSONArray ships = JSONArray.parseArray(result1.getResult().toString());
                    if (ships != null && !ships.isEmpty()) {
                        for (Object datum : ships) {
                            JSONObject jsonObject = JSON.parseObject(datum.toString());
                            String mmsi = jsonObject.getString("mmsi");
                            shipMMSIList.put(mmsi, jsonObject);
                        }
                    }


//                    List ships = (List) result1.getResult();
//                    if (ships != null && !ships.isEmpty()) {
//                        for (Object datum : ships) {
//                            JSONObject jsonObject = JSON.parseObject(datum.toString());
//                            String mmsi = jsonObject.getString("mmsi");
//                            shipMMSIList.put(mmsi, jsonObject);
//                        }
//                    }
                }
                Map<String, WindReport> collect = reports.stream().collect(Collectors.toMap(WindReport::getId, v -> v));

                for (WindReportDepartmentVo reportVo : reportDetail) {
                    if (reportVo.getShips() != null && !reportVo.getShips().isEmpty()) {
                        for (WindReportShipVo item : reportVo.getShips()) {
                            if (shipMMSIList.get(item.getMmsi()) == null) {
                                //TODO 查询船舶位置
                                item.setStatus(0);
                            } else {
                                JSONObject jsonObject = shipMMSIList.get(item.getMmsi());
                                item.setStatus(1);
                                item.setLon(jsonObject.getIntValue("lon"));
                                item.setLat(jsonObject.getIntValue("lat"));
                            }
                        }
                    }
                }
            }

            result.put("reportDetail", reportDetail);
            return Result.OK(result);
        }
    }
}
