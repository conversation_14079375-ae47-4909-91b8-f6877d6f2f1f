package com.jinghe.breeze.modules.project.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.*;
/**
 * @Description: 关联工序
 * @Author: jeecg-boot
 * @Date:   2024-10-10
 * @Version: V1.0
 */
@Data
@TableName("construction_plan_process")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="construction_plan_process对象", description="关联工序")
public class ConstructionPlanProcess implements Serializable {
    private static final long serialVersionUID = 1L;

	/**标识*/
	@TableId(type = IdType.ASSIGN_ID)
    @NotNull(message = "标识不能为空!")

    @ApiModelProperty(value = "标识")
    private String id;
	/**创建人*/

    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**更新人*/

    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
	/**删除标识*/
	@Excel(name = "删除标识", width = 15)

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;
	/**组织机构编码*/

    @ApiModelProperty(value = "组织机构编码")
    private String sysOrgCode;
	/**项目id*/
	@Excel(name = "项目id", width = 15)

    @ApiModelProperty(value = "项目id")
    private String projectId;
	/**工序编码*/
	@Excel(name = "工序编码", width = 15)

    @ApiModelProperty(value = "工序编码")
    private String processCode;
	/**计划编制code*/
	@Excel(name = "计划编制code", width = 15)

    @ApiModelProperty(value = "计划编制code")
    private String planCode;
	/**pbsID*/
	@Excel(name = "pbsID", width = 15)

    @ApiModelProperty(value = "pbsID")
    private String pbsId;
	/**工序id*/
	@Excel(name = "工序id", width = 15)

    @ApiModelProperty(value = "工序id")
    private String processId;
}
