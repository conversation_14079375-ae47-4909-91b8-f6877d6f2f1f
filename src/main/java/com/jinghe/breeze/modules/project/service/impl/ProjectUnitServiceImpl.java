package com.jinghe.breeze.modules.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.modules.project.entity.ProjectUnit;
import com.jinghe.breeze.modules.project.mapper.ProjectUnitMapper;
import com.jinghe.breeze.modules.project.service.IProjectUnitService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: project_unit
 * @Author: jeecg-boot
 * @Date:   2024-05-07
 * @Version: V1.0
 */
@Service
public class ProjectUnitServiceImpl extends ServiceImpl<ProjectUnitMapper, ProjectUnit> implements IProjectUnitService {

    @Override
    public List<ProjectUnit> listNoPage() {
        return baseMapper.selectList(new LambdaQueryWrapper<ProjectUnit>().orderByDesc(ProjectUnit::getCreateTime));
    }

}
