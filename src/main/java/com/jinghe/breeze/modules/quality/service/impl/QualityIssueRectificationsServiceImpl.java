package com.jinghe.breeze.modules.quality.service.impl;

import com.jinghe.breeze.modules.quality.entity.QualityIssueRectifications;
import com.jinghe.breeze.modules.quality.mapper.QualityIssueRectificationsMapper;
import com.jinghe.breeze.modules.quality.service.IQualityIssueRectificationsService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 质量整改
 * @Author: jeecg-boot
 * @Date:   2025-06-21
 * @Version: V1.0
 */
@Service
public class QualityIssueRectificationsServiceImpl extends ServiceImpl<QualityIssueRectificationsMapper, QualityIssueRectifications> implements IQualityIssueRectificationsService {

}
