package com.jinghe.breeze.modules.ship.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: ship_outgoing
 * @Author: jeecg-boot
 * @Date:   2024-05-07
 * @Version: V1.0
 */
@Data
@TableName("ship_outgoing")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="ship_outgoing对象", description="ship_outgoing")
public class ShipOutgoing implements Serializable {
    private static final long serialVersionUID = 1L;

    /**标识*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "标识")
    private java.lang.String id;
    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**更新时间*/
    @Excel(name = "更新时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateDate;
    /**删除标识*/
    @Excel(name = "删除标识", width = 15)
    @ApiModelProperty(value = "删除标识")
    private java.lang.Integer delFlag;
    /**组织机构编码*/
    @ApiModelProperty(value = "组织机构编码")
    private java.lang.String sysOrgCode;
    /**项目id*/
    @Excel(name = "项目id", width = 15)
    @ApiModelProperty(value = "项目id")
    private java.lang.String projectId;
    /**关联船舶信息库id*/
    @Excel(name = "关联船舶信息库id", width = 15)
    @ApiModelProperty(value = "关联船舶信息库id")
    private java.lang.String shipDataId;
    /**船东*/
    @Excel(name = "船东", width = 15)
    @ApiModelProperty(value = "船东")
    private java.lang.String shipownerName;
    /**船舶名称*/
    @Excel(name = "船舶名称", width = 15, dictTable = "ship_info", dicText = "name", dicCode = "id")
    @Dict(dictTable = "ship_info", dicText = "name", dicCode = "id")
    @ApiModelProperty(value = "船舶名称")
    private java.lang.String name;
    /**船舶类型*/
    @Excel(name = "船舶类型", width = 15, dicCode = "ship_typ_code")
    @Dict(dicCode = "ship_typ_code")
    @ApiModelProperty(value = "船舶类型")
    private java.lang.String typeValue;
    /**MMSI*/
    @Excel(name = "MMSI", width = 15)
    @ApiModelProperty(value = "MMSI")
    private java.lang.String mmsi;
    /**进/离场状态*/
    @Excel(name = "进/离场状态", width = 15, dicCode = "ship_exit_status")
    @Dict(dicCode = "ship_exit_status")
    @ApiModelProperty(value = "进/离场状态")
    private java.lang.String isOut;
    /**使用单位*/
    @Excel(name = "使用单位", width = 15, dictTable = "project_unit", dicText = "name", dicCode = "id")
    @Dict(dictTable = "project_unit", dicText = "name", dicCode = "id")
    @ApiModelProperty(value = "使用单位")
    private java.lang.String unit;
    /**进退场时间*/
    @Excel(name = "进退场时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "进退场时间")
    private java.util.Date date;
    /**备注*/
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
}
