package com.jinghe.breeze.modules.education.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jinghe.breeze.common.enums.QuestionTypeEnum;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;

/**
 * @Description: 试卷详情
 * @Author: jeecg-boot
 * @Date:   2025-06-25
 * @Version: V1.0
 */
@Data
@TableName("edu_exam_paper_detail")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="edu_exam_paper_detail对象", description="试卷详情")
public class EduExamPaperDetail implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;

    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;

	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;

	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;

	/**删除标识*/
	@Excel(name = "删除标识", width = 15)
    @ApiModelProperty(value = "删除标识")
    private String delFlag;


    @ApiModelProperty(value = "题目类型")
    private String type;

    @ApiModelProperty(value = "考卷id")
    private String paperId;

    @ApiModelProperty(value = "原始题目ID，用于追溯题目来源")
    private String questionId;

    @ApiModelProperty(value = "题干快照")
    private String content;

    @ApiModelProperty(value = "选项json快照")
    private String options;

    @ApiModelProperty(value = "答案快照")
    private String answer;

    @ApiModelProperty(value="解析")
    private String explanation;

    @ApiModelProperty(value = "题目分值快照")
    private BigDecimal score;



}
