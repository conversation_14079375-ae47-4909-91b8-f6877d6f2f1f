package com.jinghe.breeze.modules.project.entity;

import org.jeecg.modules.system.entity.SysCategory;

import java.util.List;




public class MySysCategory extends SysCategory {

    private List<MySysCategory> children;

    public MySysCategory() {
    }

    public List<MySysCategory> getChildren() {
        return children;
    }

    public void setChildren(List<MySysCategory> children) {
        this.children = children;
    }
}
