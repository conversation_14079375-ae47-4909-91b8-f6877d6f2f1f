package com.jinghe.breeze.modules.ship.service.impl;

import com.jinghe.breeze.modules.ship.service.IShipApiService;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ShipApiServiceImpl implements IShipApiService {

    @Override
    public Result<?> GetAreaShip(String xy) {
        //[[119.210739,38.000950],[119.187400,38.049800],[119.444300,38.018900],[119.448600,38.012600],[119.352000,38.009900],[119.352121,37.992417]]
        String url = "http://api.shipxy.com/apicall/GetAreaShip?v=4&k=76ef6af48489499e99db757790b1dcff" +
                "&enc=1&jsf=func&scode=0&xy=119210739,38000950-119187400,38049800-119444300,38018900-119448600,38012600-119352000,38009900-119352121,37992417";
        Object data = "[{\"mmsi\":413301910,\"cnname\":\"\",\"shiptype\":90,\"imo\":9573426,\"lon\":108749370,\"sog\":5247,\"dest\":\"ZYH5\",\"newtype\":99,\"trail\":480,\"dest_std\":\"ZYH5\",\"eta\":\"09-01 06:00\",\"destcode\":\"\",\"rot\":-1200,\"callsign\":\"BFAC9\",\"lat\":21368410,\"length\":680,\"hdg\":23600,\"From\":0,\"navistat\":0,\"lasttime\":1725499417,\"draught\":5700,\"left\":60,\"name\":\"MIN SHENG 006\",\"width\":150,\"ShipID\":413301910,\"cog\":18000,\"eta_std\":\"2024-09-01 06:00:00\"}," +
                "{\"mmsi\":413301911,\"cnname\":\"\",\"shiptype\":90,\"imo\":9573426,\"lon\":108749350,\"sog\":5247,\"dest\":\"ZYH5\",\"newtype\":99,\"trail\":480,\"dest_std\":\"ZYH5\",\"eta\":\"09-01 06:00\",\"destcode\":\"\",\"rot\":-1200,\"callsign\":\"BFAC9\",\"lat\":21458020,\"length\":680,\"hdg\":23600,\"From\":0,\"navistat\":0,\"lasttime\":1725499417,\"draught\":5700,\"left\":60,\"name\":\"MIN SHENG 007\",\"width\":150,\"ShipID\":413301911,\"cog\":23200,\"eta_std\":\"2024-09-01 06:00:00\"}," +
                "{\"mmsi\":413301912,\"cnname\":\"\",\"shiptype\":90,\"imo\":9573426,\"lon\":108749360,\"sog\":5247,\"dest\":\"ZYH5\",\"newtype\":99,\"trail\":480,\"dest_std\":\"ZYH5\",\"eta\":\"09-01 06:00\",\"destcode\":\"\",\"rot\":-1200,\"callsign\":\"BFAC9\",\"lat\":21354850,\"length\":680,\"hdg\":23600,\"From\":0,\"navistat\":0,\"lasttime\":1725499417,\"draught\":5700,\"left\":60,\"name\":\"MIN SHENG 008\",\"width\":150,\"ShipID\":413301912,\"cog\":9000,\"eta_std\":\"2024-09-01 06:00:00\"}," +
                "{\"mmsi\":413301913,\"cnname\":\"\",\"shiptype\":90,\"imo\":9573426,\"lon\":108866902,\"sog\":5247,\"dest\":\"ZYH5\",\"newtype\":99,\"trail\":480,\"dest_std\":\"ZYH5\",\"eta\":\"09-01 06:00\",\"destcode\":\"\",\"rot\":-1200,\"callsign\":\"BFAC9\",\"lat\":21354070,\"length\":680,\"hdg\":23600,\"From\":0,\"navistat\":0,\"lasttime\":1725499417,\"draught\":5700,\"left\":60,\"name\":\"MIN SHENG 009\",\"width\":150,\"ShipID\":413301913,\"cog\":14600,\"eta_std\":\"2024-09-01 06:00:00\"}]";
        return Result.OK(data);
//        try {
//            String json = HttpTools.doGet(url, null);
//            JSONObject jsonObject = JSONObject.parseObject(json.substring(json.indexOf("(") + 1, json.lastIndexOf(")")));
//            if (Integer.valueOf(jsonObject.getString("status")) == 0) {
//                System.out.println(jsonObject.get("data"));
//                Object data = "[{\"mmsi\":413301910,\"cnname\":\"\",\"shiptype\":90,\"imo\":9573426,\"lon\":108749370,\"sog\":5247,\"dest\":\"ZYH5\",\"newtype\":99,\"trail\":480,\"dest_std\":\"ZYH5\",\"eta\":\"09-01 06:00\",\"destcode\":\"\",\"rot\":-1200,\"callsign\":\"BFAC9\",\"lat\":21368410,\"length\":680,\"hdg\":23600,\"From\":0,\"navistat\":0,\"lasttime\":1725499417,\"draught\":5700,\"left\":60,\"name\":\"MIN SHENG 006\",\"width\":150,\"ShipID\":413301910,\"cog\":18000,\"eta_std\":\"2024-09-01 06:00:00\"}," +
//                        "{\"mmsi\":413301911,\"cnname\":\"\",\"shiptype\":90,\"imo\":9573426,\"lon\":108749350,\"sog\":5247,\"dest\":\"ZYH5\",\"newtype\":99,\"trail\":480,\"dest_std\":\"ZYH5\",\"eta\":\"09-01 06:00\",\"destcode\":\"\",\"rot\":-1200,\"callsign\":\"BFAC9\",\"lat\":21458020,\"length\":680,\"hdg\":23600,\"From\":0,\"navistat\":0,\"lasttime\":1725499417,\"draught\":5700,\"left\":60,\"name\":\"MIN SHENG 007\",\"width\":150,\"ShipID\":413301911,\"cog\":23200,\"eta_std\":\"2024-09-01 06:00:00\"}," +
//                        "{\"mmsi\":413301912,\"cnname\":\"\",\"shiptype\":90,\"imo\":9573426,\"lon\":108749360,\"sog\":5247,\"dest\":\"ZYH5\",\"newtype\":99,\"trail\":480,\"dest_std\":\"ZYH5\",\"eta\":\"09-01 06:00\",\"destcode\":\"\",\"rot\":-1200,\"callsign\":\"BFAC9\",\"lat\":21354850,\"length\":680,\"hdg\":23600,\"From\":0,\"navistat\":0,\"lasttime\":1725499417,\"draught\":5700,\"left\":60,\"name\":\"MIN SHENG 008\",\"width\":150,\"ShipID\":413301912,\"cog\":9000,\"eta_std\":\"2024-09-01 06:00:00\"}," +
//                        "{\"mmsi\":413301913,\"cnname\":\"\",\"shiptype\":90,\"imo\":9573426,\"lon\":108866902,\"sog\":5247,\"dest\":\"ZYH5\",\"newtype\":99,\"trail\":480,\"dest_std\":\"ZYH5\",\"eta\":\"09-01 06:00\",\"destcode\":\"\",\"rot\":-1200,\"callsign\":\"BFAC9\",\"lat\":21354070,\"length\":680,\"hdg\":23600,\"From\":0,\"navistat\":0,\"lasttime\":1725499417,\"draught\":5700,\"left\":60,\"name\":\"MIN SHENG 009\",\"width\":150,\"ShipID\":413301913,\"cog\":14600,\"eta_std\":\"2024-09-01 06:00:00\"}]";
//                return Result.OK(data);
//            } else {
//                return Result.error("区域船舶查询失败");
//            }
//        } catch (Exception e) {
//            log.error("区域船舶查询数据失败：：", e.getMessage());
//            return Result.error("区域船舶查询数据失败");
//        }
    }

    public static void main(String[] args) {
        System.out.println(new ShipApiServiceImpl().GetAreaShip(""));
    }
}
