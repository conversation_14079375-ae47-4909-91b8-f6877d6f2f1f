package com.jinghe.breeze.modules.education.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.*;

/**
 * @Description: 学员答案记录表
 * @Author: jeecg-boot
 * @Date:   2025-06-26
 * @Version: V1.0
 */
@Data
@TableName("edu_exam_detail")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="edu_exam_detail对象", description="学员答案记录表")
public class EduExamDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;

    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;

    /**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;

    /**删除标识*/
    @Excel(name = "删除标识", width = 15)
    @ApiModelProperty(value = "删除标识")
    private String delFlag;

    /**考试实例ID*/
    @Excel(name = "考试实例ID", width = 15, dictTable = "edu_exam", dicText = "id", dicCode = "id")
    @Dict(dictTable = "edu_exam", dicText = "id", dicCode = "id")
    @NotNull(message = "考试实例ID不能为空!")
    @ApiModelProperty(value = "考试实例ID (关联 edu_exam.id)")
    private String examId;



    /**试卷题目ID*/
    @Excel(name = "试卷题目ID", width = 15, dictTable = "edu_exam_paper_detail", dicText = "id", dicCode = "id")
    @Dict(dictTable = "edu_exam_paper_detail", dicText = "id", dicCode = "id")
    @NotNull(message = "试卷题目ID不能为空!")
    @ApiModelProperty(value = "试卷题目ID (关联 edu_exam_paper_detail.id)")
    private String paperDetailId;

    /**学员提交的答案*/
    @Excel(name = "学员提交的答案", width = 15)
    @ApiModelProperty(value = "学员提交的答案")
    private String answer;

    /**本题得分*/
    @Excel(name = "本题得分", width = 15)
    @NotNull(message = "本题得分不能为空!")
    @ApiModelProperty(value = "本题得分")
    private BigDecimal score;

    /**是否正确*/
    @Excel(name = "是否正确", width = 15, dicCode = "yes_no")
    @NotNull(message = "是否正确不能为空!")
    @ApiModelProperty(value = "是否正确")
    private Integer isCorrect;

    @ApiModelProperty(value = "原始题目ID，用于追溯题目来源")
    private String questionId;

}
