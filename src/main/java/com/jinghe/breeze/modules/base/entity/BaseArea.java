package com.jinghe.breeze.modules.base.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
 * @Description: 区域
 * @Author: jeecg-boot
 * @Date: 2024-08-13
 * @Version: V1.0
 */
@Data
@TableName("base_area")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "base_area对象", description = "区域")
public class BaseArea implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
    /**
     * 删除状态（0，正常，1已删除）
     */
    @Excel(name = "删除状态（0，正常，1已删除）", width = 15)
    @ApiModelProperty(value = "删除状态（0，正常，1已删除）")
    private String delFlag;
    /**
     * 区域名称
     */
    @Excel(name = "区域名称", width = 15)
    @ApiModelProperty(value = "区域名称")
    private String name;
    /**
     * 区域code
     */
    @Excel(name = "区域code", width = 15)
    @ApiModelProperty(value = "区域code")
    private String code;
    /**
     * 坐标点X轴
     */
    @Excel(name = "坐标点X轴", width = 15)
    @ApiModelProperty(value = "坐标点X轴")
    private String positionX;
    /**
     * 坐标点Y轴
     */
    @Excel(name = "坐标点Y轴", width = 15)
    @ApiModelProperty(value = "坐标点Y轴")
    private String positionY;
    /**
     * 底图
     */
    @Excel(name = "底图", width = 15)
    @ApiModelProperty(value = "底图")
    private String filePath;
}
