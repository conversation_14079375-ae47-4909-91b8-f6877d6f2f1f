package com.jinghe.breeze.modules.sailingcheck.controller;

import com.jinghe.breeze.modules.sailingcheck.entity.vo.SailingVo;
import com.jinghe.breeze.modules.ship.entity.ShipOutgoing;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import com.jinghe.breeze.modules.sailingcheck.entity.ShipSailingRecord;
import com.jinghe.breeze.modules.sailingcheck.service.IShipSailingRecordService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;


import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: ship_sailing_record
 * @Author: jeecg-boot
 * @Date: 2024-06-18
 * @Version: V1.0
 */
@Api(tags = "ship_sailing_record")
@RestController
@RequestMapping("/sailingcheck/shipSailingRecord")
@Slf4j
public class ShipSailingRecordController extends JeecgController<ShipSailingRecord, IShipSailingRecordService> {
    @Autowired
    private IShipSailingRecordService shipSailingRecordService;

    /**
     * 出航核检
     *
     * @param sailingVo
     * @return
     */
    @AutoLog(value = "出航核检")
    @ApiOperation(value = "出航核检", notes = "出航核检")
    @PostMapping(value = "/leave")
    public Result<?> leave(@Validated @RequestBody SailingVo sailingVo) {
        return shipSailingRecordService.leave(sailingVo);
    }

    /**
     * 返航核检
     *
     * @param sailingVo
     * @return
     */
    @AutoLog(value = "返航核检")
    @ApiOperation(value = "返航核检", notes = "返航核检")
    @PostMapping(value = "/back")
    public Result<?> back(@Validated @RequestBody SailingVo sailingVo) {
        return shipSailingRecordService.back(sailingVo);
    }

    /**
     * 核检——本单位船舶列表
     *
     * @param option 1出航核检 2返航核检
     * @param req
     * @return
     */
    @AutoLog(value = "核检——本单位船舶列表")
    @ApiOperation(value = "核检——本单位船舶列表", notes = "核检——本单位船舶列表")
    @GetMapping(value = "/shipList")
    public Result<?> queryShip(@RequestParam(name = "option", defaultValue = "1") int option, HttpServletRequest req) {
        Object principal = SecurityUtils.getSubject().getPrincipal();
        if (principal == null) {
            return Result.error("请登录");
        } else {
            LoginUser sysUser = (LoginUser) principal;
            List<ShipOutgoing> list = shipSailingRecordService.shipList(option, sysUser.getId());
            return Result.OK(list);
        }
    }

    /**
     * 返航核检人员列表
     *
     * @param shipId
     * @param req
     * @return
     */
    @AutoLog(value = "返航核检人员列表")
    @ApiOperation(value = "返航核检人员列表", notes = "返航核检人员列表")
    @GetMapping(value = "/shipUsers")
    public Result<?> shipUsers(@RequestParam(name = "shipId") String shipId, HttpServletRequest req) {
        SailingVo sailingVo = shipSailingRecordService.shipUsers(shipId);
        return Result.OK(sailingVo);
    }

    /**
     * 分页列表查询
     *
     * @param shipSailingRecord
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "ship_sailing_record-分页列表查询")
    @ApiOperation(value = "ship_sailing_record-分页列表查询", notes = "ship_sailing_record-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(ShipSailingRecord shipSailingRecord,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<ShipSailingRecord> queryWrapper = QueryGenerator.initQueryWrapper(shipSailingRecord, req.getParameterMap());
        Page<ShipSailingRecord> page = new Page<ShipSailingRecord>(pageNo, pageSize);
        IPage<ShipSailingRecord> pageList = shipSailingRecordService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param shipSailingRecord
     * @return
     */
    @AutoLog(value = "ship_sailing_record-添加")
    @ApiOperation(value = "ship_sailing_record-添加", notes = "ship_sailing_record-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@Validated @RequestBody ShipSailingRecord shipSailingRecord) {
        shipSailingRecordService.save(shipSailingRecord);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param shipSailingRecord
     * @return
     */
    @AutoLog(value = "ship_sailing_record-编辑")
    @ApiOperation(value = "ship_sailing_record-编辑", notes = "ship_sailing_record-编辑")
    @RequiresPermissions("shipSailingRecord:edit")
    @PutMapping(value = "/edit")
    public Result<?> edit(@Validated @RequestBody ShipSailingRecord shipSailingRecord) {
        shipSailingRecordService.updateById(shipSailingRecord);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "ship_sailing_record-通过id删除")
    @ApiOperation(value = "ship_sailing_record-通过id删除", notes = "ship_sailing_record-通过id删除")
    @RequiresPermissions("shipSailingRecord:delete")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        shipSailingRecordService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "ship_sailing_record-批量删除")
    @ApiOperation(value = "ship_sailing_record-批量删除", notes = "ship_sailing_record-批量删除")
    @RequiresPermissions("shipSailingRecord:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.shipSailingRecordService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "ship_sailing_record-通过id查询")
    @ApiOperation(value = "ship_sailing_record-通过id查询", notes = "ship_sailing_record-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        ShipSailingRecord shipSailingRecord = shipSailingRecordService.getById(id);
        if (shipSailingRecord == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(shipSailingRecord);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param shipSailingRecord
     */
    @RequiresPermissions("shipSailingRecord:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ShipSailingRecord shipSailingRecord) {
        return super.exportXls(request, shipSailingRecord, ShipSailingRecord.class, "ship_sailing_record");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("shipSailingRecord:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ShipSailingRecord.class);
    }

}
