package com.jinghe.breeze.modules.sailingcheck.entity.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="船只出航返回核检信息对象", description="船只出航返回核检信息对象")
public class SailingVo implements Serializable {
    private static final long serialVersionUID = 1L;

    private String shipId;

    private String shipName;

    private String mmsi;

    private String departmentId;

    private String departmentName;

    private String inspector;

    private String inspectorName;

    private List<SailingUser> users;
}
