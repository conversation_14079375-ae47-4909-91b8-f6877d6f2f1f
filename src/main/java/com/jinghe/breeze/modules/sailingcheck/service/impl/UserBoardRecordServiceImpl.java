package com.jinghe.breeze.modules.sailingcheck.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.modules.person.entity.PersonInfo;
import com.jinghe.breeze.modules.person.mapper.PersonInfoMapper;
import com.jinghe.breeze.modules.sailingcheck.entity.UserBoardRecord;
import com.jinghe.breeze.modules.sailingcheck.mapper.UserBoardRecordMapper;
import com.jinghe.breeze.modules.sailingcheck.service.IUserBoardRecordService;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: user_board_record
 * @Author: jeecg-boot
 * @Date: 2024-06-18
 * @Version: V1.0
 */
@Service
public class UserBoardRecordServiceImpl extends ServiceImpl<UserBoardRecordMapper, UserBoardRecord> implements IUserBoardRecordService {
    @Autowired
    private PersonInfoMapper personInfoMapper;

    @Override
    public Result<?> statistics() {
        LocalDate today = LocalDate.now();
        LambdaQueryWrapper<UserBoardRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(UserBoardRecord::getCreateTime, LocalDateTime.of(today, LocalTime.MIN))
                .lt(UserBoardRecord::getCreateTime, LocalDateTime.of(today, LocalTime.MAX))
                .eq(UserBoardRecord::getType, Common.commonality.ONE);
//        queryWrapper.eq(UserSailingRecord::getStatus, Common.commonality.ONE);
        Integer todaySum = baseMapper.selectCount(queryWrapper);
        LocalDate firstDayOfMonth = today.with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastDayOfMonth = today.with(TemporalAdjusters.lastDayOfMonth());
        // 构造起始和结束时间
        LocalDateTime startTime = LocalDateTime.of(firstDayOfMonth, LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.of(lastDayOfMonth, LocalTime.MAX);
        LambdaQueryWrapper<UserBoardRecord> month = new LambdaQueryWrapper<>();
//        month.eq(UserSailingRecord::getStatus, Common.commonality.ONE);
        month.ge(UserBoardRecord::getCreateTime, startTime)
                .lt(UserBoardRecord::getCreateTime, endTime)
                .eq(UserBoardRecord::getType, Common.commonality.ONE);
        Integer monthSum = baseMapper.selectCount(month);

        // 获取本年的第一天和最后一天
        LocalDate yearsEndDate = today.with(TemporalAdjusters.lastDayOfYear());
        LocalDate yearsStartDate = today.with(TemporalAdjusters.firstDayOfYear());
        startTime = LocalDateTime.of(yearsStartDate, LocalTime.MIN);
        endTime = LocalDateTime.of(yearsEndDate, LocalTime.MAX);
        LambdaQueryWrapper<UserBoardRecord> years = new LambdaQueryWrapper<>();
//        years.eq(UserSailingRecord::getStatus, Common.commonality.ONE);
        years.ge(UserBoardRecord::getCreateTime, startTime)
                .lt(UserBoardRecord::getCreateTime, endTime)
                .eq(UserBoardRecord::getType, Common.commonality.ONE);
        Integer yearSum = baseMapper.selectCount(years);

        Map<String, Object> map = new HashMap<>();
        map.put("todaySum", todaySum);
        map.put("monthSum", monthSum);
        map.put("yearSum", yearSum);
        return Result.OK(map);
    }

    @Override
    public Result<?> queryPageList(Integer pageNo, Integer pageSize, String startDate, String endDate) {
        LambdaQueryWrapper<UserBoardRecord> queryWrapper = new LambdaQueryWrapper<>();
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//        LocalDateTime localDateTime = LocalDateTime.of(LocalDateTime.now().toLocalDate(), LocalTime.MAX);
        if (startDate != null && !startDate.isEmpty() && endDate != null && !endDate.isEmpty()) {
            // 如果传递了 startDate 和 endDate，则设置时间范围查询条件
            queryWrapper.ge(UserBoardRecord::getCreateTime, startDate + " 00:00:00")
                    .le(UserBoardRecord::getCreateTime, endDate + " 23:59:59");
        } else {
            // 如果没有传递时间，则查询最新的数据，假设根据 CreateTime 降序排列并限制结果数
            queryWrapper.orderByDesc(UserBoardRecord::getCreateTime);
        }

        Page<UserBoardRecord> page = new Page<>(pageNo, pageSize);
        IPage<UserBoardRecord> pageList = super.page(page, queryWrapper);

        if (!pageList.getRecords().isEmpty()) {
            Set<String> userIds = pageList.getRecords().stream().map(UserBoardRecord::getUserId).collect(Collectors.toSet());
            LambdaQueryWrapper<PersonInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.in(PersonInfo::getUserId, userIds);
            List<PersonInfo> personInfos = personInfoMapper.selectList(lambdaQueryWrapper);

            for (UserBoardRecord record : pageList.getRecords()) {
                Optional<PersonInfo> personInfo = personInfos.stream().filter(info -> info.getUserId().equals(record.getUserId())).findFirst();
                if (personInfo.isPresent()) {
                    System.out.println(personInfo.get().getPhone());
                    record.setPhone(personInfo.get().getPhone());
                }

//                personInfo.ifPresent(info -> record.setPhone(info.getPhone()));
            }
        }
        return Result.OK(pageList);
    }

}
