package com.jinghe.breeze.modules.quality.controller;

import java.io.UnsupportedEncodingException;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.jinghe.breeze.modules.quality.entity.QualityEvaluationInformationDetail;
import com.jinghe.breeze.modules.quality.entity.QualityEvaluationInformation;
import com.jinghe.breeze.modules.quality.vo.QualityEvaluationInformationPage;
import com.jinghe.breeze.modules.quality.service.IQualityEvaluationInformationService;
import com.jinghe.breeze.modules.quality.service.IQualityEvaluationInformationDetailService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 验评资料
 * @Author: jeecg-boot
 * @Date:   2025-06-27
 * @Version: V1.0
 */
@Api(tags="验评资料")
@RestController
@RequestMapping("/quality/qualityEvaluationInformation")
@Slf4j
public class QualityEvaluationInformationController {
	@Autowired
	private IQualityEvaluationInformationService qualityEvaluationInformationService;
	@Autowired
	private IQualityEvaluationInformationDetailService qualityEvaluationInformationDetailService;
	
	/**
	 * 分页列表查询
	 *
	 * @param qualityEvaluationInformation
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "验评资料-分页列表查询")
	@ApiOperation(value="验评资料-分页列表查询", notes="验评资料-分页列表查询")

	@GetMapping(value = "/list")
	public Result<?> queryPageList(QualityEvaluationInformation qualityEvaluationInformation,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<QualityEvaluationInformation> queryWrapper = QueryGenerator.initQueryWrapper(qualityEvaluationInformation, req.getParameterMap());
		Page<QualityEvaluationInformation> page = new Page<QualityEvaluationInformation>(pageNo, pageSize);
		IPage<QualityEvaluationInformation> pageList = qualityEvaluationInformationService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param qualityEvaluationInformationPage
	 * @return
	 */
	@AutoLog(value = "验评资料-添加")
    @RequiresPermissions("qualityEvaluationInformation:add")
	@ApiOperation(value="验评资料-添加", notes="验评资料-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody QualityEvaluationInformationPage qualityEvaluationInformationPage) {
		QualityEvaluationInformation qualityEvaluationInformation = new QualityEvaluationInformation();
		BeanUtils.copyProperties(qualityEvaluationInformationPage, qualityEvaluationInformation);
		qualityEvaluationInformationService.saveMain(qualityEvaluationInformation, qualityEvaluationInformationPage.getQualityEvaluationInformationDetailList());
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param qualityEvaluationInformationPage
	 * @return
	 */
	@AutoLog(value = "验评资料-编辑")
    @RequiresPermissions("qualityEvaluationInformation:edit")
	@ApiOperation(value="验评资料-编辑", notes="验评资料-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody QualityEvaluationInformationPage qualityEvaluationInformationPage) {
		QualityEvaluationInformation qualityEvaluationInformation = new QualityEvaluationInformation();
		BeanUtils.copyProperties(qualityEvaluationInformationPage, qualityEvaluationInformation);
		QualityEvaluationInformation qualityEvaluationInformationEntity = qualityEvaluationInformationService.getById(qualityEvaluationInformation.getId());
		if(qualityEvaluationInformationEntity==null) {
			return Result.error("未找到对应数据");
		}
		qualityEvaluationInformationService.updateMain(qualityEvaluationInformation, qualityEvaluationInformationPage.getQualityEvaluationInformationDetailList());
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "验评资料-通过id删除")
    @RequiresPermissions("qualityEvaluationInformation:delete")
	@ApiOperation(value="验评资料-通过id删除", notes="验评资料-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		qualityEvaluationInformationService.delMain(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "验评资料-批量删除")
    @RequiresPermissions("qualityEvaluationInformation:deleteBatch")
	@ApiOperation(value="验评资料-批量删除", notes="验评资料-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.qualityEvaluationInformationService.delBatchMain(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "验评资料-通过id查询")
	@ApiOperation(value="验评资料-通过id查询", notes="验评资料-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		QualityEvaluationInformation qualityEvaluationInformation = qualityEvaluationInformationService.getById(id);
		if(qualityEvaluationInformation==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(qualityEvaluationInformation);

	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "验评资料详情通过主表ID查询")
	@ApiOperation(value="验评资料详情主表ID查询", notes="验评资料详情-通主表ID查询")
	@GetMapping(value = "/queryQualityEvaluationInformationDetailByMainId")
	public Result<?> queryQualityEvaluationInformationDetailListByMainId(@RequestParam(name="id",required=true) String id) {
		List<QualityEvaluationInformationDetail> qualityEvaluationInformationDetailList = qualityEvaluationInformationDetailService.selectByMainId(id);
		return Result.OK(qualityEvaluationInformationDetailList);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param qualityEvaluationInformation
    */
    @RequiresPermissions("qualityEvaluationInformation:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, QualityEvaluationInformation qualityEvaluationInformation) {
      // Step.1 组装查询条件查询数据
      QueryWrapper<QualityEvaluationInformation> queryWrapper = QueryGenerator.initQueryWrapper(qualityEvaluationInformation, request.getParameterMap());
      LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

      //Step.2 获取导出数据
      List<QualityEvaluationInformation> queryList = qualityEvaluationInformationService.list(queryWrapper);
      // 过滤选中数据
      String selections = request.getParameter("selections");
      List<QualityEvaluationInformation> qualityEvaluationInformationList = new ArrayList<QualityEvaluationInformation>();
      if(oConvertUtils.isEmpty(selections)) {
          qualityEvaluationInformationList = queryList;
      }else {
          List<String> selectionList = Arrays.asList(selections.split(","));
          qualityEvaluationInformationList = queryList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
      }

      // Step.3 组装pageList
      List<QualityEvaluationInformationPage> pageList = new ArrayList<QualityEvaluationInformationPage>();
      for (QualityEvaluationInformation main : qualityEvaluationInformationList) {
          QualityEvaluationInformationPage vo = new QualityEvaluationInformationPage();
          BeanUtils.copyProperties(main, vo);
          List<QualityEvaluationInformationDetail> qualityEvaluationInformationDetailList = qualityEvaluationInformationDetailService.selectByMainId(main.getId());
          vo.setQualityEvaluationInformationDetailList(qualityEvaluationInformationDetailList);
          pageList.add(vo);
      }

      // Step.4 AutoPoi 导出Excel
      ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "验评资料列表");
      mv.addObject(NormalExcelConstants.CLASS, QualityEvaluationInformationPage.class);
      mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("验评资料数据", "导出人:"+sysUser.getRealname(), "验评资料"));
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
      return mv;
    }

    /**
    * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("qualityEvaluationInformation:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
      Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
      for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
          MultipartFile file = entity.getValue();// 获取上传文件对象
          ImportParams params = new ImportParams();
          params.setTitleRows(2);
          params.setHeadRows(1);
          params.setNeedSave(true);
          try {
              List<QualityEvaluationInformationPage> list = ExcelImportUtil.importExcel(file.getInputStream(), QualityEvaluationInformationPage.class, params);
              for (QualityEvaluationInformationPage page : list) {
                  QualityEvaluationInformation po = new QualityEvaluationInformation();
                  BeanUtils.copyProperties(page, po);
                  qualityEvaluationInformationService.saveMain(po, page.getQualityEvaluationInformationDetailList());
              }
              return Result.OK("文件导入成功！数据行数:" + list.size());
          } catch (Exception e) {
              log.error(e.getMessage(),e);
              return Result.error("文件导入失败:"+e.getMessage());
          } finally {
              try {
                  file.getInputStream().close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
          }
      }
      return Result.OK("文件导入失败！");
    }

}
