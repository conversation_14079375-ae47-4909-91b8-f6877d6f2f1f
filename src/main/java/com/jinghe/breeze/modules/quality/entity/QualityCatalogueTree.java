package com.jinghe.breeze.modules.quality.entity;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 * @Description: quality_catalogue_tree
 * @Author: jeecg-boot
 * @Date:   2025-06-20
 * @Version: V1.0
 */
@Data
@TableName("quality_catalogue_tree")
@ApiModel(value="quality_catalogue_tree对象", description="quality_catalogue_tree")
public class QualityCatalogueTree implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private java.lang.String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    private java.util.Date updateTime;
	/**delFlag*/
	@Excel(name = "delFlag", width = 15)
    @ApiModelProperty(value = "delFlag")
    private java.lang.Integer delFlag;
	/**组织机构编码*/
    @ApiModelProperty(value = "组织机构编码")
    private java.lang.String sysOrgCode;
	/**节点编码*/
	@Excel(name = "节点编码", width = 15)
    @ApiModelProperty(value = "节点编码")
    private java.lang.String code;
	/**节点名*/
	@Excel(name = "节点名", width = 15)
    @ApiModelProperty(value = "节点名")
    private java.lang.String name;
	/**父级节点id*/
	@Excel(name = "父级节点id", width = 15)
    @ApiModelProperty(value = "父级节点id")
    private java.lang.String pid;
	/**层级*/
    @Dict(dicCode = "qbs_layer")
	@Excel(name = "层级", width = 15)
    @ApiModelProperty(value = "层级")
    private java.lang.String qbsLayer;
	/**排序值*/
	@Excel(name = "排序值", width = 15)
    @ApiModelProperty(value = "排序值")
    private java.lang.Integer sequence;
	/**id路径 /rootid/id2/id3*/
	@Excel(name = "id路径 /rootid/id2/id3", width = 15)
    @ApiModelProperty(value = "id路径 /rootid/id2/id3")
    private java.lang.String path;
	/**是否有子节点*/
	@Excel(name = "是否有子节点", width = 15, dicCode = "yn")
	@Dict(dicCode = "yn")
    @ApiModelProperty(value = "是否有子节点")
    private java.lang.String hasChild;

    @TableField(exist = false)
    private List<QualityCatalogueTree> children;
}
