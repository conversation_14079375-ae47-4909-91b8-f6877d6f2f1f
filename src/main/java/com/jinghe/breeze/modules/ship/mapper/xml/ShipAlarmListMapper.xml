<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghe.breeze.modules.ship.mapper.ShipAlarmListMapper">

    <select id="selectByCreateDate" resultType="com.jinghe.breeze.modules.ship.entity.ShipAlarmDetail">

SELECT * FROM ship_alarm_detail
where mmsi =#{mmsi}
ORDER BY ABS(TIMESTAMPDIFF(MINUTE, create_time, #{createTime})) ASC
LIMIT 1;
    </select>
</mapper>