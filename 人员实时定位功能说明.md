# 人员实时定位功能模块

## 功能概述

本模块实现了人员实时定位功能，包括设备位置数据接收和人员位置信息查询两个核心功能。

## 实现的文件

### 1. DTO类
- `LocationDataDTO.java` - 位置数据传输对象
- `EnrichedLocationDataDTO.java` - 增强的位置数据传输对象

### 2. 服务层
- `IDeviceHelmetService.java` - 服务接口（已更新）
- `DeviceHelmetServiceImpl.java` - 服务实现类（已更新）

### 3. 控制器层
- `DeviceHelmetController.java` - 设备管理控制器（已更新）
- `PersonnelLocationController.java` - 人员位置控制器（新增）

## API接口说明

### 1. 数据接收接口

**接口地址**: `POST /person/deviceHelmet/location/receive`

**功能**: 接收设备定时上报的位置数据并存储到Redis

**请求体**: JSON数组格式
```json
[
  {
    "puname": "1031001",
    "gpstime": 1576035257,
    "alarmtype": "无报警",
    "longitude": 107.008272,
    "latitude": 39.427993,
    "speed": 3.0,
    "power": "70",
    "datatype": "Lastgpsdata",
    "reserved": "0"
  }
]
```

**响应**: 
```json
{
  "success": true,
  "message": "位置数据接收成功",
  "code": 200
}
```

### 2. 数据查询接口

**接口地址**: `GET /api/personnel/location/list`

**功能**: 查询所有人员的最新位置信息

**响应**: 
```json
{
  "success": true,
  "result": [
    {
      "puname": "1031001",
      "gpstime": 1576035257,
      "alarmtype": "无报警",
      "longitude": 107.008272,
      "latitude": 39.427993,
      "speed": 3.0,
      "power": "70",
      "datatype": "Lastgpsdata",
      "reserved": "0",
      "isAssessed": null,
      "isRegistered": null,
      "latestViolation": null
    }
  ],
  "timestamp": 1641024000000
}
```

## 技术实现细节

### Redis存储策略
- **Key格式**: `deviceHelmet:location:{puname}`
- **过期时间**: 10分钟（600秒）
- **存储内容**: LocationDataDTO对象序列化后的数据

### 业务逻辑增强
在`DeviceHelmetServiceImpl`中预留了三个方法供您实现具体的业务逻辑：

1. `enrichBusinessLogic_IsAssessed()` - 判断是否通过考核
2. `enrichBusinessLogic_IsRegistered()` - 判断是否登记
3. `enrichBusinessLogic_LatestViolation()` - 获取最新违规记录

### 错误处理
- 接口包含完整的异常处理机制
- 详细的日志记录便于问题排查
- 友好的错误信息返回

## 使用说明

1. **数据接收**: 设备通过POST请求向`/person/deviceHelmet/location/receive`发送位置数据
2. **数据查询**: 前端通过GET请求从`/api/personnel/location/list`获取人员位置信息
3. **业务增强**: 根据实际需求在预留的方法中实现具体的业务逻辑

## 注意事项

1. 接口对外开放，无需身份验证
2. Redis中的数据会在10分钟后自动过期
3. 业务逻辑增强方法目前返回null，需要根据实际业务需求实现
4. 建议在生产环境中添加适当的限流和安全措施
