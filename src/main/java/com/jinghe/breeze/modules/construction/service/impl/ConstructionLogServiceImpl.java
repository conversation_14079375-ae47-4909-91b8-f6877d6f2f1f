package com.jinghe.breeze.modules.construction.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.common.constants.enums.ProjectPbsEnum;
import com.jinghe.breeze.common.constants.enums.VideoFolderEnum;
import com.jinghe.breeze.modules.construction.entity.ConstructionLog;
import com.jinghe.breeze.modules.construction.entity.vo.ConstructionLogResult;
import com.jinghe.breeze.modules.construction.entity.vo.ConstructionLogVo;
import com.jinghe.breeze.modules.data.entity.DataCableInfo;
import com.jinghe.breeze.modules.data.service.IDataCableInfoService;
import com.jinghe.breeze.modules.project.entity.*;
import com.jinghe.breeze.modules.project.entity.vo.ProjectProcessVo;
import com.jinghe.breeze.modules.construction.mapper.ConstructionLogMapper;
import com.jinghe.breeze.modules.construction.service.IConstructionLogService;
import com.jinghe.breeze.modules.project.mapper.*;
import com.jinghe.breeze.modules.project.service.IProjectPlanReportService;
import com.jinghe.breeze.modules.video.service.IVideoInfoService;
import jodd.util.StringUtil;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.system.entity.SysDict;
import org.jeecg.modules.system.entity.SysDictItem;
import org.jeecg.modules.system.service.ISysDictItemService;
import org.jeecg.modules.system.service.ISysDictService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: construction_log
 * @Author: jeecg-boot
 * @Date: 2024-05-09
 * @Version: V1.0
 */
@Service
public class ConstructionLogServiceImpl extends ServiceImpl<ConstructionLogMapper, ConstructionLog> implements IConstructionLogService {

    private static final String DICTIONARY_CODE = "B12";
    private static final String PROCESS_TYPE = "路由";

    @Autowired
    private IVideoInfoService videoInfoService;
    @Autowired
    private ISysDictService sysDictService;
    @Autowired
    private ISysDictItemService sysDictItemService;
    @Autowired
    private IDataCableInfoService dataCableInfoService;

    @Autowired
    private ProjectProcessMapper projectProcessMapper;
    @Autowired
    private ProjectPbsMapper projectPbsMapper;
    @Autowired
    private ProjectUnitMapper projectUnitMapper;
    @Autowired
    private ProjectPlanMapper planMapper;
    @Autowired
    private IProjectPlanReportService iProjectPlanReportService;
    @Autowired
    private ConstructionPlanProcessMapper planProcessMapper;


    @Override
    public Result<?> queryList(String pbsId, String projectId, String processType) {
        LambdaQueryWrapper<ConstructionLog> queryWrapper = new LambdaQueryWrapper<>();
        if (oConvertUtils.isNotEmpty(projectId)) {
            queryWrapper.eq(ConstructionLog::getProjectId, projectId);
        }
        queryWrapper.eq(ConstructionLog::getPbsId, pbsId);
        List<ConstructionLog> list = super.list(queryWrapper);

        LambdaQueryWrapper<ProjectProcess> processQueryWrapper = new LambdaQueryWrapper<>();
        if (oConvertUtils.isNotEmpty(projectId)) {
            processQueryWrapper.eq(ProjectProcess::getProjectId, projectId);
        }
        ProjectPbs projectPbs = projectPbsMapper.selectById(pbsId);
        processQueryWrapper.eq(ProjectProcess::getType, projectPbs.getApplicableProcess());
        processQueryWrapper.orderByAsc(ProjectProcess::getProcessCode);
        List<ProjectProcess> projectProcesses = projectProcessMapper.selectList(processQueryWrapper);

        if (projectProcesses.size() == 0) {
            return Result.OK();
        }

        List<ConstructionLog> logList = new LinkedList<>();
        for (ProjectProcess projectProcess : projectProcesses) {
            ConstructionLog constructionLog = new ConstructionLog();
            if (list.size() == 0) {
                constructionLog.setProcessId(projectProcess.getId());
            } else {
                ConstructionLog first = list.stream().filter(x -> x.getProcessId().equals(projectProcess.getId())).findFirst().orElse(null);
                if (first != null) {
                    String unitId = first.getUnitId();
                    BeanUtils.copyProperties(first, constructionLog);

                    if (oConvertUtils.isNotEmpty(unitId)) {
                        ProjectUnit projectUnit = projectUnitMapper.selectById(unitId);
                        constructionLog.setUnitName(StringUtils.isEmpty(projectUnit) ? "" : projectUnit.getName());
                    }
                } else {
                    constructionLog.setProcessId(projectProcess.getId());
                }
            }
            constructionLog.setPbsId(pbsId);
            constructionLog.setProcessName(projectProcess.getName());
            logList.add(constructionLog);
        }
        return Result.OK(logList);
    }

    @Override
    public Result<?> getAllList(String projectId) {
        //查询所有的pbsid 根据pbsid查询日志工序
        LambdaQueryWrapper<ProjectPbs> pbsWrapper = new LambdaQueryWrapper<>();
        LambdaQueryWrapper<ProjectProcess> processWrapper = new LambdaQueryWrapper<>();
        LambdaQueryWrapper<ConstructionLog> queryWrapper = new LambdaQueryWrapper<>();
        if (oConvertUtils.isNotEmpty(projectId)) {
            pbsWrapper.eq(ProjectPbs::getProjectId, projectId);
            processWrapper.eq(ProjectProcess::getProjectId, projectId);
            queryWrapper.eq(ConstructionLog::getProjectId, projectId);
        }
        //本项目只有一层所以查询第一层级数据
        pbsWrapper.eq(ProjectPbs::getLayer, Common.commonality.ZERO);
        List<ProjectPbs> projectPbsList = projectPbsMapper.selectList(pbsWrapper);

        if (projectPbsList.size() == 0) {
            return Result.OK();
        }

        List<ProjectProcessVo> processVos = new ArrayList<>();
        List<String> pbsIds = projectPbsList.stream().map(x -> x.getId()).collect(Collectors.toList());

        queryWrapper.in(ConstructionLog::getPbsId, pbsIds);
        List<ConstructionLog> list = super.list(queryWrapper);
        List<String> processListId = list.stream().map(x -> x.getProcessId()).collect(Collectors.toList());

        processWrapper.in(ProjectProcess::getId, processListId);
        List<ProjectProcess> projectProcesses = projectProcessMapper.selectList(processWrapper);

        for (ProjectPbs projectPbs : projectPbsList) {
            ProjectProcessVo processVo = new ProjectProcessVo();
            processVo.setPbsId(projectPbs.getId());
            processVo.setPoint(projectPbs.getPoint());
            processVo.setPbsName(projectPbs.getName());
            //写法修改
            if (Objects.equals(projectPbs.getType(), ProjectPbsEnum.FJ.getCode())) {
                processVo.setProcessType(Common.AIR_BLOWER_PROCESS.FJ_MAKE);
            } else if (ObjectUtils.nullSafeEquals(projectPbs.getType(), ProjectPbsEnum.HL.getCode())) {
                processVo.setProcessType(Common.AIR_BLOWER_PROCESS.HL_MAKE);
            }

            List<ConstructionLog> collect = list.stream()
                    .filter(x -> x.getPbsId().equals(projectPbs.getId()))
                    .sorted(Comparator.comparing(ConstructionLog::getProcessCode))
                    .collect(Collectors.toList());

            if (collect.size() != 0) {
                ConstructionLog constructionLog = collect.stream().reduce((first, second) -> second).orElse(null);
                Optional<ProjectProcess> first = projectProcesses.stream().filter(x -> ObjectUtil.equal(x.getId(), constructionLog.getProcessId())).findFirst();
                if (first.isPresent()) {
                    processVo.setIcon(first.get().getIcon());
                }
            }
            processVos.add(processVo);
        }
        return Result.OK(processVos);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveList(ConstructionLogVo constructionLogVo) {
        if (constructionLogVo.getConstructionLogs().size() == 0) {
            return;
        }
        List<String> newFileList = constructionLogVo.getConstructionLogs().stream().map(ConstructionLog::getFile).collect(Collectors.toList());
        String pbsId = constructionLogVo.getPbsId();
        List<String> oldFileList = baseMapper.selectList(new LambdaQueryWrapper<ConstructionLog>().eq(ConstructionLog::getPbsId, pbsId))
                .stream().map(ConstructionLog::getFile).collect(Collectors.toList());
        videoInfoService.updateVideoInfo(oldFileList, newFileList, VideoFolderEnum.CONSTRUCTION.getId());
        Set<String> collect = constructionLogVo.getConstructionLogs().stream().map(x -> x.getProcessId()).collect(Collectors.toSet());
        LambdaQueryWrapper<ProjectProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjectProcess::getId, collect);
        List<ProjectProcess> projectProcesses = projectProcessMapper.selectList(queryWrapper);
        for (ConstructionLog log : constructionLogVo.getConstructionLogs()) {
            if (oConvertUtils.isNotEmpty(log.getId())) {
                log.setConstructionState(Common.commonality.TWO);
                super.updateById(log);
            } else {
//                for (ConstructionLog constructionLog : constructionLogVo.getConstructionLogs()) {
//                    constructionLog.setPbsId(constructionLogVo.getPbsId());
//                }
                Optional<ProjectProcess> first = projectProcesses.stream().filter(x -> x.getId().equals(log.getProcessId())).findFirst();
                if (first.isPresent()) {
                    log.setProcessCode(first.get().getProcessCode());
                }
                log.setPbsId(constructionLogVo.getPbsId());
                log.setConstructionState(Common.commonality.TWO);
                super.save(log);
            }
        }
        List<String> collect1 = constructionLogVo.getConstructionLogs().stream().map(x -> x.getProcessId()).collect(Collectors.toList());
        //自动计算进度填报日期
        this.planProcess(pbsId, collect1);
    }

    @Async
    @Override
    public void planProcess(String pbsId, List<String> processIds) {
        for (String processId : processIds) {

            LambdaQueryWrapper<ConstructionPlanProcess> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ConstructionPlanProcess::getPbsId, pbsId)
                    .eq(ConstructionPlanProcess::getProcessId, processId);
            ConstructionPlanProcess constructionPlanProcess = planProcessMapper.selectOne(queryWrapper);
            if (ObjectUtils.isEmpty(constructionPlanProcess)) {
                continue;
            }
            queryWrapper.clear();
            queryWrapper.eq(ConstructionPlanProcess::getPlanCode, constructionPlanProcess.getPlanCode());
            //计划关联的所有工序
            List<ConstructionPlanProcess> list = planProcessMapper.selectList(queryWrapper);

            List<ConstructionLog> logList = new ArrayList<>();
            for (ConstructionPlanProcess planProcess : list) {
                LambdaQueryWrapper<ConstructionLog> queryWrapper1 = new LambdaQueryWrapper<>();
                queryWrapper1.eq(ConstructionLog::getPbsId, planProcess.getPbsId())
                        .eq(ConstructionLog::getProcessId, planProcess.getProcessId())
                        .eq(ConstructionLog::getDelFlag, Common.commonality.ZERO).last("limit 1");
                ConstructionLog constructionLog1 = baseMapper.selectOne(queryWrapper1);
                if (!ObjectUtils.isEmpty(constructionLog1)) {
                    logList.add(constructionLog1);
                }
            }

            ProjectPlanReport projectPlanReport = iProjectPlanReportService.getOne(
                    new LambdaQueryWrapper<ProjectPlanReport>()
                            .eq(ProjectPlanReport::getBaseCode, constructionPlanProcess.getPlanCode()));

            ProjectPlanReport planReport = new ProjectPlanReport();
            planReport.setBaseCode(constructionPlanProcess.getPlanCode());
            if (!logList.isEmpty()) {
                Date startTime = logList.stream().min(Comparator.comparing(ConstructionLog::getStartTime)).get().getStartTime();
                if (list.size() == logList.size()) {//全部已完成
                    Date endTime = logList.stream().max(Comparator.comparing(ConstructionLog::getEndTime)).get().getEndTime();
                    long differenceInMillis = Math.abs(endTime.getTime() - startTime.getTime());  // 相差的毫秒数
                    long differenceInDays = differenceInMillis / (24 * 60 * 60 * 1000);  // 相差的天数
                    planReport.setStatus(Common.commonality.TWO)
                            .setActualDay((int) differenceInDays + 1)
                            .setActualStartDate(startTime)
                            .setActualEndDate(endTime);
                } else {//部分完成
                    planReport.setStatus(Common.commonality.ONE)
                            .setActualStartDate(startTime)
                            .setActualDay(null)
                            .setActualEndDate(null);
                }
            } else {
                planReport.setStatus(Common.commonality.ZERO)
                        .setActualStartDate(null)
                        .setActualEndDate(null)
                        .setActualDay(null);
            }
            if (ObjectUtils.isEmpty(projectPlanReport) && !logList.isEmpty()) {
                iProjectPlanReportService.save(planReport);
            } else {
                planReport.setId(projectPlanReport.getId());
                iProjectPlanReportService.updateById(planReport);
            }

            LambdaQueryWrapper<ProjectPlan> planQuery = new LambdaQueryWrapper<>();
            planQuery.eq(ProjectPlan::getCode, planReport.getBaseCode());
            planQuery.orderByDesc(ProjectPlan::getVersion);
            planQuery.last("LIMIT 1");
            ProjectPlan projectPlan = planMapper.selectOne(planQuery);
            iProjectPlanReportService.updateByBase(projectPlan);
        }
    }

    @Override
    public List<ConstructionLogResult> commitBatch(List<ConstructionLog> data) {
        List<String> newFileList = new ArrayList<>();
        List<String> oldFileList = new ArrayList<>();
        List<String> pbsIds = new ArrayList<>();
        List<ConstructionLogResult> itemLogs = new ArrayList<>();
        for (ConstructionLog log : data) {
            if (oConvertUtils.isNotEmpty(log.getFile())) {
                newFileList.add(log.getFile());
            }
            ConstructionLogResult record = new ConstructionLogResult();
            BeanUtils.copyProperties(log, record);
            try {
                if (oConvertUtils.isNotEmpty(log.getId())) {
                    log.setConstructionState(Common.commonality.TWO);
                    super.updateById(log);
                } else {
                    List<ConstructionLog> constructionLogs = baseMapper.selectList(new LambdaQueryWrapper<ConstructionLog>().eq(ConstructionLog::getPbsId, log.getPbsId()).eq(ConstructionLog::getProcessId, log.getProcessId()));
                    if (constructionLogs.isEmpty()) {
                        log.setConstructionState(Common.commonality.TWO);
                        super.save(log);
                        record.setId(log.getId());
                    } else {
                        log.setId(constructionLogs.get(0).getId());
                        log.setConstructionState(Common.commonality.TWO);
                        super.updateById(log);
                        record.setId(constructionLogs.get(0).getId());
                    }
                }
                pbsIds.add(log.getPbsId());
                record.setCode(0);
            } catch (Exception e) {
                record.setCode(500);
            }
            itemLogs.add(record);
        }
        if (!pbsIds.isEmpty()) {
            List<String> logFileList = baseMapper.selectList(new LambdaQueryWrapper<ConstructionLog>().in(ConstructionLog::getPbsId, pbsIds))
                    .stream().map(ConstructionLog::getFile).collect(Collectors.toList());
            oldFileList.addAll(logFileList);
            videoInfoService.updateVideoInfo(oldFileList, newFileList, VideoFolderEnum.CONSTRUCTION.getId());
        }

        return itemLogs;
    }

    private Map<String, String> getDictMap(String dictCode, String dicriptName) {
        if (StringUtil.isEmpty(dictCode)) {
            throw new JeecgBootException("字典编码不能为空");
        }
        SysDict sysDict = sysDictService.getOne(new LambdaQueryWrapper<SysDict>().eq(SysDict::getDictCode, dictCode), false);
        if (sysDict == null) {
            throw new JeecgBootException(String.format("请先配置%s数据字典", dicriptName));
        }
        List<SysDictItem> sysDictItems = sysDictItemService.selectItemsByMainId(sysDict.getId());
        sysDictItems.sort(Comparator.comparing(SysDictItem::getSortOrder));
        LinkedHashMap<String, String> retMap = new LinkedHashMap<>();
        sysDictItems.forEach(c -> retMap.put(c.getItemValue(), c.getItemText())); //key 是 B12A02 value 是9#路由
        return retMap;
    }

    @Override
    public Result<?> report(String projectId, String type) {
        Map<String, Object> finalMap = new LinkedHashMap<>();

        List<DataCableInfo> cableInfoList = dataCableInfoService.list(new LambdaQueryWrapper<DataCableInfo>());
        Map<String, String> cableInfoMap = cableInfoList.stream().collect(Collectors.toMap(DataCableInfo::getId, DataCableInfo::getCableType));
        Map<String, String> routeDictMap = getDictMap(DICTIONARY_CODE, PROCESS_TYPE);

        LambdaQueryWrapper<ProjectPbs> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectPbs::getType, type)
                .eq(ProjectPbs::getLayer, Common.commonality.ZERO)
                .eq(ProjectPbs::getFanSection, Common.commonality.ZERO)
                .isNotNull(ProjectPbs::getApplicableProcess);
        List<ProjectPbs> projectPbs = projectPbsMapper.selectList(queryWrapper);

        List<String> collect = projectPbs.stream().map(x -> x.getId()).collect(Collectors.toList());


        LambdaQueryWrapper<ConstructionLog> logWrapper = new LambdaQueryWrapper<>();
        logWrapper.in(ConstructionLog::getPbsId, collect).eq(ConstructionLog::getDelFlag,Common.delete_flag.OK);
        List<ConstructionLog> constructionLogs = baseMapper.selectList(logWrapper);
        Map<String, List<ConstructionLog>> logMap = constructionLogs.stream().collect(Collectors.groupingBy(ConstructionLog::getPbsId));


        Map<String, List<ProjectPbs>> routeMap = projectPbs.stream().collect(Collectors.groupingBy(score -> !StringUtils.isEmpty(score.getRoute()) ? score.getRoute() : ""));

        Set<String> processSet = projectPbs.stream().map(x -> x.getApplicableProcess()).collect(Collectors.toSet());
        LambdaQueryWrapper<ProjectProcess> processQueryWrapper = new LambdaQueryWrapper<>();
        processQueryWrapper.in(ProjectProcess::getType, processSet);
        List<ProjectProcess> projectProcesses = projectProcessMapper.selectList(processQueryWrapper);
        Map<String, ProjectProcess> processMap = projectProcesses.stream().collect(Collectors.toMap(ProjectProcess::getId, Function.identity()));

        TreeMap<String, Object> tempMap = new TreeMap<>();
        for (String route : routeMap.keySet()) {
            List<Object> list = new LinkedList<>();
            List<ProjectPbs> projectPbs1 = routeMap.get(route);
            for (ProjectPbs pbs : projectPbs1) {
                Map<String, Object> pbsMap = new HashMap<>();
                pbsMap.put("name", pbs.getName());
                pbsMap.put("id", pbs.getId());
                List<ConstructionLog> logList = logMap.getOrDefault(pbs.getId(), Collections.emptyList());
                double percentage = logList.stream().filter(x -> !StringUtils.isEmpty(x.getEndTime()))
                        .map(log -> processMap.getOrDefault(log.getProcessId(), new ProjectProcess()))
                        .filter(process -> !StringUtils.isEmpty(process.getWeight()))
                        .mapToDouble(process -> process.getWeight()).sum();
//                int percentage = 0;
//                if (!StringUtils.isEmpty(logList) && logList.size() > 0) {
//                    for (ConstructionLog constructionLog : logList) {
//                        if (!StringUtils.isEmpty(constructionLog.getEndTime())) {
//                            if (!StringUtils.isEmpty(processMap.get(constructionLog.getProcessId()))
//                                    && !StringUtils.isEmpty(processMap.get(constructionLog.getProcessId()).getWeight())) {
//                                percentage += processMap.get(constructionLog.getProcessId()).getWeight();
//                            }
//                        }
//                    }
//                }
                pbsMap.put("processType", pbs.getApplicableProcess());
                pbsMap.put("percentage", percentage);
                pbsMap.put("cableType", cableInfoMap.getOrDefault(pbs.getSpecificationModel(), null)); //海缆型号名
                list.add(pbsMap);
            }
            tempMap.put(route, list);
        }

        routeDictMap.forEach((key, value) -> {
            if (tempMap.containsKey(key)) {
                finalMap.put(value, tempMap.get(key));
            }
        });
//        for (String key : routeDictMap.keySet()) {
//            if (StringUtils.isEmpty(map.get(key))) {
//                continue;
//            }
//            objectMap.put(routeDictMap.get(key), map.get(key));
//        }
        return Result.OK(finalMap);
    }

    @Override

    public void clear(ConstructionLog constructionLog) {
        baseMapper.deleteById(constructionLog.getId());

        //重新计算填报进度
        this.planProcess(constructionLog.getPbsId(), Arrays.asList(constructionLog.getProcessId()));
    }
}

