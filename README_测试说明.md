# 安全帽位置数据测试工具

这个工具包用于测试安全帽位置数据接收和电子围栏检测功能。

## 文件说明

### 1. `create_test_fence.sql`
- 创建测试用电子围栏的SQL脚本
- 定义了一个位于北京天安门广场附近的矩形围栏区域
- 围栏坐标范围：
  - 经度：116.397428 ~ 116.407428 (约1公里宽)
  - 纬度：39.89923 ~ 39.90923 (约1公里高)

### 2. `test_helmet_location.py`
- 主要测试脚本
- 每10秒自动发送随机生成的安全帽位置数据
- 70%概率生成围栏内坐标，30%概率生成围栏外坐标
- 支持多设备同时测试

### 3. `quick_test.py`
- 快速测试脚本
- 发送一次性测试数据（2个围栏内设备 + 2个围栏外设备）
- 用于快速验证接口是否正常工作

## 使用步骤

### 第一步：创建电子围栏数据
```sql
-- 在数据库中执行以下SQL
source create_test_fence.sql;
```

### 第二步：安装Python依赖
```bash
pip install requests
```

### 第三步：修改API地址
在脚本中修改API地址为你的实际地址：
```python
api_url = "http://your-server:port/jeecg-boot/deviceHelmet/location/receive"
```

### 第四步：运行测试

#### 快速测试（推荐先运行）
```bash
python quick_test.py
```

#### 持续测试
```bash
python test_helmet_location.py
```

## 电子围栏坐标

测试围栏是一个矩形区域，坐标如下：
```json
[
  [116.397428, 39.90923],  // 西北角
  [116.407428, 39.90923],  // 东北角
  [116.407428, 39.89923],  // 东南角
  [116.397428, 39.89923],  // 西南角
  [116.397428, 39.90923]   // 回到起点
]
```

## 测试数据格式

发送的位置数据格式：
```json
[
  {
    "puname": "HELMET_001",           // 设备编号
    "gpstime": 1704067200000,         // GPS时间戳(毫秒)
    "alarmtype": "0",                 // 报警类型 0:正常 1:低电量 2:紧急
    "longitude": 116.400428,          // 经度
    "latitude": 39.90423,             // 纬度
    "speed": 2.5,                     // 速度(km/h)
    "power": "85",                    // 电量百分比
    "datatype": "GPS",                // 数据类型
    "reserved": ""                    // 保留字段
  }
]
```

## 预期结果

### 接口响应
成功时返回：
```json
{
  "success": true,
  "message": "位置数据接收成功"
}
```

### 数据库验证
1. 位置数据会存储到Redis中，key格式：`deviceHelmet:location:{puname}`
2. 调用获取位置接口时，返回的数据会包含 `isInFence` 字段
3. 围栏内的设备 `isInFence` 为 `true`
4. 围栏外的设备 `isInFence` 为 `false`

## 验证围栏检测功能

调用获取所有人员位置的接口：
```bash
GET /jeecg-boot/deviceHelmet/location/all
```

返回数据示例：
```json
{
  "success": true,
  "result": [
    {
      "puname": "HELMET_001",
      "longitude": 116.400428,
      "latitude": 39.90423,
      "isInFence": true,     // 在围栏内
      "isRegistered": false,
      "isAssessed": null,
      // ... 其他字段
    }
  ]
}
```

## 故障排除

### 1. 连接失败
- 检查API地址是否正确
- 确认服务是否启动
- 检查网络连接

### 2. 围栏检测不准确
- 确认电子围栏数据已正确插入数据库
- 检查围栏坐标格式是否正确
- 验证 `MapWktUtil.isPointInPolygon` 方法是否正常工作

### 3. 数据格式错误
- 检查经纬度数据类型（应为Double）
- 确认时间戳格式（毫秒级Long类型）
- 验证JSON格式是否正确

## 自定义围栏区域

如需测试其他区域，修改 `test_helmet_location.py` 中的 `fence_polygon` 坐标：
```python
self.fence_polygon = [
    [你的经度1, 你的纬度1],
    [你的经度2, 你的纬度2],
    [你的经度3, 你的纬度3],
    [你的经度4, 你的纬度4],
    [你的经度1, 你的纬度1]  # 回到起点
]
```

同时更新SQL脚本中的 `fence_radius` 字段。
