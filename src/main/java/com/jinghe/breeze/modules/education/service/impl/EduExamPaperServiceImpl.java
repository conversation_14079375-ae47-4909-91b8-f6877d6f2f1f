package com.jinghe.breeze.modules.education.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jinghe.breeze.common.enums.DelFlagEnum;
import com.jinghe.breeze.modules.education.entity.EduExamPaper;
import com.jinghe.breeze.modules.education.entity.EduExamPaperDetail;
import com.jinghe.breeze.modules.education.entity.EduQuestion;
import com.jinghe.breeze.modules.education.dto.PaperRule;
import com.jinghe.breeze.modules.education.entity.EduTrain;
import com.jinghe.breeze.modules.education.mapper.EduExamPaperMapper;
import com.jinghe.breeze.modules.education.service.IEduExamPaperDetailService;
import com.jinghe.breeze.modules.education.service.IEduExamPaperService;
import com.jinghe.breeze.modules.education.service.IEduQuestionService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.modules.education.service.IEduTrainService;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description: 试卷管理
 * @Author: jeecg-boot
 * @Date: 2025-06-25
 * @Version: V1.0
 */
@Slf4j
@Service
public class EduExamPaperServiceImpl extends ServiceImpl<EduExamPaperMapper, EduExamPaper> implements IEduExamPaperService {
    @Autowired
    private IEduExamPaperDetailService detailService;

    @Autowired
    private IEduTrainService trainService;
    @Resource
    private IEduQuestionService questionService;

    @Transactional(rollbackFor = Exception.class)
    public Result<?> create(EduExamPaper eduExamPaper) {
        // 参数校验
        if (CollectionUtil.isEmpty(eduExamPaper.getPaperRules())) {
            return Result.error("至少选择一种题型!");
        }
        if (Objects.isNull(eduExamPaper.getScore()) || eduExamPaper.getScore() == 0) {
            return Result.error("试卷总分不能为0!");
        }
        eduExamPaper.setRules(JSON.toJSONString(eduExamPaper.getPaperRules()));
        save(eduExamPaper);
        // 生成试卷详情快照
        List<EduExamPaperDetail> paperDetailList = generatePaperDetails(eduExamPaper);
        // 批量保存详情
        detailService.saveBatch(paperDetailList);
        return Result.OK("添加成功!");
    }

    /**
     * 生成试卷详情快照
     * 根据试卷配置的题型和数量，从题库中随机抽取题目并创建快照
     */
    private List<EduExamPaperDetail> generatePaperDetails(EduExamPaper eduExamPaper) {
        List<EduExamPaperDetail> paperDetailList = new ArrayList<>();

        // 解析 rules JSON 字符串
        String rulesJson = eduExamPaper.getRules();
        if (!StringUtils.hasLength(rulesJson)) {
            throw new RuntimeException("试卷规则配置不能为空!");
        }

        try {
            JSONArray rulesArray = JSON.parseArray(rulesJson);
            for (int i = 0; i < rulesArray.size(); i++) {
                JSONObject ruleConfig = rulesArray.getJSONObject(i);
                // 解析题型配置
                String type = ruleConfig.getString("type");
                String questionTypeText = ruleConfig.getString("questionTypeText");
                Integer count = ruleConfig.getInteger("count");
                Integer score = ruleConfig.getInteger("score");
                Integer subtotal = ruleConfig.getInteger("subtotal");
                // 参数校验
                if (!StringUtils.hasLength(type)) {
                    throw new RuntimeException("题目类型不能为空!");
                }
                if (count == null || count <= 0) {
                    throw new RuntimeException("题目数量必须大于0!");
                }
                if (score == null || score <= 0) {
                    throw new RuntimeException("题目分值必须大于0!");
                }
                // 根据题型和标签随机获取题目
                List<EduQuestion> questions = questionService.getRandomQuestionsByTypeAndTags(type, eduExamPaper.getTags(), count);
                // 为每道题目创建快照记录
                for (EduQuestion question : questions) {
                    EduExamPaperDetail paperDetail = new EduExamPaperDetail();
                    paperDetail.setPaperId(eduExamPaper.getId());
                    paperDetail.setType(question.getType());
                    paperDetail.setQuestionId(question.getId());
                    // 创建题目快照 - 将原题目的完整信息复制到试卷详情中
                    paperDetail.setContent(question.getContent());
                    paperDetail.setOptions(question.getOptions());
                    paperDetail.setAnswer(question.getAnswer());
                    paperDetail.setExplanation(question.getExplanation());
                    // 使用配置中的分值
                    paperDetail.setScore(new BigDecimal(score));
                    paperDetailList.add(paperDetail);
                }
            }
        } catch (Exception e) {
            log.error("解析试卷规则配置失败", e);
            throw new RuntimeException("解析试卷规则配置失败: " + e.getMessage());
        }

        log.info("试卷详情快照生成完成，共生成 {} 道题目", paperDetailList.size());
        return paperDetailList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> updatePaper(EduExamPaper eduExamPaper) {
        long startTime = System.currentTimeMillis();
        log.info("开始更新试卷: {}, ID: {}", eduExamPaper.getName(), eduExamPaper.getId());

        try {
            // 参数校验
            if (!CollectionUtil.isEmpty(eduExamPaper.getPaperRules())) {
                return Result.error("至少选择一种题型!");
            }
            eduExamPaper.setRules(JSON.toJSONString(eduExamPaper.getPaperRules()));
            if (Objects.isNull(eduExamPaper.getScore()) || eduExamPaper.getScore() == 0) {
                return Result.error("试卷总分不能为0!");
            }
            // 1. 更新试卷基本信息
            boolean updateResult = updateById(eduExamPaper);
            if (!updateResult) {
                return Result.error("更新试卷基本信息失败!");
            }
            // 2. 删除所有相关联的子表 EduExamPaperDetail
            LambdaQueryWrapper<EduExamPaperDetail> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(EduExamPaperDetail::getPaperId, eduExamPaper.getId());
            boolean deleteResult = detailService.remove(deleteWrapper);
            // 3. 重新生成子表 EduExamPaperDetail
            List<EduExamPaperDetail> paperDetailList = generatePaperDetails(eduExamPaper);
            // 4. 批量保存新的详情
            boolean saveResult = detailService.saveBatch(paperDetailList);
            if (!saveResult) {
                return Result.error("保存试卷详情失败!");
            }
            return Result.OK("更新成功!");
        } catch (Exception e) {
            log.error("更新试卷失败", e);
            throw e; // 重新抛出异常，让事务回滚
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> deletePaper(String id) {
        log.info("开始删除试卷，ID: {}", id);

        try {
            // 1. 删除试卷详情
            LambdaQueryWrapper<EduExamPaperDetail> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(EduExamPaperDetail::getPaperId, id);
            boolean deleteDetailResult = detailService.remove(deleteWrapper);
            log.info("删除试卷详情完成，删除结果: {}", deleteDetailResult);
            // 2. 删除试卷主表
            boolean deletePaperResult = removeById(id);
            if (!deletePaperResult) {
                return Result.error("删除试卷失败!");
            }
            return Result.OK("删除成功!");
        } catch (Exception e) {
            throw e; // 重新抛出异常，让事务回滚
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> deletePapers(List<String> ids) {
        log.info("开始批量删除试卷，数量: {}", ids.size());
        try {
            // 1. 批量删除试卷详情
            LambdaQueryWrapper<EduExamPaperDetail> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.in(EduExamPaperDetail::getPaperId, ids);
            boolean deleteDetailResult = detailService.remove(deleteWrapper);
            log.info("批量删除试卷详情完成，删除结果: {}", deleteDetailResult);
            // 2. 批量删除试卷主表
            boolean deletePaperResult = removeByIds(ids);
            if (!deletePaperResult) {
                return Result.error("批量删除试卷失败!");
            }
            log.info("批量删除试卷完成，数量: {}", ids.size());
            return Result.OK("批量删除成功!");
        } catch (Exception e) {
            log.error("批量删除试卷失败", e);
            throw e; // 重新抛出异常，让事务回滚
        }
    }

    @Override
    public void fillPaperDetails(List<EduExamPaper> eduExamPapers, boolean includeAnswerAndAnalysis) {
        if (eduExamPapers == null || eduExamPapers.isEmpty()) {
            return;
        }
        // 1. 获取所有试卷ID
        List<String> paperIds = eduExamPapers.stream().map(EduExamPaper::getId).collect(Collectors.toList());
        // 2. 批量查询所有试卷的详情数据
        List<EduTrain> trainList = trainService.list(new LambdaQueryWrapper<EduTrain>()
                .eq(EduTrain::getDelFlag, DelFlagEnum.NORMAL.getType())
                .in(EduTrain::getPaperId, paperIds));
        Map<String, List<EduTrain>> trainMap = trainList.stream().collect(Collectors.groupingBy(EduTrain::getPaperId));
        LambdaQueryWrapper<EduExamPaperDetail> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.in(EduExamPaperDetail::getPaperId, paperIds);
        List<EduExamPaperDetail> allDetails = detailService.list(detailWrapper);
        // 3. 按试卷ID分组详情数据
        Map<String, List<EduExamPaperDetail>> detailsByPaperId = allDetails.stream().collect(Collectors.groupingBy(EduExamPaperDetail::getPaperId));
        // 4. 为每个试卷填充数据
        for (EduExamPaper paper : eduExamPapers) {
            List<EduTrain> _trainList = trainMap.get(paper.getId());
            String trainNames = "";
            if (_trainList != null && !_trainList.isEmpty()) {
                trainNames = _trainList.stream().map(EduTrain::getSubject).collect(Collectors.joining(","));
                paper.setTrainNames(trainNames);
            }


            paper.setTrain(trainMap.get(paper.getId()));
            fillSinglePaperDetails(paper, detailsByPaperId.get(paper.getId()), includeAnswerAndAnalysis);
        }
    }

    /**
     * 为单个试卷填充详情数据
     */
    private void fillSinglePaperDetails(EduExamPaper paper, List<EduExamPaperDetail> paperDetails, boolean includeAnswerAndAnalysis) {
        if (paperDetails == null) {
            paperDetails = new ArrayList<>();
        }
        // 1. 解析 rules JSON 字符串
        List<PaperRule> paperRules = parseRulesJson(paper.getRules());
        // 2. 按题目类型分组详情数据
        Map<String, List<EduExamPaperDetail>> detailsByType = paperDetails.stream().collect(Collectors.groupingBy(EduExamPaperDetail::getType));
        // 3. 为每个 PaperRule 补充对应的详情列表
        for (PaperRule rule : paperRules) {
            List<EduExamPaperDetail> typeDetails = detailsByType.get(rule.getType());
            rule.setDetails(typeDetails != null ? typeDetails : new ArrayList<>());
            rule.getDetails().forEach(detail -> {
                if (!includeAnswerAndAnalysis) {
                    detail.setAnswer(null);
                    detail.setExplanation(null);
                }
            });
        }
        // 4. 设置到试卷对象中
        paper.setPaperRules(paperRules);
    }

    /**
     * 解析 rules JSON 字符串为 PaperRule 列表
     */
    private List<PaperRule> parseRulesJson(String rulesJson) {
        List<PaperRule> paperRules = new ArrayList<>();
        if (!StringUtils.hasLength(rulesJson)) {
            return paperRules;
        }
        try {
            JSONArray rulesArray = JSON.parseArray(rulesJson);
            for (int i = 0; i < rulesArray.size(); i++) {
                JSONObject ruleJson = rulesArray.getJSONObject(i);
                PaperRule rule = new PaperRule();
                rule.setType(ruleJson.getString("type"));
                rule.setQuestionTypeText(ruleJson.getString("questionTypeText"));
                rule.setCount(ruleJson.getInteger("count"));
                rule.setScore(ruleJson.getInteger("score"));
                rule.setSubtotal(ruleJson.getInteger("subtotal"));
                paperRules.add(rule);
            }
        } catch (Exception e) {
            log.error("解析试卷规则配置失败: {}", rulesJson, e);
        }
        return paperRules;
    }

}
