-- 创建测试用的电子围栏数据
-- 这个围栏定义了一个矩形区域，用于测试安全帽位置检测功能

-- 删除可能存在的测试数据
DELETE FROM project_fence WHERE code = 'TEST_FENCE_001';

-- 插入测试电子围栏数据
INSERT INTO project_fence (
    id, 
    create_by, 
    create_time, 
    update_by, 
    update_time, 
    del_flag, 
    sys_org_code, 
    project_id, 
    code, 
    name, 
    fence_type, 
    fence_radius, 
    remark
) VALUES (
    'TEST_FENCE_001', 
    'admin', 
    NOW(), 
    'admin', 
    NOW(), 
    0, 
    'A01', 
    'PROJECT_001', 
    'TEST_FENCE_001', 
    '安全帽测试电子围栏', 
    '1', 
    '[[116.397428, 39.90923], [116.407428, 39.90923], [116.407428, 39.89923], [116.397428, 39.89923], [116.397428, 39.90923]]', 
    '用于安全帽位置测试的电子围栏，覆盖北京天安门广场附近区域'
);

-- 验证插入结果
SELECT 
    id,
    name,
    fence_type,
    fence_radius,
    del_flag,
    create_time
FROM project_fence 
WHERE code = 'TEST_FENCE_001';

-- 围栏坐标说明:
-- 该围栏定义了一个矩形区域，坐标点如下：
-- 1. [116.397428, 39.90923] - 西北角
-- 2. [116.407428, 39.90923] - 东北角  
-- 3. [116.407428, 39.89923] - 东南角
-- 4. [116.397428, 39.89923] - 西南角
-- 5. [116.397428, 39.90923] - 回到起点形成闭合多边形
--
-- 围栏类型说明:
-- fence_type = '1' 表示 "安全防护围栏" (参考 ProjectFensEnum.YJ)
--
-- 坐标范围:
-- 经度范围: 116.397428 ~ 116.407428 (约1公里宽)
-- 纬度范围: 39.89923 ~ 39.90923 (约1公里高)
