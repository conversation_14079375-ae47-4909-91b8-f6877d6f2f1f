package com.jinghe.breeze.modules.project.service.impl;

import java.lang.reflect.Field;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.common.constants.enums.ProjectPbsEnum;
import com.jinghe.breeze.modules.construction.entity.ConstructionLog;
import com.jinghe.breeze.modules.construction.mapper.ConstructionLogMapper;
import com.jinghe.breeze.modules.construction.service.IConstructionLogService;
import com.jinghe.breeze.modules.data.entity.DataCableInfo;
import com.jinghe.breeze.modules.data.entity.DataFanInfo;
import com.jinghe.breeze.modules.data.service.IDataCableInfoService;
import com.jinghe.breeze.modules.data.service.IDataFanInfoService;
import com.jinghe.breeze.modules.project.entity.PbsBaseMapData;
import com.jinghe.breeze.modules.project.entity.ProjectPbs;
import com.jinghe.breeze.modules.project.entity.ProjectProcess;
import com.jinghe.breeze.modules.project.entity.vo.PbsVo;
import com.jinghe.breeze.modules.project.service.IProjectProcessService;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.oConvertUtils;
import com.jinghe.breeze.modules.project.mapper.ProjectPbsMapper;
import com.jinghe.breeze.modules.project.mapper.ProjectProcessMapper;
import com.jinghe.breeze.modules.project.service.IProjectPbsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.util.StringUtils;

/**
 * @Description: project_pbs
 * @Author: jeecg-boot
 * @Date: 2024-04-17
 * @Version: V1.0
 */
@Service
public class ProjectPbsServiceImpl extends ServiceImpl<ProjectPbsMapper, ProjectPbs> implements IProjectPbsService {
    @Autowired
    private IDataFanInfoService dataFanInfoService;
    @Autowired
    private IDataCableInfoService dataCableInfoService;
    @Autowired
    private IProjectProcessService projectProcessService;
    @Autowired
    private IConstructionLogService constructionLogService;
    @Autowired
    private ProjectProcessMapper projectProcessMapper;
    @Autowired
    private ConstructionLogMapper constructionLogMapper;

    @Override
    public void addProjectPbs(ProjectPbs projectPbs) {
        if (oConvertUtils.isEmpty(projectPbs.getPid())) {
            projectPbs.setPid(IProjectPbsService.ROOT_PID_VALUE);
        } else {
            // 如果当前节点父ID不为空 则设置父节点的hasChildren 为1
            ProjectPbs parent = baseMapper.selectById(projectPbs.getPid());
            if (parent != null && !(parent.getHasChild() == 1)) {
                parent.setHasChild(1);
                baseMapper.updateById(parent);
            }
        }
        baseMapper.insert(projectPbs);
    }

    @Override
    public void updateProjectPbs(ProjectPbs projectPbs) {
        ProjectPbs entity = this.getById(projectPbs.getId());
        if (entity == null) {
            throw new JeecgBootException("未找到对应实体");
        }
        String old_pid = entity.getPid();
        String new_pid = projectPbs.getPid();
        if (!old_pid.equals(new_pid)) {
            updateOldParentNode(old_pid);
            if (oConvertUtils.isEmpty(new_pid)) {
                projectPbs.setPid(IProjectPbsService.ROOT_PID_VALUE);
            }
            if (!IProjectPbsService.ROOT_PID_VALUE.equals(projectPbs.getPid())) {
                baseMapper.updateTreeNodeStatus(projectPbs.getPid(), IProjectPbsService.HASCHILD);
            }
        }
        baseMapper.updateById(projectPbs);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProjectPbs(String id) throws JeecgBootException {
        // 查询选中节点下所有子节点一并删除
        id = this.queryTreeChildIds(id);
        if (id.indexOf(",") > 0) {
            StringBuffer sb = new StringBuffer();
            String[] idArr = id.split(",");
            for (String idVal : idArr) {
                if (idVal != null) {
                    ProjectPbs projectPbs = this.getById(idVal);
                    String pidVal = projectPbs.getPid();
                    // 查询此节点上一级是否还有其他子节点
                    List<ProjectPbs> dataList = baseMapper.selectList(
                            new QueryWrapper<ProjectPbs>().eq("pid", pidVal).notIn("id", Arrays.asList(idArr)));
                    if ((dataList == null || dataList.size() == 0) && !Arrays.asList(idArr).contains(pidVal)
                            && !sb.toString().contains(pidVal)) {
                        // 如果当前节点原本有子节点 现在木有了，更新状态
                        sb.append(pidVal).append(",");
                    }
                }
            }
            // 批量删除节点
            baseMapper.deleteBatchIds(Arrays.asList(idArr));
            // 修改已无子节点的标识
            String[] pidArr = sb.toString().split(",");
            for (String pid : pidArr) {
                this.updateOldParentNode(pid);
            }
        } else {
            ProjectPbs projectPbs = this.getById(id);
            if (projectPbs == null) {
                throw new JeecgBootException("未找到对应实体");
            }
            updateOldParentNode(projectPbs.getPid());
            baseMapper.deleteById(id);
        }
    }

    @Override
    public List<ProjectPbs> queryTreeListNoPage(QueryWrapper<ProjectPbs> queryWrapper) {
        List<ProjectPbs> dataList = baseMapper.selectList(queryWrapper);
        List<ProjectPbs> mapList = new ArrayList<>();
        for (ProjectPbs data : dataList) {
            String pidVal = data.getPid();
            // 递归查询子节点的根节点
            if (pidVal != null && !"0".equals(pidVal)) {
                ProjectPbs rootVal = this.getTreeRoot(pidVal);
                if (rootVal != null && !mapList.contains(rootVal)) {
                    mapList.add(rootVal);
                }
            } else {
                if (!mapList.contains(data)) {
                    mapList.add(data);
                }
            }
        }
        return mapList;
    }

    @Transactional(rollbackFor = Exception.class)
    public void movePbs(ProjectPbs pbs, boolean isUp) {
        pbs = baseMapper.selectById(pbs.getId());
        if (pbs == null) {
            throw new JeecgBootException("记录不存在");
        }
        String pid = pbs.getPid();
        LambdaQueryWrapper<ProjectPbs> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjectPbs::getPid, pid);
        if (isUp) {
            wrapper.lt(ProjectPbs::getSortNo, pbs.getSortNo()).orderByDesc(ProjectPbs::getSortNo);
        } else { // down
            wrapper.gt(ProjectPbs::getSortNo, pbs.getSortNo()).orderByAsc(ProjectPbs::getSortNo);
        }
        wrapper.last("LIMIT 1");
        ProjectPbs target = baseMapper.selectOne(wrapper);
        if (target == null) {
            throw new JeecgBootException(isUp ? "被移动的对象已经是第一个" : "被移动的对象已经是最后一个");
        }
        int tmpSort = target.getSortNo();
        target.setSortNo(pbs.getSortNo());
        pbs.setSortNo(tmpSort);// 交换双方的sort
        baseMapper.updateById(pbs);
        baseMapper.updateById(target);
    }

    @Override
    public void setCanMoveUpDown(List<ProjectPbs> pbsList, List<ProjectPbs> allList) {
        for (ProjectPbs tree : pbsList) {
            String pidVal = tree.getPid();
            ProjectPbs biggerBrother = allList.stream()
                    .filter(d -> d.getPid().equals(pidVal) && d.getSortNo() > tree.getSortNo()).findFirst()
                    .orElse(null);
            tree.setDownEnable(biggerBrother != null);// 没有比自己更大的,不能下移
            ProjectPbs littleBrother = allList.stream()
                    .filter(d -> d.getPid().equals(pidVal) && d.getSortNo() < tree.getSortNo()).findFirst()
                    .orElse(null);
            tree.setUpEnable(littleBrother != null);// 没有比自己更小的,不能上移
        }
    }

    /**
     * 根据所传pid查询旧的父级节点的子节点并修改相应状态值
     *
     * @param pid
     */
    private void updateOldParentNode(String pid) {
        if (!IProjectPbsService.ROOT_PID_VALUE.equals(pid)) {
            Integer count = baseMapper.selectCount(new QueryWrapper<ProjectPbs>().eq("pid", pid));
            if (count == null || count <= 1) {
                baseMapper.updateTreeNodeStatus(pid, IProjectPbsService.NOCHILD);
            }
        }
    }

    /**
     * 递归查询节点的根节点
     *
     * @param pidVal
     * @return
     */
    private ProjectPbs getTreeRoot(String pidVal) {
        ProjectPbs data = baseMapper.selectById(pidVal);
        if (data != null && !"0".equals(data.getPid())) {
            return this.getTreeRoot(data.getPid());
        } else {
            return data;
        }
    }

    /**
     * 根据id查询所有子节点id
     *
     * @param ids
     * @return
     */
    private String queryTreeChildIds(String ids) {
        // 获取id数组
        String[] idArr = ids.split(",");
        StringBuffer sb = new StringBuffer();
        for (String pidVal : idArr) {
            if (pidVal != null) {
                if (!sb.toString().contains(pidVal)) {
                    if (sb.toString().length() > 0) {
                        sb.append(",");
                    }
                    sb.append(pidVal);
                    this.getTreeChildIds(pidVal, sb);
                }
            }
        }
        return sb.toString();
    }

    /**
     * 递归查询所有子节点
     *
     * @param pidVal
     * @param sb
     * @return
     */
    private StringBuffer getTreeChildIds(String pidVal, StringBuffer sb) {
        List<ProjectPbs> dataList = baseMapper.selectList(new QueryWrapper<ProjectPbs>().eq("pid", pidVal));
        if (dataList != null && dataList.size() > 0) {
            for (ProjectPbs tree : dataList) {
                if (!sb.toString().contains(tree.getId())) {
                    sb.append(",").append(tree.getId());
                }
                this.getTreeChildIds(tree.getId(), sb);
            }
        }
        return sb;
    }

    public List<ProjectPbs> buildTree(List<ProjectPbs> nodes) {
        Map<String, ProjectPbs> nodeMap = new HashMap<>();
        List<ProjectPbs> rootNodes = new ArrayList<>();
        for (ProjectPbs node : nodes) {
            nodeMap.put(node.getId(), node);
        }
        // 构建树形结构
        for (ProjectPbs node : nodes) {
            if (Objects.equals(node.getPid(), IProjectPbsService.ROOT_PID_VALUE) || node.getPid() == null) {
                rootNodes.add(node);
            } else {
                ProjectPbs parentNode = nodeMap.get(node.getPid());
                if (parentNode != null) {
                    parentNode.addChild(node);
                }
            }
        }
        return rootNodes;
    }

    private static void copyProperties(Object source, Object target) throws Exception {
        Class sourceClass = source.getClass();
        Class targetClass = target.getClass();

        for (Field sourceField : sourceClass.getDeclaredFields()) {
            Field targetField = targetClass.getDeclaredField(sourceField.getName());
            sourceField.setAccessible(true);
            targetField.setAccessible(true);
            Object value = sourceField.get(source);
            targetField.set(target, value);
        }
    }

    @Override
    public Result<?> getBaseMapData() {
        // 返回的字段为pbs完整信息加上
        // 1.风机 施工记录中 最后一个的icon
        // 2.海缆 对应的工序 施工记录中 是否全部完成

        LambdaQueryWrapper<ProjectPbs> pbsQuery = new LambdaQueryWrapper<>();
        pbsQuery.eq(ProjectPbs::getDelFlag, Common.delete_flag.OK);
        pbsQuery.eq(ProjectPbs::getLayer, Common.commonality.ZERO);
        List<ProjectPbs> projectPbsList = this.list(pbsQuery);
        // Map<String, ProjectProcess> processIconMap = new HashMap<>();
        // 取到所有末端节点(没有子节点)的code
        // List<String> codeList =
        // mySysCategoryService.getNodesWithoutChildren("B04").stream().map(c ->
        // c.getCode()).collect(Collectors.toList());
        // for (String code : codeList) { //对每个code 取到该code下对应的工序中 最后一个icon不为空的 记录放到Map中
        // List<ProjectProcess> processList = new
        // ArrayList<>(projectProcessService.list(new
        // LambdaQueryWrapper<ProjectProcess>()
        // .eq(ProjectProcess::getType, code)
        // .isNotNull(ProjectProcess::getIcon)
        // .orderByDesc(ProjectProcess::getProcessCode).last("limit 1")));
        // if (processList.size() > 0) {
        // processIconMap.put(code, processList.get(0));
        // }
        // }
        // 所有pbs中 类型为 B06A01A01 或 B06A01A02 即风机/海缆的集合
        List<String> pbsIds = projectPbsList.stream().map(x -> x.getId()).collect(Collectors.toList());
        LambdaQueryWrapper<ConstructionLog> logWrapper = new LambdaQueryWrapper<>();
        logWrapper.in(ConstructionLog::getPbsId, pbsIds).isNotNull(ConstructionLog::getEndTime);
        List<ConstructionLog> logList = constructionLogMapper.selectList(logWrapper);

        Map<String, List<ConstructionLog>> logs = logList.stream()
                .collect(Collectors.groupingBy(ConstructionLog::getPbsId));

        Set<String> processTypes = projectPbsList.stream().map(x -> x.getApplicableProcess())
                .collect(Collectors.toSet());

        LambdaQueryWrapper<ProjectProcess> processWrapper = new LambdaQueryWrapper<>();
        processWrapper.eq(ProjectProcess::getDelFlag, Common.delete_flag.OK)
                .in(ProjectProcess::getType, processTypes);
        List<ProjectProcess> projectProcesses = projectProcessMapper.selectList(processWrapper);
        Map<String, List<ProjectProcess>> processtype = projectProcesses.stream()
                .collect(Collectors.groupingBy(ProjectProcess::getType));

        List<ProjectPbs> targetPbsList = projectPbsList.stream()
                .filter(projectPbs -> {
                    String type = projectPbs.getType();
                    return (type.equals(ProjectPbsEnum.FJ.getCode()) || type.equals(ProjectPbsEnum.HL.getCode())
                            || type.equals(ProjectPbsEnum.SYZ.getCode()));
                }).collect(Collectors.toList());
        PbsBaseMapData pbsBaseMapData = new PbsBaseMapData(); // 要返回的底图数据
        List<PbsVo> fanpbsList = new ArrayList<>();
        List<PbsVo> cablePbsList = new ArrayList<>();
        List<PbsVo> syzPbsList = new ArrayList<>();
        LambdaQueryWrapper<DataCableInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DataCableInfo::getDelFlag, Common.delete_flag.OK);
        List<DataCableInfo> list = dataCableInfoService.list(queryWrapper);
        LambdaQueryWrapper<DataFanInfo> fanQuery = new LambdaQueryWrapper<>();
        fanQuery.eq(DataFanInfo::getDelFlag, Common.delete_flag.OK);
        List<DataFanInfo> fans = dataFanInfoService.list(fanQuery);
        for (ProjectPbs pbs : targetPbsList) {
            String type = pbs.getType();
            String pbsId = pbs.getId();
            List<ProjectProcess> projectProcesses1 = processtype.get(pbs.getApplicableProcess()); // 包含哪些工序
            List<ConstructionLog> pbsLogList = logs.get(pbs.getId()); // 包含哪些完成的施工记录

            if (Objects.equals(type, ProjectPbsEnum.FJ.getCode())) { // 风机
                String lastIcon = getLastIconFromConstructLog(pbsId);
                PbsVo pbsVo = new PbsVo(pbs);
                Optional<DataFanInfo> first = fans.stream().filter(x -> x.getId().equals(pbs.getSpecificationModel()))
                        .findFirst();
                if (first.isPresent()) {
                    pbsVo.setDataFanInfo(first.get());
                }
                pbsVo.setLastProcessIcon(lastIcon);
                fanpbsList.add(pbsVo);
            } else if (Objects.equals(type, ProjectPbsEnum.HL.getCode())) {
                PbsVo pbsVo = new PbsVo(pbs);
                Optional<DataCableInfo> first = list.stream().filter(x -> x.getId().equals(pbs.getSpecificationModel()))
                        .findFirst();
                if (first.isPresent()) {
                    pbsVo.setDataCableInfo(first.get());
                }
                Boolean isCompleted = projectProcesses1 != null && pbsLogList != null
                        && projectProcesses1.size() == pbsLogList.size(); //工序数量和 完成的施工记录数量一致
                pbsVo.setIsCompleted(isCompleted);
                cablePbsList.add(pbsVo);
            } else if (Objects.equals(type, ProjectPbsEnum.SYZ.getCode())) {
                PbsVo pbsVo = new PbsVo(pbs);
                syzPbsList.add(pbsVo);
            }
        }
        pbsBaseMapData.setFanList(fanpbsList);
        pbsBaseMapData.setSyzPbsList(syzPbsList);
        pbsBaseMapData.setCableList(cablePbsList);
        return Result.OK(pbsBaseMapData);
    }

    /**
     * 施工记录中根据pbsId取回最后一条施工过的工序的图标
     *
     * @param pbsId
     * @return
     */
    private String getLastIconFromConstructLog(String pbsId) {
        LambdaQueryWrapper<ConstructionLog> conLogWrapper = new LambdaQueryWrapper<>();
        LambdaQueryWrapper<ProjectProcess> processWrapper = new LambdaQueryWrapper<>();
        conLogWrapper.eq(ConstructionLog::getPbsId, pbsId).isNotNull(ConstructionLog::getStartTime);
        List<ConstructionLog> list = constructionLogService.list(conLogWrapper);
        if (list.size() == 0) {
            return null;
        }
        List<String> collect = list.stream().map(x -> x.getProcessId()).collect(Collectors.toList());
        processWrapper.in(ProjectProcess::getId, collect);
        processWrapper.orderByDesc(ProjectProcess::getProcessCode);
        List<ProjectProcess> list1 = projectProcessService.list(processWrapper);
        String icon = null;
        // for (ConstructionLog constructionLog : list) {
        // Optional<ProjectProcess> first = list1.stream()
        // .filter(x -> x.getId().equals(constructionLog.getProcessId())).findFirst();
        // if (first.isPresent() && !StringUtils.isEmpty(first.get().getIcon())) {
        // icon = first.get().getIcon();
        // }
        // }
        for (ProjectProcess projectProcess : list1) {
            if (!StringUtils.isEmpty(projectProcess.getIcon())) {
                icon = projectProcess.getIcon();
                break;
            }
        }
        // if (list1.isEmpty() && !StringUtils.isEmpty(list1.get(list1.size() -
        // 1).getIcon())) {
        // icon = list1.get(list1.size() - 1).getIcon();
        // }
        return icon;
    }

    // private Boolean getIsCompleted(String pbsId) {
    // int total = constructionLogService
    // .count(new
    // LambdaQueryWrapper<ConstructionLog>().eq(ConstructionLog::getPbsId, pbsId));
    // if (total == 0) { // 总数不为0 且 未完成的为 0
    // return false;
    // }
    // int unfinished = constructionLogService.count(new
    // LambdaQueryWrapper<ConstructionLog>()
    // .eq(ConstructionLog::getPbsId, pbsId)
    // .isNull(ConstructionLog::getEndTime));
    // return unfinished == 0;
    // }
}
