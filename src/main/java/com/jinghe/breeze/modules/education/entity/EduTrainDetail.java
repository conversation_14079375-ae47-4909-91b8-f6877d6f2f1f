package com.jinghe.breeze.modules.education.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.*;
/**
 * @Description: 培训记录
 * @Author: jeecg-boot
 * @Date:   2025-06-25
 * @Version: V1.0
 */
@Data
@TableName("edu_train_detail")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="edu_train_detail对象", description="培训记录")
public class EduTrainDetail implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)

    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/

    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/

    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/

    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**删除标识*/
	@Excel(name = "删除标识", width = 15)

    @ApiModelProperty(value = "删除标识")
    private java.lang.String delFlag;
	/**姓名*/
	@Excel(name = "姓名", width = 15)
    @NotNull(message = "姓名不能为空!")

    @ApiModelProperty(value = "姓名")
    private java.lang.String name;
	/**分包商*/
	@Excel(name = "分包商", width = 15)
    @NotNull(message = "分包商不能为空!")

    @ApiModelProperty(value = "分包商")
    private java.lang.String subcontractor;
	/**工种*/
	@Excel(name = "工种", width = 15)
    @NotNull(message = "工种不能为空!")

    @ApiModelProperty(value = "工种")
    private java.lang.String workType;
	/**身份证号*/
	@Excel(name = "身份证号", width = 15)
    @NotNull(message = "身份证号不能为空!")

    @ApiModelProperty(value = "身份证号")
    private java.lang.String idCard;
	/**考试成绩*/
	@Excel(name = "考试成绩", width = 15)
    @NotNull(message = "考试成绩不能为空!")

    @ApiModelProperty(value = "考试成绩")
    private BigDecimal score;

    @ApiModelProperty(value="培训id")
    private String trainId;
}
