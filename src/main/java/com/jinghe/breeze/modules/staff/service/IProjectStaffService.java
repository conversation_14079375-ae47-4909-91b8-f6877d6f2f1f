package com.jinghe.breeze.modules.staff.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghe.breeze.modules.staff.entity.ProjectStaff;
import com.jinghe.breeze.modules.staff.entity.StaffAttendance;
import com.jinghe.breeze.modules.staff.entity.StaffSalary;
import com.jinghe.breeze.modules.staff.entity.SubProject;

import java.util.List;

/**
 * @Description: 人员相关功能接口
 * @Author: jeecg-boot
 * @Date: 2024-11-04
 * @Version: V1.0
 */
public interface IProjectStaffService extends IService<ProjectStaff> {

    List<SubProject> subInfo();

    Page<ProjectStaff> staffPage(Integer pageNo, Integer pageSize, String subName, String name, String idCard);

    Page<StaffSalary> salaryPage(Integer pageNo, Integer pageSize, String subcontractorName, String monthIn, String userName, String idCard);

    Page<StaffAttendance> attendancePage(Integer pageNo, Integer pageSize, String subcontractorName, String name, String start, String end, String identityCard);

    String staffContract(String staffId);

    void staffSync();

    void salarySync(String month);

    void attendSync();

}
