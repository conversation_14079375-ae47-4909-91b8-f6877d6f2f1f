package com.jinghe.breeze.modules.staff.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghe.breeze.common.thirdPart.CggcUtil;
import com.jinghe.breeze.modules.staff.entity.*;
import com.jinghe.breeze.modules.staff.mapper.*;
import com.jinghe.breeze.modules.staff.service.IProjectStaffService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * @Description: 区域
 * @Author: jeecg-boot
 * @Date: 2024-08-13
 * @Version: V1.0
 */
@Service
public class ProjectStaffServiceImpl extends ServiceImpl<ProjectStaffMapper, ProjectStaff> implements IProjectStaffService {

    @Autowired
    private SubProjectMapper subProjectMapper;

    @Autowired
    private SubCompanyMapper subCompanyMapper;

    @Autowired
    private ProjectStaffMapper projectStaffMapper;

    @Autowired
    private StaffSalaryMapper salaryMapper;

    @Autowired
    private StaffAttendanceMapper attendanceMapper;

    @Autowired
    private CggcUtil cggcUtil;

    private static final SimpleDateFormat monthFormat = new SimpleDateFormat("yyyy-MM");

    @Override
    public List<SubProject> subInfo() {
        return subProjectMapper.selectList(Wrappers.emptyWrapper());
    }

    private List<SubCompany> companies() {
        return subCompanyMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public Page<ProjectStaff> staffPage(Integer pageNo, Integer pageSize, String subName, String name, String idCard) {
        LambdaQueryWrapper<ProjectStaff> queryWrapper = new LambdaQueryWrapper<>();
        if (!StringUtils.isEmpty(subName)) {
            queryWrapper.eq(ProjectStaff::getProjectId, subName);
        }
        if (!StringUtils.isEmpty(name)) {
            queryWrapper.like(ProjectStaff::getName, name);
        }
        if (!StringUtils.isEmpty(idCard)) {
            queryWrapper.like(ProjectStaff::getIdCard, idCard);
        }
        Page<ProjectStaff> page = new Page<>(pageNo, pageSize);
        return projectStaffMapper.selectPage(page, queryWrapper);
    }

    @Override
    public Page<StaffSalary> salaryPage(Integer pageNo, Integer pageSize, String subcontractorName, String monthIn, String userName, String idCard) {
        LambdaQueryWrapper<StaffSalary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(StaffSalary::getMonthIn);
        if (!StringUtils.isEmpty(subcontractorName)) {
            queryWrapper.eq(StaffSalary::getProjectId, subcontractorName);
        }
        if (!StringUtils.isEmpty(monthIn)) {
            queryWrapper.eq(StaffSalary::getMonthIn, monthIn);
        }
        if (!StringUtils.isEmpty(userName)) {
            queryWrapper.like(StaffSalary::getUserName, userName);
        }
        if (!StringUtils.isEmpty(idCard)) {
            queryWrapper.like(StaffSalary::getIdCard, idCard);
        }
        Page<StaffSalary> page = new Page<>(pageNo, pageSize);
        return salaryMapper.selectPage(page, queryWrapper);
    }

    @Override
    public Page<StaffAttendance> attendancePage(Integer pageNo, Integer pageSize, String subcontractorName, String name, String start, String end, String identityCard) {
        LambdaQueryWrapper<StaffAttendance> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(StaffAttendance::getClockDate);
        if (!StringUtils.isEmpty(subcontractorName)) {
            queryWrapper.eq(StaffAttendance::getProjectId, subcontractorName);
        }
        if (!StringUtils.isEmpty(start) && !StringUtils.isEmpty(end)) {
            queryWrapper.between(StaffAttendance::getClockDate, start, end);
        }
        if (!StringUtils.isEmpty(name)) {
            queryWrapper.like(StaffAttendance::getName, name);
        }
        if (!StringUtils.isEmpty(identityCard)) {
            queryWrapper.like(StaffAttendance::getIdentityCard, identityCard);
        }
        Page<StaffAttendance> page = new Page<>(pageNo, pageSize);
        return attendanceMapper.selectPage(page, queryWrapper);
    }

    @Override
    public String staffContract(String staffId) {
        LambdaQueryWrapper<ProjectStaff> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectStaff::getStaffId, staffId);
        queryWrapper.last("limit 1");
        ProjectStaff projectStaff = projectStaffMapper.selectOne(queryWrapper);
        if (projectStaff == null) {
            return null;
        } else {
            SubProject subProject = subProjectMapper.selectById(projectStaff.getProjectId());
            if (subProject == null) {
                return null;
            } else {
                try {
                    String s = cggcUtil.staffContract(staffId, subProject);
                    return s;
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }

    @Override
    @Async
    public void staffSync() {
        try {
            List<SubProject> subProjects = this.subInfo();
            for (SubProject subProject : subProjects) {
                int page = 1;
                int size = 20;
                int count = 20;
                while (count == size) {
                    String s = cggcUtil.projectStaff(subProject, page, size);
                    JSONObject resp = JSONObject.parseObject(s);
                    Integer state = resp.getInteger("state");
                    if (state == 2000) {
                        JSONObject content = resp.getJSONObject("content");
                        List<ProjectStaff> list = JSON.parseArray(content.getString("records"), ProjectStaff.class);
                        if (list != null) {
                            count = list.size();
                            page++;
                            for (ProjectStaff item : list) {
                                if (!StringUtils.isEmpty(item.getComingDate())) {
                                    item.setComingDate(item.getComingDate().substring(0, 10));
                                }
                                if (!StringUtils.isEmpty(item.getOutDate())) {
                                    item.setOutDate(item.getOutDate().substring(0, 10));
                                }
                                LambdaQueryWrapper<ProjectStaff> queryWrapper = new LambdaQueryWrapper<>();
                                queryWrapper.eq(ProjectStaff::getIdCard, item.getIdCard());
                                queryWrapper.eq(ProjectStaff::getProjectId, subProject.getId());
                                List<ProjectStaff> projectStaffs = projectStaffMapper.selectList(queryWrapper);
                                if (projectStaffs.isEmpty()) {
                                    item.setProjectId(subProject.getId());
                                    projectStaffMapper.insert(item);
                                } else {
                                    item.setId(projectStaffs.get(0).getId());
                                    projectStaffMapper.updateById(item);
                                }
                            }
                        } else {
                            count = 0;
                        }
                    } else {
                        count = 0;
                    }
                }
            }
            CggcUtil.STAFF_SYNCING = false;
        } catch (Exception e) {
            CggcUtil.STAFF_SYNCING = false;
            log.error(e.getMessage());
        }

    }

    @Override
    @Async
    public void salarySync(String month) {
        try {
            List<SubProject> subProjects = this.subInfo();
            for (SubProject project : subProjects) {
                String resStr = cggcUtil.projectSubInfo(project);
                JSONObject companyRes = JSONObject.parseObject(resStr);
                Integer companyState = companyRes.getInteger("state");
                if (companyState == 2000) {
                    JSONObject companyContent = companyRes.getJSONObject("content");
                    List<SubCompany> subCompanies = JSON.parseArray(companyContent.getString("records"), SubCompany.class);
                    if (subCompanies != null && !subCompanies.isEmpty()) {
                        for (SubCompany company : subCompanies) {
                            String s = cggcUtil.staffSalary(company.getCode(), month, project);
                            JSONObject resp = JSONObject.parseObject(s);
                            Integer state = resp.getInteger("state");
                            if (state == 2000) {
                                JSONObject content = resp.getJSONObject("content");
                                List<StaffSalary> list = JSON.parseArray(content.getString("records"), StaffSalary.class);
                                if (list != null) {
                                    for (StaffSalary item : list) {
                                        String salaryMonth = item.getMonthIn().substring(0, 7);
                                        LambdaQueryWrapper<StaffSalary> queryWrapper = new LambdaQueryWrapper<>();
                                        queryWrapper.eq(StaffSalary::getIdCard, item.getIdCard());
                                        queryWrapper.eq(StaffSalary::getMonthIn, salaryMonth);
                                        List<StaffSalary> salaryList = salaryMapper.selectList(queryWrapper);
                                        if (salaryList.isEmpty()) {
                                            item.setProjectId(project.getId());
                                            item.setMonthIn(salaryMonth);
                                            salaryMapper.insert(item);
                                        } else {
                                            item.setId(salaryList.get(0).getId());
                                            item.setMonthIn(salaryMonth);
                                            salaryMapper.updateById(item);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            CggcUtil.SALARY_SYNCING = false;
        } catch (Exception e) {
            CggcUtil.SALARY_SYNCING = false;
            log.error(e.getMessage());
        }
    }

    @Override
    @Async
    public void attendSync() {
        try {
            List<SubProject> subProjects = this.subInfo();
            for (SubProject project : subProjects) {
                String resStr = cggcUtil.projectSubInfo(project);
                JSONObject companyRes = JSONObject.parseObject(resStr);
                Integer companyState = companyRes.getInteger("state");
                if (companyState == 2000) {
                    JSONObject companyContent = companyRes.getJSONObject("content");
                    List<SubCompany> subCompanies = JSON.parseArray(companyContent.getString("records"), SubCompany.class);
                    if (subCompanies != null && !subCompanies.isEmpty()) {
                        for (SubCompany company : subCompanies) {
                            String s = cggcUtil.staffAttendance(company.getCode(), monthFormat.format(new Date()), project);
                            JSONObject resp = JSONObject.parseObject(s);
                            Integer state = resp.getInteger("state");
                            if (state == 2000) {
                                JSONObject content = resp.getJSONObject("content");
                                List<StaffAttendance> list = JSON.parseArray(content.getString("records"), StaffAttendance.class);
                                if (list != null) {
                                    for (StaffAttendance item : list) {
                                        LambdaQueryWrapper<StaffAttendance> queryWrapper = new LambdaQueryWrapper<>();
                                        queryWrapper.eq(StaffAttendance::getIdentityCard, item.getIdentityCard());
                                        queryWrapper.eq(StaffAttendance::getClockDate, item.getClockDate());
                                        List<StaffAttendance> attendances = attendanceMapper.selectList(queryWrapper);
                                        if (attendances.isEmpty()) {
                                            item.setProjectId(project.getId());
                                            attendanceMapper.insert(item);
                                        } else {
//                                            item.setId(attendances.get(0).getId());
//                                            attendanceMapper.updateById(item);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            CggcUtil.ATTEND_SYNCING = false;
        } catch (Exception e) {
            CggcUtil.ATTEND_SYNCING = false;
            log.error(e.getMessage());
        }
    }
}
