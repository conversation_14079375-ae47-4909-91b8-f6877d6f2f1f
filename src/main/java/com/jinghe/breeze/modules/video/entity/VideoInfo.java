package com.jinghe.breeze.modules.video.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.*;

/**
 * @Description: video_info
 * @Author: jeecg-boot
 * @Date: 2024-06-13
 * @Version: V1.0
 */
@Data
@TableName("video_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "video_info对象", description = "video_info")
public class VideoInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 标识
     */
    @TableId(type = IdType.ASSIGN_ID)
    @NotNull(message = "标识不能为空!")

    @ApiModelProperty(value = "标识")
    private java.lang.String id;
    /**
     * 创建人
     */

    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
    /**
     * 更新人
     */

    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")

    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
    /**
     * delFlag
     */
    @Excel(name = "delFlag", width = 15)
    @NotNull(message = "delFlag不能为空!")

    @ApiModelProperty(value = "delFlag")
    private java.lang.Integer delFlag;
    /**
     * 组织机构编码
     */

    @ApiModelProperty(value = "组织机构编码")
    private java.lang.String sysOrgCode;
    /**
     * 项目id
     */
    @Excel(name = "项目id", width = 15)

    @ApiModelProperty(value = "项目id")
    private java.lang.String projectId;
    /**
     * 文件夹id
     */
    @Excel(name = "文件夹id", width = 15)
    @NotNull(message = "文件夹id不能为空!")

    @ApiModelProperty(value = "文件夹id")
    private java.lang.String folderId;
    /**
     * 文件名
     */
    @Excel(name = "文件名", width = 15)
    @NotNull(message = "文件名不能为空!")
    @ApiModelProperty(value = "文件名")
    private java.lang.String file;

    /**
     * 文件类型
     */
    @Excel(name = "文件类型", width = 15)
    @NotNull(message = "类型名不能为空!")
    @ApiModelProperty(value = "文件名")
    private java.lang.Integer fileType;
}
