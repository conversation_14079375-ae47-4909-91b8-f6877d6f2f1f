package com.jinghe.breeze.modules.wind.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Description: wind_report_detail
 * @Author: jeecg-boot
 * @Date:   2024-05-24
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="避风船只", description="避风船只")
public class WindReportShipVo implements Serializable {
    private static final long serialVersionUID = 1L;

	/**避风报备id*/
    @ApiModelProperty(value = "避风报备id")
    private String reportId;

	/**船名*/
    @ApiModelProperty(value = "船舶Id")
    private String shipId;

    /**船名*/
    @ApiModelProperty(value = "船舶")
    private String shipName;

	/**MMSI*/
    @ApiModelProperty(value = "MMSI")
    private String mmsi;

	/**船上紧急联系人*/
    @ApiModelProperty(value = "船上紧急联系人")
    private String shipContact;

	/**在船人数*/
    @ApiModelProperty(value = "在船人数")
    private String personTotal;

	/**避风港围栏id*/
    @ApiModelProperty(value = "避风港围栏id")
    private String fenceId;

    /**单位*/
    @ApiModelProperty(value = "单位")
    private String department;

    /**是否进港*/
    @ApiModelProperty(value = "是否进港")
    private int status;

    @ApiModelProperty(value = "纬度")
    private int lat;

    @ApiModelProperty(value = "经度")
    private int lon;

	/**备注*/
    @ApiModelProperty(value = "备注")
    private String remark;
}
