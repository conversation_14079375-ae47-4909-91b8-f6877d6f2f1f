#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全帽位置数据测试脚本
每10秒向API接口推送随机生成的安全帽位置数据
"""

import requests
import json
import time
import random
import math
from datetime import datetime
from typing import List, Tuple

class HelmetLocationTester:
    def __init__(self, api_url: str = "http://localhost:8080/jeecg-boot/deviceHelmet/location/receive"):
        self.api_url = api_url
        
        # 定义一个多边形电子围栏区域 (以北京某工地为例)
        # 格式: [[经度, 纬度], [经度, 纬度], ...]
        self.fence_polygon = [
            [116.397428, 39.90923],   # 天安门广场附近的一个矩形区域
            [116.407428, 39.90923],   # 东北角
            [116.407428, 39.89923],   # 东南角
            [116.397428, 39.89923],   # 西南角
            [116.397428, 39.90923]    # 回到起点形成闭合多边形
        ]
        
        # 计算围栏的边界框
        self.min_lng = min(point[0] for point in self.fence_polygon)
        self.max_lng = max(point[0] for point in self.fence_polygon)
        self.min_lat = min(point[1] for point in self.fence_polygon)
        self.max_lat = max(point[1] for point in self.fence_polygon)
        
        # 设备编号列表
        self.device_codes = [
            "HELMET_001", "HELMET_002", "HELMET_003", "HELMET_004", "HELMET_005",
            "HELMET_006", "HELMET_007", "HELMET_008", "HELMET_009", "HELMET_010"
        ]
        
        print("电子围栏多边形坐标:")
        print(json.dumps(self.fence_polygon, indent=2))
        print(f"围栏边界: 经度[{self.min_lng:.6f}, {self.max_lng:.6f}], 纬度[{self.min_lat:.6f}, {self.max_lat:.6f}]")

    def point_in_polygon(self, point: Tuple[float, float], polygon: List[Tuple[float, float]]) -> bool:
        """
        使用射线法判断点是否在多边形内
        """
        x, y = point
        n = len(polygon)
        inside = False

        p1x, p1y = polygon[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y

        return inside

    def generate_random_location(self, inside_fence_probability: float = 0.7) -> Tuple[float, float]:
        """
        生成随机位置坐标
        inside_fence_probability: 生成围栏内坐标的概率
        """
        if random.random() < inside_fence_probability:
            # 生成围栏内的坐标
            attempts = 0
            while attempts < 100:  # 最多尝试100次
                lng = random.uniform(self.min_lng, self.max_lng)
                lat = random.uniform(self.min_lat, self.max_lat)
                if self.point_in_polygon((lng, lat), [(p[0], p[1]) for p in self.fence_polygon]):
                    return lng, lat
                attempts += 1
            
            # 如果100次都没生成到围栏内，就在围栏中心附近生成
            center_lng = (self.min_lng + self.max_lng) / 2
            center_lat = (self.min_lat + self.max_lat) / 2
            return center_lng, center_lat
        else:
            # 生成围栏外的坐标
            # 在围栏边界框外扩展一些范围
            expand_factor = 0.002  # 约200米
            lng = random.uniform(self.min_lng - expand_factor, self.max_lng + expand_factor)
            lat = random.uniform(self.min_lat - expand_factor, self.max_lat + expand_factor)
            
            # 确保生成的点在围栏外
            if self.point_in_polygon((lng, lat), [(p[0], p[1]) for p in self.fence_polygon]):
                # 如果意外生成在围栏内，就移到围栏外
                lng = self.max_lng + expand_factor
                lat = self.max_lat + expand_factor
            
            return lng, lat

    def generate_location_data(self) -> List[dict]:
        """
        生成位置数据列表
        """
        location_data_list = []
        current_time = int(time.time() * 1000)  # 毫秒时间戳
        
        # 随机选择1-3个设备发送数据
        num_devices = random.randint(1, 3)
        selected_devices = random.sample(self.device_codes, num_devices)
        
        for device_code in selected_devices:
            lng, lat = self.generate_random_location()
            
            location_data = {
                "puname": device_code,
                "gpstime": current_time,
                "alarmtype": random.choice(["0", "1", "2"]),  # 0:正常, 1:低电量, 2:紧急
                "longitude": round(lng, 6),
                "latitude": round(lat, 6),
                "speed": round(random.uniform(0, 5), 2),  # 步行速度 0-5 km/h
                "power": str(random.randint(20, 100)),  # 电量百分比
                "datatype": "GPS",
                "reserved": ""
            }
            
            location_data_list.append(location_data)
            
            # 检查是否在围栏内
            is_inside = self.point_in_polygon((lng, lat), [(p[0], p[1]) for p in self.fence_polygon])
            print(f"设备 {device_code}: 经度={lng:.6f}, 纬度={lat:.6f}, 围栏内={is_inside}")
        
        return location_data_list

    def send_location_data(self, location_data_list: List[dict]) -> bool:
        """
        发送位置数据到API接口
        """
        try:
            headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
            
            response = requests.post(
                self.api_url,
                json=location_data_list,
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success', False):
                    print(f"✅ 数据发送成功: {result.get('message', '')}")
                    return True
                else:
                    print(f"❌ 服务器返回错误: {result.get('message', '')}")
                    return False
            else:
                print(f"❌ HTTP错误: {response.status_code} - {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求失败: {e}")
            return False
        except Exception as e:
            print(f"❌ 发送数据时发生错误: {e}")
            return False

    def run_test(self, duration_minutes: int = 60):
        """
        运行测试，持续指定分钟数
        """
        print(f"开始测试，将运行 {duration_minutes} 分钟...")
        print(f"API地址: {self.api_url}")
        print("=" * 60)
        
        start_time = time.time()
        end_time = start_time + (duration_minutes * 60)
        count = 0
        
        try:
            while time.time() < end_time:
                count += 1
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                print(f"\n[{current_time}] 第 {count} 次发送:")
                
                # 生成并发送位置数据
                location_data = self.generate_location_data()
                print(f"生成 {len(location_data)} 条位置数据")
                
                success = self.send_location_data(location_data)
                if success:
                    print("数据发送成功")
                else:
                    print("数据发送失败")
                
                # 等待10秒
                print("等待10秒...")
                time.sleep(10)
                
        except KeyboardInterrupt:
            print("\n\n⚠️  用户中断测试")
        except Exception as e:
            print(f"\n\n❌ 测试过程中发生错误: {e}")
        
        print(f"\n测试结束，共发送了 {count} 次数据")

    def print_fence_sql(self):
        """
        打印创建电子围栏的SQL语句
        """
        fence_json = json.dumps(self.fence_polygon)
        
        sql = f"""
-- 创建电子围栏数据的SQL语句
INSERT INTO project_fence (
    id, 
    create_by, 
    create_time, 
    update_by, 
    update_time, 
    del_flag, 
    sys_org_code, 
    project_id, 
    code, 
    name, 
    fence_type, 
    fence_radius, 
    remark
) VALUES (
    '1', 
    'admin', 
    NOW(), 
    'admin', 
    NOW(), 
    0, 
    'A01', 
    'PROJECT_001', 
    'FENCE_001', 
    '测试电子围栏', 
    '1', 
    '{fence_json}', 
    '用于安全帽位置测试的电子围栏'
);
"""
        print("=" * 60)
        print("电子围栏数据库插入SQL:")
        print("=" * 60)
        print(sql)
        print("=" * 60)


def main():
    # 配置API地址 - 请根据实际情况修改
    api_url = "http://localhost:8080/jeecg-boot/deviceHelmet/location/receive"
    
    # 创建测试器实例
    tester = HelmetLocationTester(api_url)
    
    # 打印SQL语句用于创建围栏数据
    tester.print_fence_sql()
    
    # 询问用户是否开始测试
    print("\n请先执行上面的SQL语句创建电子围栏数据，然后按回车开始测试...")
    input()
    
    # 运行测试 (默认60分钟，可以修改)
    tester.run_test(duration_minutes=60)


if __name__ == "__main__":
    main()
