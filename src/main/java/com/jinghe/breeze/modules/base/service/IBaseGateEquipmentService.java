package com.jinghe.breeze.modules.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghe.breeze.modules.base.entity.BaseGateEquipment;

import java.util.List;

/**
 * @Description: base_gate_equipment
 * @Author: jeecg-boot
 * @Date: 2024-08-06
 * @Version: V1.0
 */
public interface IBaseGateEquipmentService extends IService<BaseGateEquipment> {

    public List<BaseGateEquipment> selectByMainId(String mainId);
}
