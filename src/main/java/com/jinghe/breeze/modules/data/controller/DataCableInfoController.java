package com.jinghe.breeze.modules.data.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.jinghe.breeze.modules.data.entity.DataCableInfo;
import com.jinghe.breeze.modules.data.service.IDataCableInfoService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: data_cable_info
 * @Author: jeecg-boot
 * @Date: 2024-05-22
 * @Version: V1.0
 */
@Api(tags = "data_cable_info")
@RestController
@RequestMapping("/data/dataCableInfo")
@Slf4j
public class DataCableInfoController extends JeecgController<DataCableInfo, IDataCableInfoService> {
    @Autowired
    private IDataCableInfoService dataCableInfoService;

    /**
     * 分页列表查询
     *
     * @param dataCableInfo
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "data_cable_info-分页列表查询")
    @ApiOperation(value = "data_cable_info-分页列表查询", notes = "data_cable_info-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(DataCableInfo dataCableInfo,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<DataCableInfo> queryWrapper = QueryGenerator.initQueryWrapper(dataCableInfo, req.getParameterMap());
        Page<DataCableInfo> page = new Page<DataCableInfo>(pageNo, pageSize);
        IPage<DataCableInfo> pageList = dataCableInfoService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param dataCableInfo
     * @return
     */
    @AutoLog(value = "data_cable_info-添加")
    @ApiOperation(value = "data_cable_info-添加", notes = "data_cable_info-添加")
    @RequiresPermissions("dataCableInfo:add")
    @PostMapping(value = "/add")
    public Result<?> add(@Validated @RequestBody DataCableInfo dataCableInfo) {
        dataCableInfoService.save(dataCableInfo);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param dataCableInfo
     * @return
     */
    @AutoLog(value = "data_cable_info-编辑")
    @ApiOperation(value = "data_cable_info-编辑", notes = "data_cable_info-编辑")
    @RequiresPermissions("dataCableInfo:edit")
    @PutMapping(value = "/edit")
    public Result<?> edit(@Validated @RequestBody DataCableInfo dataCableInfo) {
        dataCableInfoService.updateById(dataCableInfo);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "data_cable_info-通过id删除")
    @ApiOperation(value = "data_cable_info-通过id删除", notes = "data_cable_info-通过id删除")
    @RequiresPermissions("dataCableInfo:delete")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        dataCableInfoService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "data_cable_info-批量删除")
    @ApiOperation(value = "data_cable_info-批量删除", notes = "data_cable_info-批量删除")
    @RequiresPermissions("dataCableInfo:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.dataCableInfoService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "data_cable_info-通过id查询")
    @ApiOperation(value = "data_cable_info-通过id查询", notes = "data_cable_info-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        DataCableInfo dataCableInfo = dataCableInfoService.getById(id);
        if (dataCableInfo == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(dataCableInfo);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param dataCableInfo
     */
    @RequiresPermissions("dataCableInfo:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DataCableInfo dataCableInfo) {
        return super.exportXls(request, dataCableInfo, DataCableInfo.class, "data_cable_info");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("dataCableInfo:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DataCableInfo.class);
    }

}
