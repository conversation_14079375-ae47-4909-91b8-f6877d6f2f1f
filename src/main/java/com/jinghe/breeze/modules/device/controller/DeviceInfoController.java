package com.jinghe.breeze.modules.device.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinghe.breeze.common.enums.DeviceTypeEnum;
import com.jinghe.breeze.common.thirdPart.ArtemisUtil;
import com.jinghe.breeze.modules.device.entity.DeviceGate;
import com.jinghe.breeze.modules.device.entity.DeviceInfo;
import com.jinghe.breeze.modules.device.service.IDeviceGateService;
import com.jinghe.breeze.modules.device.service.IDeviceInfoService;
import com.jinghe.breeze.modules.device.vo.BaseDevicePage;
import com.jinghe.breeze.modules.monitor.entity.DeviceOnlineCountRecord;
import com.jinghe.breeze.modules.monitor.service.IDeviceOnlineCountRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @Description: 设备信息
 * @Author: jeecg-boot
 * @Date: 2024-08-13
 * @Version: V1.0
 */
@Api(tags = "设备信息")
@RestController
@RequestMapping("/device/deviceInfo")
@Slf4j
public class DeviceInfoController {
    @Autowired
    private IDeviceInfoService baseDeviceService;
    @Autowired
    private IDeviceGateService baseDeviceGateService;
    @Autowired
    private IDeviceOnlineCountRecordService onlineCountRecordService;

    @Autowired
    private ArtemisUtil artemisUtil;

    /**
     * 分页列表查询
     *
     * @param baseDevice
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "设备信息-分页列表查询")
    @ApiOperation(value = "设备信息-分页列表查询", notes = "设备信息-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(DeviceInfo baseDevice,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) throws Exception {
        if (StrUtil.isEmpty(baseDevice.getDeviceType())) {
            return Result.error("设备类型不能为空");
        }
        QueryWrapper<DeviceInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("device_type", baseDevice.getDeviceType());
        if (StrUtil.isNotEmpty(baseDevice.getMonitorType())) {
            queryWrapper.eq("monitor_type", baseDevice.getMonitorType());
        }
        if (StrUtil.isNotEmpty(baseDevice.getInstallPosition())) {
            queryWrapper.like("install_position", baseDevice.getInstallPosition());
        }
        if (StrUtil.isNotEmpty(baseDevice.getAreaId())) {
            queryWrapper.eq("area_id", baseDevice.getAreaId());
        }
        if (StrUtil.isNotEmpty(baseDevice.getName())) {
            queryWrapper.like("name", baseDevice.getName());
        }
        if (StrUtil.isNotEmpty(baseDevice.getEquipmentSerial())) {
            queryWrapper.and(wrapper -> wrapper.like("name", baseDevice.getEquipmentSerial()).or().like("equipment_serial", baseDevice.getEquipmentSerial()));
        }
        Page<DeviceInfo> page = new Page<DeviceInfo>(pageNo, pageSize);
        IPage<DeviceInfo> pageList = baseDeviceService.page(page, queryWrapper);
        IPage<BaseDevicePage> pageResult = new Page<>();
        //设置明细数据
        List<DeviceInfo> records = pageList.getRecords();
        List<BaseDevicePage> result = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(records)) {
            for (DeviceInfo record : records) {
                BaseDevicePage obj = new BaseDevicePage();
                BeanUtil.copyProperties(record, obj);
                if (DeviceTypeEnum.GATE.getType().equals(baseDevice.getDeviceType())) {
                    List<DeviceGate> baseDeviceGates = baseDeviceGateService.selectByMainId(record.getId());
                    obj.setDeviceGateList(baseDeviceGates);
                }
                result.add(obj);
            }
        }
//        监控点位i需要查询到当前监控点位的在线状态
        if ("monitor".equals(baseDevice.getDeviceType())) {
//        取出 result中所有的equipmentSerial  组成新的数组
            List<String> equipmentSerials = result.stream().map(BaseDevicePage::getEquipmentSerial).collect(Collectors.toList());
//        设置一个 param对象  然后让如下面的属性
            Map<String, Object> param = new HashMap<>();
            param.put("pageSize", result.size());
            param.put("pageNo", 1);
            param.put("indexCodes", equipmentSerials);
//       转json
            String json = JSONUtil.toJsonStr(param);
            val execute = JSONUtil.parseObj(artemisUtil.execute("/api/nms/v1/online/camera/get", json));
            if (Objects.equals(execute.get("code"), "0")) {
                // 获取data对象
                JSONObject data = execute.getJSONObject("data");

                // 获取data中的list属性，注意list是一个JSONArray
                JSONArray list = data.getJSONArray("list");

                // 将 result 转为 Map，以 equipmentSerial 为键
                Map<String, BaseDevicePage> resultMap = result.stream()
                        .collect(Collectors.toMap(BaseDevicePage::getEquipmentSerial, device -> device));

                // 遍历 list 数组
                for (int i = 0; i < list.size(); i++) {
                    // 获取 list 中的每个对象
                    JSONObject map = list.getJSONObject(i);

                    // 获取设备的 equipmentSerial
                    String equipmentSerial = map.getStr("indexCode");

                    // 如果 resultMap 中包含对应的 equipmentSerial
                    if (resultMap.containsKey(equipmentSerial)) {
                        // 找到对应的 BaseDevicePage 对象
                        BaseDevicePage devicePage = resultMap.get(equipmentSerial);
                        devicePage.setOnLine(map.getStr("online"));
                        // 将 online 值赋给对应的 BaseDevicePage 对象
                        System.out.println("Updated device: " + equipmentSerial + ", online: ");
                    }
                }
            } else {
//                循环result 设置字段 online为0
                result.forEach(devicePage -> devicePage.setOnLine("0"));
            }

        }
        pageResult.setRecords(result);

        pageResult.setTotal(pageList.getTotal());
        return Result.OK(pageResult);
    }

    /**
     * 添加
     *
     * @param baseDevicePage
     * @return
     */
    @AutoLog(value = "设备信息-添加")
    @ApiOperation(value = "设备信息-添加", notes = "设备信息-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody BaseDevicePage baseDevicePage) {
        // 执行通用校验逻辑
        Result<?> validationResult = validateDevice(baseDevicePage, null);
        if (!validationResult.isSuccess()) {
            return validationResult;
        }

        // 保存设备信息
        DeviceInfo baseDevice = new DeviceInfo();
        BeanUtils.copyProperties(baseDevicePage, baseDevice);
        baseDeviceService.saveMain(baseDevice, baseDevicePage.getDeviceGateList());
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param baseDevicePage
     * @return
     */
    @AutoLog(value = "设备信息-编辑")
    @ApiOperation(value = "设备信息-编辑", notes = "设备信息-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody BaseDevicePage baseDevicePage) {
        // 执行通用校验逻辑
        Result<?> validationResult = validateDevice(baseDevicePage, baseDevicePage.getId());
        if (!validationResult.isSuccess()) {
            return validationResult;
        }

        // 更新设备信息
        DeviceInfo baseDevice = new DeviceInfo();
        BeanUtils.copyProperties(baseDevicePage, baseDevice);
        DeviceInfo baseDeviceEntity = baseDeviceService.getById(baseDevice.getId());
        if (baseDeviceEntity == null) {
            return Result.error("未找到对应数据");
        }
        baseDeviceService.updateMain(baseDevice, baseDevicePage.getDeviceGateList());
        return Result.OK("编辑成功!");
    }


    /**
     * 仅编辑设备 点位信息
     *
     * @param baseDevicePage
     * @return
     */
    @AutoLog(value = "设备信息-仅编辑设备点位信息")
    @ApiOperation(value = "设备信息-编辑", notes = "设备信息-仅编辑设备点位信息")
    @PutMapping(value = "/editPosition")
    public Result<?> editPosition(@RequestBody BaseDevicePage baseDevicePage) {
        if (ObjectUtil.isEmpty(baseDevicePage.getDeviceType())) {
            return Result.error("设备类型不能为空!");
        }
        if (ObjectUtil.isEmpty(baseDevicePage.getId())) {
            return Result.error("点位id不能为空!");
        }
        // 更新设备信息
        DeviceInfo baseDeviceEntity = baseDeviceService.getById(baseDevicePage.getId());
        baseDeviceEntity.setPositionX(baseDevicePage.getPositionX());
        baseDeviceEntity.setPositionY(baseDevicePage.getPositionY());
        if (baseDeviceEntity == null) {
            return Result.error("未找到对应数据");
        }
        baseDeviceService.updateById(baseDeviceEntity);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "设备信息-通过id删除")
    @ApiOperation(value = "设备信息-通过id删除", notes = "设备信息-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        baseDeviceService.delMain(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "设备信息-批量删除")
    @ApiOperation(value = "设备信息-批量删除", notes = "设备信息-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.baseDeviceService.delBatchMain(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "设备信息-通过id查询")
    @ApiOperation(value = "设备信息-通过id查询", notes = "设备信息-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        DeviceInfo baseDevice = baseDeviceService.getById(id);
        if (baseDevice == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(baseDevice);
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "闸机设备信息通过主表ID查询")
    @ApiOperation(value = "闸机设备信息主表ID查询", notes = "闸机设备信息-通主表ID查询")
    @GetMapping(value = "/queryBaseDeviceGateByMainId")
    public Result<?> queryBaseDeviceGateListByMainId(@RequestParam(name = "id", required = true) String id) {
        List<DeviceGate> baseDeviceGateList = baseDeviceGateService.selectByMainId(id);
        return Result.OK(baseDeviceGateList);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param baseDevice
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DeviceInfo baseDevice) {
        // Step.1 组装查询条件查询数据
        QueryWrapper<DeviceInfo> queryWrapper = QueryGenerator.initQueryWrapper(baseDevice, request.getParameterMap());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        //Step.2 获取导出数据
        List<DeviceInfo> queryList = baseDeviceService.list(queryWrapper);
        // 过滤选中数据
        String selections = request.getParameter("selections");
        List<DeviceInfo> baseDeviceList = new ArrayList<DeviceInfo>();
        if (oConvertUtils.isEmpty(selections)) {
            baseDeviceList = queryList;
        } else {
            List<String> selectionList = Arrays.asList(selections.split(","));
            baseDeviceList = queryList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
        }

        // Step.3 组装pageList
        List<BaseDevicePage> pageList = new ArrayList<BaseDevicePage>();
        for (DeviceInfo main : baseDeviceList) {
            BaseDevicePage vo = new BaseDevicePage();
            BeanUtils.copyProperties(main, vo);
            List<DeviceGate> baseDeviceGateList = baseDeviceGateService.selectByMainId(main.getId());
            vo.setDeviceGateList(baseDeviceGateList);
            pageList.add(vo);
        }

        // Step.4 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "设备信息列表");
        mv.addObject(NormalExcelConstants.CLASS, BaseDevicePage.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("设备信息数据", "导出人:" + sysUser.getRealname(), "设备信息"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<BaseDevicePage> list = ExcelImportUtil.importExcel(file.getInputStream(), BaseDevicePage.class, params);
                for (BaseDevicePage page : list) {
                    DeviceInfo po = new DeviceInfo();
                    BeanUtils.copyProperties(page, po);
                    baseDeviceService.saveMain(po, page.getDeviceGateList());
                }
                return Result.OK("文件导入成功！数据行数:" + list.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.OK("文件导入失败！");
    }

    /**
     * 获取当前系统所有得监控设备-得到在线设备和离线设备数量
     *
     * @param
     * @return
     */
    @AutoLog(value = "设备信息-仅编辑设备点位信息")
    @ApiOperation(value = "设备信息-编辑", notes = "设备信息-仅编辑设备点位信息")
    @GetMapping(value = "/nowMonitorOnline")
    public Result<?> nowMonitorOnline() {
//        获取所有的监控设备
//        查询条件
        QueryWrapper<DeviceInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("device_type", "monitor");
        queryWrapper.eq("del_flag", "0");
        List<DeviceInfo> devicelist = baseDeviceService.list(queryWrapper);
        int onLineSize = 0;
        int offLineSize = 0;
        if (ObjectUtil.isNotEmpty(devicelist)) {
            List<String> equipmentSerials = devicelist.stream().map(DeviceInfo::getEquipmentSerial).collect(Collectors.toList());
            Map<String, Object> param = new HashMap<>();
            param.put("pageSize", equipmentSerials.size());
            param.put("pageNo", 1);
            param.put("indexCodes", equipmentSerials);
            String json = JSONUtil.toJsonStr(param);
            try {
                val execute = JSONUtil.parseObj(artemisUtil.execute("/api/nms/v1/online/camera/get", json));
                if (Objects.equals(execute.get("code"), "0")) {
                    JSONObject data = execute.getJSONObject("data");
                    // 获取data中的list属性，注意list是一个JSONArray
                    JSONArray list = data.getJSONArray("list");

                    // 将 result 转为 Map，以 equipmentSerial 为键
                    Map<String, DeviceInfo> resultMap = devicelist.stream()
                            .collect(Collectors.toMap(DeviceInfo::getEquipmentSerial, device -> device));
                    // 遍历 list 数组
                    for (int i = 0; i < list.size(); i++) {
                        // 获取 list 中的每个对象
                        JSONObject map = list.getJSONObject(i);
                        String equipmentSerial = map.getStr("indexCode");
                        // 如果 resultMap 中包含对应的 equipmentSerial
                        if (resultMap.containsKey(equipmentSerial)) {
                            // 找到对应的 BaseDevicePage 对象
                            if (Objects.equals(map.get("online"), 1)) {
                                onLineSize++;
                            } else if (Objects.equals(map.get("online"), 0)) {
                                offLineSize++;
                            }
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        Map<String, Object> result = new HashMap<>();
        result.put("onLineSize", onLineSize);
        result.put("offLineSize", offLineSize);
        result.put("unknown", devicelist.size() - onLineSize - offLineSize);
        return Result.OK(result);
    }


    @AutoLog(value = "设备信息-监控列表查询")
    @ApiOperation(value = "设备信息-监控列表查询", notes = "设备信息-监控列表查询")
    @GetMapping(value = "/monitorList")
    public Result<?> monitorList(DeviceInfo baseDevice,
                                 @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                 @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                 HttpServletRequest req) {
        IPage<BaseDevicePage> pageResult = new Page<>();
        List<String> baseIds = null;
        QueryWrapper<DeviceInfo> queryWrapper = QueryGenerator.initQueryWrapper(baseDevice, req.getParameterMap());
        Page<DeviceInfo> page = new Page<DeviceInfo>(pageNo, pageSize);
        IPage<DeviceInfo> pageList = baseDeviceService.page(page, queryWrapper);

        //设置明细数据
        List<DeviceInfo> records = pageList.getRecords();
        List<BaseDevicePage> result = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(records)) {
            for (DeviceInfo record : records) {
                BaseDevicePage obj = new BaseDevicePage();
                BeanUtil.copyProperties(record, obj);
                result.add(obj);
            }
        }
        pageResult.setRecords(result);
        pageResult.setTotal(pageList.getTotal());
        return Result.OK(pageResult);
    }

    @AutoLog(value = "设备信息-删除标记")
    @ApiOperation(value = "设备信息-删除标记", notes = "设备信息-删除标记")
    @PutMapping(value = "/cleanMark")
    public Result<?> cleanMark(@RequestBody BaseDevicePage baseDevicePage) {
        DeviceInfo baseDevice = new DeviceInfo();
        BeanUtils.copyProperties(baseDevicePage, baseDevice);
        DeviceInfo baseDeviceEntity = baseDeviceService.getById(baseDevice.getId());
        if (baseDeviceEntity == null) {
            return Result.error("未找到对应数据");
        }
        LambdaUpdateWrapper<DeviceInfo> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(DeviceInfo::getPositionX, baseDevice.getPositionX());
        lambdaUpdateWrapper.set(DeviceInfo::getPositionY, baseDevice.getPositionY());
        lambdaUpdateWrapper.eq(DeviceInfo::getId, baseDevice.getId());
        baseDeviceService.update(lambdaUpdateWrapper);
        return Result.OK("操作成功!");
    }

    @AutoLog(value = "设备在线率记录")
    @ApiOperation(value = "设备信息-删除标记", notes = "设备信息-删除标记")
    @GetMapping(value = "/onlineCountRecord")
    public Result<?> onlineCountRecord() {
        List<DeviceOnlineCountRecord> deviceOnlineCountRecords = onlineCountRecordService.viewHis();
        return Result.OK(deviceOnlineCountRecords);
    }

    /**
     * 通用校验方法
     *
     * @param baseDevicePage  设备信息页面对象
     * @param currentDeviceId 当前设备ID，如果是新增操作则传 null
     * @return 校验结果
     */
    private Result<?> validateDevice(BaseDevicePage baseDevicePage, String currentDeviceId) {
        if (DeviceTypeEnum.GATE.getType().equals(baseDevicePage.getDeviceType())) {
            List<DeviceGate> deviceGateList = baseDevicePage.getDeviceGateList();
            if (CollectionUtil.isEmpty(deviceGateList)) {
                return Result.error("闸机明细信息不可为空！");
            } else {
                List<String> serialNumbers = deviceGateList.stream()
                        .map(DeviceGate::getEquipmentSerial)
                        .collect(Collectors.toList());
                if (CollectionUtil.isEmpty(serialNumbers)) {
                    return Result.error("闸机序列号不可为空！");
                } else {
                    if (deviceGateList.size() != serialNumbers.size()) {
                        return Result.error("闸机明细序列号未填写完整！");
                    }
                }
            }
        } else {
            if (StrUtil.isEmpty(baseDevicePage.getEquipmentSerial())) {
                return Result.error("监控设备序列号不可为空！");
            }
        }
        // 校验设备名称和类型是否重复
        if (isDeviceNameAndTypeDuplicated(baseDevicePage, currentDeviceId)) {
            return Result.error("名称重复！");
        }

        // 校验设备序列号是否重复
        String duplicateSerials = checkDuplicateSerialNumbers(baseDevicePage, currentDeviceId);
        if (duplicateSerials != null) {
            return Result.error(duplicateSerials);
        }

        return Result.OK();
    }

    private boolean isDeviceNameAndTypeDuplicated(BaseDevicePage baseDevicePage, String currentDeviceId) {
        LambdaQueryWrapper<DeviceInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceInfo::getName, baseDevicePage.getName());
        queryWrapper.eq(DeviceInfo::getDeviceType, baseDevicePage.getDeviceType());
        if (currentDeviceId != null) {
            queryWrapper.ne(DeviceInfo::getId, currentDeviceId); // 排除当前设备
        }
        List<DeviceInfo> list = baseDeviceService.list(queryWrapper);
        return CollectionUtil.isNotEmpty(list);
    }

    private String checkDuplicateSerialNumbers(BaseDevicePage baseDevicePage, String currentDeviceId) {
        if (DeviceTypeEnum.GATE.getType().equals(baseDevicePage.getDeviceType())) {
            return checkGateDeviceSerialNumbers(baseDevicePage.getDeviceGateList(), currentDeviceId);
        } else {
            return checkMonitorDeviceSerialNumbers(baseDevicePage, currentDeviceId);
        }
    }

    private String checkGateDeviceSerialNumbers(List<DeviceGate> baseDeviceGateList, String currentDeviceId) {
        if (CollectionUtil.isEmpty(baseDeviceGateList)) {
            return "闸机设备信息不可为空！";
        }

        List<String> serialNumbers = baseDeviceGateList.stream()
                .map(DeviceGate::getEquipmentSerial)
                .collect(Collectors.toList());

        LambdaQueryWrapper<DeviceGate> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(DeviceGate::getEquipmentSerial, serialNumbers);
        if (currentDeviceId != null) {
            lambdaQueryWrapper.ne(DeviceGate::getBaseId, currentDeviceId); // 排除当前设备的序列号
        }

        List<DeviceGate> existingDevices = baseDeviceGateService.list(lambdaQueryWrapper);
        if (CollectionUtil.isNotEmpty(existingDevices)) {
            List<String> duplicateSerials = existingDevices.stream()
                    .map(DeviceGate::getEquipmentSerial)
                    .collect(Collectors.toList());
            return "设备序列号：" + ArrayUtil.join(duplicateSerials, ",") + "重复！";
        }

        return null;
    }

    private String checkMonitorDeviceSerialNumbers(BaseDevicePage baseDevicePage, String currentDeviceId) {
        if (StrUtil.isEmpty(baseDevicePage.getEquipmentSerial())) {
            return "设备序列号不可为空！";
        }

        LambdaQueryWrapper<DeviceInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceInfo::getEquipmentSerial, baseDevicePage.getEquipmentSerial());
        queryWrapper.eq(DeviceInfo::getDeviceType, baseDevicePage.getDeviceType());
        if (currentDeviceId != null) {
            queryWrapper.ne(DeviceInfo::getId, currentDeviceId); // 排除当前设备
        }
        List<DeviceInfo> list = baseDeviceService.list(queryWrapper);
        if (CollectionUtil.isNotEmpty(list)) {
            List<String> duplicateSerials = list.stream()
                    .map(DeviceInfo::getEquipmentSerial)
                    .collect(Collectors.toList());
            return "设备序列号：" + ArrayUtil.join(duplicateSerials, ",") + "重复！";
        }
        return null;
    }
}
