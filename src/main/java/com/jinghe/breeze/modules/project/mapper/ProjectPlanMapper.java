package com.jinghe.breeze.modules.project.mapper;

import org.apache.ibatis.annotations.Param;
import com.jinghe.breeze.modules.project.entity.ProjectPlan;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: project_plan
 * @Author: jeecg-boot
 * @Date:   2024-04-17
 * @Version: V1.0
 */
public interface ProjectPlanMapper extends BaseMapper<ProjectPlan> {

	/**
	 * 编辑节点状态
	 * @param id
	 * @param status
	 */
	void updateTreeNodeStatus(@Param("id") String id,@Param("status") String status);

}
