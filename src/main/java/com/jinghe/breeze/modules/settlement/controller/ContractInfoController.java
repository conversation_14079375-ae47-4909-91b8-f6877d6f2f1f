package com.jinghe.breeze.modules.settlement.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.modules.settlement.entity.ContractInfo;
import com.jinghe.breeze.modules.settlement.entity.ContractInfoDetail;
import com.jinghe.breeze.modules.settlement.entity.Settlement;
import com.jinghe.breeze.modules.settlement.service.IContractInfoService;
import com.jinghe.breeze.modules.settlement.service.ISettlementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @Description: contract_info
 * @Author: jeecg-boot
 * @Date: 2024-05-31
 * @Version: V1.0
 */
@Api(tags = "contract_info")
@RestController
@RequestMapping("/settlement/contractInfo")
@Slf4j
public class ContractInfoController extends JeecgController<ContractInfo, IContractInfoService> {
    @Autowired
    private IContractInfoService contractInfoService;

    @Autowired
    private ISettlementService settlementService;

    /**
     * 分页列表查询
     *
     * @param contractInfo
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "contract_info-分页列表查询")
    @ApiOperation(value = "contract_info-分页列表查询", notes = "contract_info-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(ContractInfo contractInfo,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<ContractInfo> queryWrapper = QueryGenerator.initQueryWrapper(contractInfo, req.getParameterMap());
        Page<ContractInfo> page = new Page<ContractInfo>(pageNo, pageSize);
        IPage<ContractInfo> pageList = contractInfoService.page(page, queryWrapper);
        pageList.getRecords().forEach(info -> {
            info.setSettledMoney(contractInfoService.getSettledMoney(info.getId()));
        });
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param contractInfo
     * @return
     */
    @AutoLog(value = "contract_info-添加")
    @ApiOperation(value = "contract_info-添加", notes = "contract_info-添加")
    @RequiresPermissions("contractInfo:add")
    @PostMapping(value = "/addOrEdit")
    public Result<?> add(@Validated @RequestBody ContractInfo contractInfo) {
        return contractInfoService.addOrEdit(contractInfo);
    }

    /**
     * 编辑
     *
     * @param contractInfo
     * @return
     */
    @AutoLog(value = "contract_info-编辑")
    @ApiOperation(value = "contract_info-编辑", notes = "contract_info-编辑")
    @RequiresPermissions("contractInfo:edit")
    @PutMapping(value = "/edit")
    public Result<?> edit(@Validated @RequestBody ContractInfo contractInfo) {
        contractInfoService.updateById(contractInfo);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "contract_info-通过id删除")
    @ApiOperation(value = "contract_info-通过id删除", notes = "contract_info-通过id删除")
    @RequiresPermissions("contractInfo:delete")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        return contractInfoService.removeWithDetailsById(id);

    }


    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "contract_info-通过id查询")
    @ApiOperation(value = "contract_info-通过id查询", notes = "contract_info-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        ContractInfo contractInfo = contractInfoService.getById(id);
        if (contractInfo == null) {
            return Result.error("未找到对应数据");
        }
        List<ContractInfoDetail> details = contractInfoService.getDetails(id);
        contractInfo.setChildren(details);
        return Result.OK(contractInfo);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param contractInfo
     */
    @RequiresPermissions("contractInfo:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ContractInfo contractInfo) {
        return super.exportXls(request, contractInfo, ContractInfo.class, "contract_info");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("contractInfo:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ContractInfo.class);
    }

    @AutoLog(value = "结算信息")
    @GetMapping(value = "/getSettledContractList")
    public Result<?> getSettledContractList() {
        Map<String, Object> result = new HashMap<>();

        try {
            LambdaQueryWrapper<ContractInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ContractInfo::getDelFlag, Common.delete_flag.OK);
            // 合同总额
            List<ContractInfo> contractInfoList = contractInfoService.list(queryWrapper);
            BigDecimal totalContract = contractInfoList.stream()
                    .map(ContractInfo::getTotalMoney)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 累计结算
            LambdaQueryWrapper<Settlement> settleWrapper = new LambdaQueryWrapper<>();
            settleWrapper.eq(Settlement::getDelFlag,Common.delete_flag.OK);
            settleWrapper.groupBy(Settlement::getBatch);
            List<Settlement> settlementList = settlementService.list(settleWrapper);
            BigDecimal settlementMoney = settlementList.stream()
                    .map(Settlement::getPayMoney)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            result.put("totalContract", totalContract);
            result.put("settlementMoney", settlementMoney);

            BigDecimal settlementPercent = totalContract.compareTo(BigDecimal.ZERO) == 0
                    ? BigDecimal.ZERO
                    : settlementMoney.multiply(new BigDecimal(100)).divide(totalContract, 2, RoundingMode.HALF_UP);

            result.put("settlementPercent", settlementPercent);
            return Result.OK(result);

        } catch (Exception e) {
            // 返回错误响应
            return Result.error("获取结算信息失败: " + e.getMessage());
        }
    }

}
