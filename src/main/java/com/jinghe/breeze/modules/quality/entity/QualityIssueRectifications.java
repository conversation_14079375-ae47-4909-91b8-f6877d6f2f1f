package com.jinghe.breeze.modules.quality.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.*;

/**
 * @Description: 质量整改
 * @Author: jeecg-boot
 * @Date: 2025-06-21
 * @Version: V1.0
 */
@Data
@TableName("quality_issue_rectifications")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "quality_issue_rectifications对象", description = "质量整改")
public class QualityIssueRectifications implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 创建人
     */

    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */

    @ApiModelProperty(value = "修改人")
    private String updateBy;
    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**
     * delFlag
     */
    @Excel(name = "delFlag", width = 15)

    @ApiModelProperty(value = "delFlag")
    private Integer delFlag;
    /**
     * 组织机构编码
     */

    @ApiModelProperty(value = "组织机构编码")
    private String sysOrgCode;
    /**
     * 检查记录ID
     */
    @Excel(name = "检查记录ID", width = 15)

    @ApiModelProperty(value = "检查记录ID")
    private String recordId;
    /**
     * 整改单号
     */
    @Excel(name = "整改单号", width = 15)
    @ApiModelProperty(value = "整改单号")
    private String code;
    /**
     * 整改工区
     */
    @Excel(name = "整改工区", width = 15)
    @ApiModelProperty(value = "整改工区")
    private String workArea;
    /**
     * 整改部位
     */
    @Dict(dictTable = "quality_catalogue_tree", dicText = "name", dicCode = "id")
    @Excel(name = "整改部位", width = 15)
    @ApiModelProperty(value = "整改部位")
    private String rectificationPart;
    /**
     * 整改内容
     */
    @Excel(name = "整改内容", width = 15)
    @ApiModelProperty(value = "整改内容")
    private String rectificationContent;
    /**
     * 影像资料
     */
    @Excel(name = "影像资料", width = 15)
    @ApiModelProperty(value = "影像资料")
    private String mediaFiles;
    /**
     * 整改建议
     */
    @Excel(name = "整改建议", width = 15)
    @ApiModelProperty(value = "整改建议")
    private String rectificationSuggestions;
    /**
     * 质量问题审核人
     */
    @Excel(name = "质量问题审核人", width = 15)
    @ApiModelProperty(value = "质量问题审核人")
    private String reviewer;
    /**
     * 整改情况
     */
    @Excel(name = "整改情况", width = 15)
    @ApiModelProperty(value = "整改情况")
    private String rectificationSituation;
    /**
     * 整改措施
     */
    @Excel(name = "整改措施", width = 15)
    @ApiModelProperty(value = "整改措施")
    private String rectificationMeasure;
    /**
     * 整改期限
     */
    @Excel(name = "整改期限", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "整改期限")
    private Date rectificationDeadline;

    /**
     * 整改期限
     */
    @Excel(name = "整改完成时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "整改完成时间")
    private Date rectificationCompleteDate;
    /**
     * 整改状态
     */
    @Dict(dicCode = "rectification_status")
    @Excel(name = "整改状态", width = 15)
    @ApiModelProperty(value = "整改状态")
    private String rectificationStatus;
    /**
     * 审批状态
     */
    @Excel(name = "审批状态", width = 15)
    @ApiModelProperty(value = "审批状态")
    private String bpmStatus;
    /**
     * 上报人
     */
    @Excel(name = "上报人", width = 15)
    @ApiModelProperty(value = "上报人")
    private String reporter;
    /**
     * 上报人名称
     */
    @Excel(name = "上报人名称", width = 15)
    @ApiModelProperty(value = "上报人名称")
    private String reporterName;
    /**
     * 上报人电话
     */
    @Excel(name = "上报人电话", width = 15)
    @ApiModelProperty(value = "上报人电话")
    private String reporterPhone;
    /**
     * 上报人部门
     */
    @Excel(name = "上报人部门", width = 15)
    @ApiModelProperty(value = "上报人部门")
    private String reporterDepart;
    /**
     * 上报日期
     */
    @Excel(name = "上报日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "上报日期")
    private Date reportDate;
    /**
     * 整改图片
     */
    @Excel(name = "整改图片", width = 15)
    @ApiModelProperty(value = "整改图片")
    private String reportFiles;


}
