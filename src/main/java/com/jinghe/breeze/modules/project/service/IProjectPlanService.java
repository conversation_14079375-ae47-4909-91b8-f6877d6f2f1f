package com.jinghe.breeze.modules.project.service;

import com.jinghe.breeze.modules.project.entity.ProjectPlan;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghe.breeze.modules.project.entity.ProjectPlanExcelData;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import java.util.List;

/**
 * @Description: project_plan
 * @Author: jeecg-boot
 * @Date: 2024-04-17
 * @Version: V1.0
 */
public interface IProjectPlanService extends IService<ProjectPlan> {

    /**
     * 根节点父ID的值
     */
    public static final String ROOT_PID_VALUE = "0";

    /**
     * 树节点有子节点状态值
     */
    public static final String HASCHILD = "1";

    /**
     * 树节点无子节点状态值
     */
    public static final String NOCHILD = "0";

    /**
     * 新增节点
     */
    void addProjectPlan(ProjectPlan projectPlan);

    /**
     * 修改节点
     */
    void updateProjectPlan(ProjectPlan projectPlan) throws JeecgBootException;

    /**
     * 删除节点
     */
    void deleteProjectPlan(String id) throws JeecgBootException;

    /**
     * 查询所有数据，无分页
     */
    List<ProjectPlan> queryTreeListNoPage(QueryWrapper<ProjectPlan> queryWrapper);


    ProjectPlan buildTree(ProjectPlan plan, List<ProjectPlan> planList);

    void buildTreeWithoutParent(List<ProjectPlan> list, String rootPidValue);

    void processSetHasChild(List<ProjectPlan> projectPlanList);

    void importExcelData(List<ProjectPlanExcelData> projectPlanExcelDataList, Integer version);

    Result<?> queryList(Integer version);

    Result<?> version();
}
