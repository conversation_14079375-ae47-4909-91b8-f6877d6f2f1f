package com.jinghe.breeze.modules.device.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghe.breeze.modules.device.entity.DeviceGate;

import java.util.List;

/**
 * @Description: 闸机设备信息
 * @Author: jeecg-boot
 * @Date: 2024-08-13
 * @Version: V1.0
 */
public interface IDeviceGateService extends IService<DeviceGate> {

    public List<DeviceGate> selectByMainId(String mainId);
}
