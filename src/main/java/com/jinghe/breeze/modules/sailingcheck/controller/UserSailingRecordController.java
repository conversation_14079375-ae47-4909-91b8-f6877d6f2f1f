package com.jinghe.breeze.modules.sailingcheck.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.modules.person.entity.PersonInfo;
import com.jinghe.breeze.modules.person.service.IPersonInfoService;
import org.apache.shiro.authz.annotation.RequiresPermissions;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import com.jinghe.breeze.modules.sailingcheck.entity.UserSailingRecord;
import com.jinghe.breeze.modules.sailingcheck.service.IUserSailingRecordService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: user_sailing_record
 * @Author: jeecg-boot
 * @Date: 2024-06-18
 * @Version: V1.0
 */
@Api(tags = "出海记录")
@RestController
@RequestMapping("/sailingcheck/userSailingRecord")
@Slf4j
public class UserSailingRecordController extends JeecgController<UserSailingRecord, IUserSailingRecordService> {
    @Autowired
    private IUserSailingRecordService userSailingRecordService;
    @Autowired
    private IPersonInfoService personInfoService;

    final List<Integer> result = Arrays.asList(1, 2, 3, 4);
    final List<Integer> status = Arrays.asList(1, 3);

    /**
     * 分页列表查询
     *
     * @param userSailingRecord
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "分页列表查询")
    @ApiOperation(value = "分页列表查询", notes = "分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(UserSailingRecord userSailingRecord,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
//        QueryWrapper<UserSailingRecord> queryWrapper = QueryGenerator.initQueryWrapper(userSailingRecord, req.getParameterMap());
        QueryWrapper<UserSailingRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("DISTINCT user_id,real_name,department_name")
                .lambda().in(UserSailingRecord::getStatus,status)
                .orderByDesc(UserSailingRecord::getCreateTime);
        Page<UserSailingRecord> page = new Page<UserSailingRecord>(pageNo, pageSize);
        IPage<UserSailingRecord> pageList = userSailingRecordService.page(page, queryWrapper);
        if (pageList.getRecords().size() > 0) {
            List<String> collect = pageList.getRecords().stream().map(x -> x.getUserId()).collect(Collectors.toList());
            LambdaQueryWrapper<PersonInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.in(PersonInfo::getUserId, collect);
            List<PersonInfo> personInfos = personInfoService.list(lambdaQueryWrapper);
            for (UserSailingRecord record : pageList.getRecords()) {
                Optional<PersonInfo> first = personInfos.stream().filter(x -> x.getUserId().equals(record.getUserId())).findFirst();
                if (first.isPresent()) {
                    record.setPhone(first.get().getPhone());
                }
            }
        }
        return Result.OK(pageList);
    }


    /**
     * 分页列表查询
     *
     * @param
     * @param pageNo
     * @param pageSize
     * @param
     * @return
     */
    @AutoLog(value = "查询异常信息")
    @ApiOperation(value = "查询异常信息", notes = "查询异常信息")
    @GetMapping(value = "/exceptionList")
    public Result<?> exceptionList(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
//        Page<UserSailingRecord> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<UserSailingRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(UserSailingRecord::getResult, result);
        queryWrapper.orderByDesc(UserSailingRecord::getUpdateTime);
        List<UserSailingRecord> list = userSailingRecordService.list(queryWrapper);
        if (list.size() > 0) {
            List<String> collect = list.stream().map(x -> x.getUserId()).collect(Collectors.toList());
            LambdaQueryWrapper<PersonInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.in(PersonInfo::getUserId, collect);
            List<PersonInfo> personInfos = personInfoService.list(lambdaQueryWrapper);
            for (UserSailingRecord record : list) {
                Optional<PersonInfo> first = personInfos.stream().filter(x -> x.getUserId().equals(record.getUserId())).findFirst();
                if (first.isPresent()) {
                    record.setPhone(first.get().getPhone());
                }
            }
        }
        return Result.OK(list);
    }


    /**
     * 添加
     *
     * @param userSailingRecord
     * @return
     */
    @AutoLog(value = "添加")
    @ApiOperation(value = "添加", notes = "添加")
    @PostMapping(value = "/add")
    public Result<?> add(@Validated @RequestBody UserSailingRecord userSailingRecord) {
        userSailingRecordService.save(userSailingRecord);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param userSailingRecord
     * @return
     */
    @AutoLog(value = "编辑")
    @ApiOperation(value = "编辑", notes = "编辑")
    @RequiresPermissions("userSailingRecord:edit")
    @PutMapping(value = "/edit")
    public Result<?> edit(@Validated @RequestBody UserSailingRecord userSailingRecord) {
        userSailingRecordService.updateById(userSailingRecord);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "通过id删除")
    @ApiOperation(value = "通过id删除", notes = "通过id删除")
    @RequiresPermissions("userSailingRecord:delete")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        userSailingRecordService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "user_sailing_record-批量删除")
    @ApiOperation(value = "user_sailing_record-批量删除", notes = "user_sailing_record-批量删除")
    @RequiresPermissions("userSailingRecord:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.userSailingRecordService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "user_sailing_record-通过id查询")
    @ApiOperation(value = "user_sailing_record-通过id查询", notes = "user_sailing_record-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        UserSailingRecord userSailingRecord = userSailingRecordService.getById(id);
        if (userSailingRecord == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(userSailingRecord);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param userSailingRecord
     */
    @RequiresPermissions("userSailingRecord:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, UserSailingRecord userSailingRecord) {
        return super.exportXls(request, userSailingRecord, UserSailingRecord.class, "user_sailing_record");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("userSailingRecord:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, UserSailingRecord.class);
    }




    /**
     * @param id
     * @param type 1人员 2船舶
     * @return
     */
    @AutoLog(value = "单位信息")
    @ApiOperation(value = "单位信息", notes = "单位信息")
    @GetMapping(value = "/unitInfo")
    public Result<?> unitInfo(@RequestParam(name = "id") String id,
                              @RequestParam(name = "type") int type) {
        return userSailingRecordService.unitInfo(id, type);
    }


}
