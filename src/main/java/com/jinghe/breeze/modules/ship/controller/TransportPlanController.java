package com.jinghe.breeze.modules.ship.controller;

import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.io.IOException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.jinghe.breeze.common.constants.Common;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import com.jinghe.breeze.modules.ship.entity.TransportPlan;
import com.jinghe.breeze.modules.ship.service.ITransportPlanService;

import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: transport_plan
 * @Author: jeecg-boot
 * @Date:   2024-06-06
 * @Version: V1.0
 */
@Api(tags="transport_plan")
@RestController
@RequestMapping("/ship/transportPlan")
@Slf4j
public class TransportPlanController extends JeecgController<TransportPlan, ITransportPlanService> {
	@Autowired
	private ITransportPlanService transportPlanService;

	 /**
	  * 进行中运单
	  *
	  * @param
	  * @return
	  */
	 @AutoLog(value = "进行中运单")
	 @ApiOperation(value="进行中运单", notes="进行中运单")
	 @GetMapping(value = "/underwayList")
	 public Result<?> underwayList() {
		 return transportPlanService.getUnderwayList();
	 }

	 /**
	  * 运单详细信息
	  *
	  * @param
	  * @return
	  */
	 @AutoLog(value = "运单详细信息")
	 @ApiOperation(value="运单详细信息", notes="运单详细信息")
	 @GetMapping(value = "/orderDetail")
	 public Result<?> orderDetail(@RequestParam(name="order") String order) throws IOException, NoSuchAlgorithmException {
		 return transportPlanService.getOrderDetail(order);
	 }
	
	/**
	 * 分页列表查询
	 *
	 * @param
	 * @param pageNo
	 * @param pageSize
	 * @param
	 * @return
	 */
	@AutoLog(value = "transport_plan-分页列表查询")
	@ApiOperation(value="transport_plan-分页列表查询", notes="transport_plan-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                   @RequestParam(name="transportCode",required = false) String transportCode,
								   @RequestParam(name="status",required = false) String status) {

		return transportPlanService.getList(transportCode,pageNo,pageSize,status);
	}
	
	/**
	 *   添加
	 *
	 * @param transportPlan
	 * @return
	 */
	@AutoLog(value = "transport_plan-添加")
	@ApiOperation(value="transport_plan-添加", notes="transport_plan-添加")
	@RequiresPermissions("transportPlan:add")
	@PostMapping(value = "/add")
	public Result<?> add(@Validated @RequestBody TransportPlan transportPlan) {
		transportPlan.setStatus(Common.transportPlan.UNDERWAY);
		transportPlanService.save(transportPlan);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param transportPlan
	 * @return
	 */
	@AutoLog(value = "transport_plan-编辑")
	@ApiOperation(value="transport_plan-编辑", notes="transport_plan-编辑")
	@RequiresPermissions("transportPlan:edit")
	@PutMapping(value = "/edit")
	public Result<?> edit(@Validated @RequestBody TransportPlan transportPlan) {
		transportPlanService.updateById(transportPlan);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "transport_plan-通过id删除")
	@ApiOperation(value="transport_plan-通过id删除", notes="transport_plan-通过id删除")
	@RequiresPermissions("transportPlan:delete")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		transportPlanService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "transport_plan-批量删除")
	@ApiOperation(value="transport_plan-批量删除", notes="transport_plan-批量删除")
	@RequiresPermissions("transportPlan:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.transportPlanService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "transport_plan-通过id查询")
	@ApiOperation(value="transport_plan-通过id查询", notes="transport_plan-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		TransportPlan transportPlan = transportPlanService.getById(id);
		if(transportPlan==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(transportPlan);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param transportPlan
    */
    @RequiresPermissions("transportPlan:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, TransportPlan transportPlan) {
        return super.exportXls(request, transportPlan, TransportPlan.class, "transport_plan");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("transportPlan:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, TransportPlan.class);
    }

}
