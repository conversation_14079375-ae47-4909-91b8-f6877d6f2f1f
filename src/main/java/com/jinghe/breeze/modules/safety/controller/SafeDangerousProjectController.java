package com.jinghe.breeze.modules.safety.controller;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.jinghe.breeze.modules.safety.entity.SafeDangerousProject;
import com.jinghe.breeze.modules.safety.service.ISafeDangerousProjectService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 危大工程
 * @Author: jeecg-boot
 * @Date:   2025-06-24
 * @Version: V1.0
 */
@Api(tags="危大工程")
@RestController
@RequestMapping("/safety/safeDangerousProject")
@Slf4j
public class SafeDangerousProjectController extends JeecgController<SafeDangerousProject, ISafeDangerousProjectService> {
	@Autowired
	private ISafeDangerousProjectService safeDangerousProjectService;
	
	/**
	 * 分页列表查询
	 *
	 * @param safeDangerousProject
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "危大工程-分页列表查询")
	@ApiOperation(value="危大工程-分页列表查询", notes="危大工程-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(SafeDangerousProject safeDangerousProject,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<SafeDangerousProject> queryWrapper = QueryGenerator.initQueryWrapper(safeDangerousProject, req.getParameterMap());
		
		// 处理施工负责人模糊查询
		if(oConvertUtils.isNotEmpty(safeDangerousProject.getConstructionLeader())) {
			String constructionLeader = safeDangerousProject.getConstructionLeader();
			// 创建新的查询条件
			queryWrapper = new QueryWrapper<SafeDangerousProject>();
			// 添加子查询，通过用户名模糊查询获取用户ID
			queryWrapper.inSql("construction_leader", 
				"SELECT id FROM person_info WHERE name LIKE '%" + constructionLeader + "%'");
		}
		
		if (!ObjectUtils.isEmpty(safeDangerousProject.getStartTime())){
			queryWrapper.between("start_date",safeDangerousProject.getStartTime(),safeDangerousProject.getEndTime());
		}
		Page<SafeDangerousProject> page = new Page<SafeDangerousProject>(pageNo, pageSize);
		IPage<SafeDangerousProject> pageList = safeDangerousProjectService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param safeDangerousProject
	 * @return
	 */
	@AutoLog(value = "危大工程-添加")
	@ApiOperation(value="危大工程-添加", notes="危大工程-添加")
	@RequiresPermissions("safeDangerousProject:add")
	@PostMapping(value = "/add")
	public Result<?> add(@Validated @RequestBody SafeDangerousProject safeDangerousProject) {
		safeDangerousProjectService.save(safeDangerousProject);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param safeDangerousProject
	 * @return
	 */
	@AutoLog(value = "危大工程-编辑")
	@ApiOperation(value="危大工程-编辑", notes="危大工程-编辑")
	@RequiresPermissions("safeDangerousProject:edit")
	@PutMapping(value = "/edit")
	public Result<?> edit(@Validated @RequestBody SafeDangerousProject safeDangerousProject) {
		safeDangerousProjectService.updateById(safeDangerousProject);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "危大工程-通过id删除")
	@ApiOperation(value="危大工程-通过id删除", notes="危大工程-通过id删除")
	@RequiresPermissions("safeDangerousProject:delete")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		safeDangerousProjectService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "危大工程-批量删除")
	@ApiOperation(value="危大工程-批量删除", notes="危大工程-批量删除")
	@RequiresPermissions("safeDangerousProject:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.safeDangerousProjectService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "危大工程-通过id查询")
	@ApiOperation(value="危大工程-通过id查询", notes="危大工程-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		SafeDangerousProject safeDangerousProject = safeDangerousProjectService.getById(id);
		if(safeDangerousProject==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(safeDangerousProject);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param safeDangerousProject
    */
    @RequiresPermissions("safeDangerousProject:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SafeDangerousProject safeDangerousProject) {
        return super.exportXls(request, safeDangerousProject, SafeDangerousProject.class, "危大工程");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("safeDangerousProject:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SafeDangerousProject.class);
    }

}
