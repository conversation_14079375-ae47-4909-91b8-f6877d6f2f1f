package com.jinghe.breeze.modules.ship.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.modules.ship.entity.ShipInfo;
import com.jinghe.breeze.modules.ship.entity.ShipOutgoing;
import com.jinghe.breeze.modules.ship.mapper.ShipOutgoingMapper;
import com.jinghe.breeze.modules.ship.service.IShipInfoService;
import com.jinghe.breeze.modules.ship.service.IShipOutgoingService;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Objects;

/**
 * @Description: ship_outgoing
 * @Author: jeecg-boot
 * @Date: 2024-04-23
 * @Version: V1.0
 */
@Service
public class ShipOutgoingServiceImpl extends ServiceImpl<ShipOutgoingMapper, ShipOutgoing> implements IShipOutgoingService {
    @Autowired
    private IShipInfoService shipInfoService;

    public Result<?> add(ShipOutgoing shipOutgoing) {
        //船舶的进退场状态只根据最后一条记录获取
        //该船舶目前为“离场”状态，无法再次退场 ,如果没有记录,则不允许退场
        //退场 时,单位名设置为 '-'  进场0  离场1
        ShipInfo shipInfo = shipInfoService.getById(shipOutgoing.getShipDataId());
        if (shipInfo == null) {
            return Result.error("船舶信息数据不存在");
        }
        //应当确保填报的日期 是最后的日期
        LambdaQueryWrapper<ShipOutgoing> wrapper = new LambdaQueryWrapper<>();
        wrapper.gt(ShipOutgoing::getDate, shipOutgoing.getDate());
        if (baseMapper.selectCount(wrapper) != 0) {
            return Result.error("存在该日期之后的进退场记录，请重新确认进/退场日期");
        }

        LambdaQueryWrapper<ShipOutgoing> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShipOutgoing::getShipDataId, shipInfo.getId());
        queryWrapper.orderByDesc(ShipOutgoing::getDate).orderByDesc(ShipOutgoing::getCreateTime).last("LIMIT 1");
        ShipOutgoing lastShipOutgoing = baseMapper.selectList(queryWrapper).stream().findFirst().orElse(null);
        if (lastShipOutgoing == null) { //没有记录,此时不允许设置为退场
            if (Objects.equals(shipOutgoing.getIsOut(), "1")) {
                return Result.error("该船只无进退场记录，无法退场");
            } else {
                shipOutgoing.setTypeValue(shipInfo.getTypeValue());
                shipOutgoing.setMmsi(shipInfo.getMmsi());
                shipOutgoing.setShipownerName(shipInfo.getShipOwner());
                baseMapper.insert(shipOutgoing);
                return Result.OK("添加成功！");
            }
        }
        String currentStatus = shipOutgoing.getIsOut();   //将要增加的 状态
        if (currentStatus == null) {
            return Result.error("请选择进场或退场");
        }
        if (currentStatus.equals(lastShipOutgoing.getIsOut())) {
            if (currentStatus.equals("1")) {
                return Result.error("该船只目前为“退场”状态，无法再退场");
            } else {
                return Result.error("该船只目前为“进场”状态，无法再次进场");
            }
        }
        if (currentStatus.equals("1")) { //设置为退场 则将单位置 '-'
            shipOutgoing.setUnit("-");
        }
        shipOutgoing.setTypeValue(shipInfo.getTypeValue());
        shipOutgoing.setMmsi(shipInfo.getMmsi());
        shipOutgoing.setShipownerName(shipInfo.getShipOwner());
        baseMapper.insert(shipOutgoing);
        return Result.OK("添加成功！");

    }


}
