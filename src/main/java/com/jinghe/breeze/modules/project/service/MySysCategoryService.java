package com.jinghe.breeze.modules.project.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.modules.project.entity.MySysCategory;
import jodd.util.StringUtil;
import org.jeecg.modules.system.entity.SysCategory;
import org.jeecg.modules.system.mapper.SysCategoryMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service

public class MySysCategoryService {
    @Autowired
    private SysCategoryMapper categoryMapper;

    public MySysCategoryService() {

    }

    public List<MySysCategory> getAllCategories() {
        List<SysCategory> allCategories = categoryMapper.selectList(new LambdaQueryWrapper<SysCategory>());
        List<MySysCategory> mySysCategories = allCategories.stream().map(this::convert).collect(Collectors.toList());
        return mySysCategories.stream()
                .filter(c -> c.getPid().equals("0"))
                .map(c -> buildTree(c, mySysCategories))
                .collect(Collectors.toList());
    }

    private MySysCategory convert(SysCategory category) {
        MySysCategory mySysCategory = new MySysCategory();

        BeanUtils.copyProperties(category, mySysCategory);
        return mySysCategory;
    }

    private MySysCategory buildTree(MySysCategory category, List<MySysCategory> allCategories) {
        List<MySysCategory> children = allCategories.stream()
                .filter(c -> c.getPid().equals(category.getId()))
                .map(c -> buildTree(c, allCategories))
                .collect(Collectors.toList());
        category.setChildren(children);
        return category;
    }

    // 用于将系统设置中的分类字典树,构建成树,去掉根节点.
    private List<MySysCategory> buildTreeWithoutParent(MySysCategory category, List<MySysCategory> allCategories) {
        return allCategories.stream()
                .filter(c -> c.getPid().equals(category.getId()))
                .map(c -> buildTree(c, allCategories))
                .collect(Collectors.toList());
    }

    public List<MySysCategory> getAllCategoriesByCode(String code) {
        List<SysCategory> categories = categoryMapper.selectList(new LambdaQueryWrapper<SysCategory>());
        List<MySysCategory> allCategories = categories.stream().map(this::convert).collect(Collectors.toList());
        // 筛选出与给定code匹配的顶级元素
        List<MySysCategory> topLevelCategories = allCategories.stream()
                .filter(c -> c.getCode() != null && c.getCode().equals(code))
                .collect(Collectors.toList());

        // 对每个顶级元素，找到它的直接子节点
        return topLevelCategories.stream()
                .flatMap(root -> {
                    List<MySysCategory> children = buildTreeWithoutParent(root, allCategories);
                    return children != null ? children.stream() : Stream.empty();
                })
                .collect(Collectors.toList());
    }


    private void getLeafCatogory(List<MySysCategory> lis, List<MySysCategory> leafList) {
        for (MySysCategory c : lis) {
            if (c.getChildren() != null && c.getChildren().size() > 0) {
                getLeafCatogory(c.getChildren(), leafList);
            } else {
                leafList.add(c);
            }
        }
    }

    /**
     * @param code code为顶级 如 B04
     * @return
     */
    public List<MySysCategory> getNodesWithoutChildren(String code) {
        List<MySysCategory> mySysCategoriesList = getAllCategoriesByCode(code);//根据code获取属性结构
        List<MySysCategory> leafList = new ArrayList<>(); //用来存放末端节点
        getLeafCatogory(mySysCategoriesList, leafList); //获取到末端节点
        return leafList;
    }

    public List<MySysCategory> getChildBatch(String pid) {
        List<SysCategory> categories = categoryMapper.selectList(new LambdaQueryWrapper<SysCategory>()
                .eq(SysCategory::getPid, pid));
        return categories.stream().map(this::convert).collect(Collectors.toList());
    }

    public List<MySysCategory> gueryTreeList(String code, String pid) {
        List<MySysCategory> mySysCategoriesList = new ArrayList<>();
        if (StringUtil.isEmpty(code)) {
            return mySysCategoriesList;
        }
        if (StringUtil.isEmpty(pid)) {
            mySysCategoriesList = getAllCategoriesByCode(code);
            return mySysCategoriesList;
        }
        if ("0".equals(pid)) {
            List<SysCategory> categories = categoryMapper.selectList(new LambdaQueryWrapper<SysCategory>()
                    .eq(SysCategory::getCode, code));
            return categories.stream().map(this::convert).collect(Collectors.toList());

        }
        List<String> pidList = Arrays.asList(pid.split(","));
        pidList = pidList.stream().filter(s -> !s.trim().isEmpty()).collect(Collectors.toList());
        if (pidList.isEmpty()) {
            return new ArrayList<>(); //没有匹配到任何pid
        }
        List<SysCategory> categories = categoryMapper.selectList(new LambdaQueryWrapper<SysCategory>()
                .in(SysCategory::getPid, pidList));
        return categories.stream().map(this::convert).collect(Collectors.toList());
    }
}
