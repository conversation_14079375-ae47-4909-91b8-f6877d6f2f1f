package com.jinghe.breeze.modules.person.controller;

import java.io.UnsupportedEncodingException;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.jinghe.breeze.modules.person.entity.DictPersonItem;
import com.jinghe.breeze.modules.person.entity.DictPerson;
import com.jinghe.breeze.modules.person.vo.DictPersonPage;
import com.jinghe.breeze.modules.person.service.IDictPersonService;
import com.jinghe.breeze.modules.person.service.IDictPersonItemService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 数据字典-工种表
 * @Author:
 * @Date:   2022-08-31
 * @Version: V1.0
 */
@Api(tags="数据字典-工种表")
@RestController
@RequestMapping("/person/dictPerson")
@Slf4j
public class DictPersonController {
	@Autowired
	private IDictPersonService dictPersonService;
	@Autowired
	private IDictPersonItemService dictPersonItemService;
	
	/**
	 * 分页列表查询
	 *
	 * @param dictPerson
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "数据字典-工种表-分页列表查询")
	@ApiOperation(value="数据字典-工种表-分页列表查询", notes="数据字典-工种表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(DictPerson dictPerson,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<DictPerson> queryWrapper = QueryGenerator.initQueryWrapper(dictPerson, req.getParameterMap());
		Page<DictPerson> page = new Page<DictPerson>(pageNo, pageSize);
		IPage<DictPerson> pageList = dictPersonService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param dictPersonPage
	 * @return
	 */
	@AutoLog(value = "数据字典-工种表-添加")
	@ApiOperation(value="数据字典-工种表-添加", notes="数据字典-工种表-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody DictPersonPage dictPersonPage) {
		DictPerson dictPerson = new DictPerson();
		BeanUtils.copyProperties(dictPersonPage, dictPerson);
		//判断字典编号是否重复
		QueryWrapper<DictPerson>  queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("code", dictPerson.getCode());
		DictPerson one = dictPersonService.getOne(queryWrapper);
		if (one!= null){
			return Result.error("字典编号已存在！");
		}
		dictPersonService.saveMain(dictPerson, dictPersonPage.getDictPersonItemList());
		return Result.OK(dictPerson);
	}
	
	/**
	 *  编辑
	 *
	 * @param dictPersonPage
	 * @return
	 */
	@AutoLog(value = "数据字典-工种表-编辑")
	@ApiOperation(value="数据字典-工种表-编辑", notes="数据字典-工种表-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody DictPersonPage dictPersonPage) {
		DictPerson dictPerson = new DictPerson();
		BeanUtils.copyProperties(dictPersonPage, dictPerson);
		DictPerson dictPersonEntity = dictPersonService.getById(dictPerson.getId());
		if(dictPersonEntity==null) {
			return Result.error("未找到对应数据");
		}
		dictPersonService.updateMain(dictPerson, dictPersonPage.getDictPersonItemList());
		return Result.OK(dictPerson);
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "数据字典-工种表-通过id删除")
	@ApiOperation(value="数据字典-工种表-通过id删除", notes="数据字典-工种表-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		dictPersonService.delMain(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "数据字典-工种表-批量删除")
	@ApiOperation(value="数据字典-工种表-批量删除", notes="数据字典-工种表-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.dictPersonService.delBatchMain(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "数据字典-工种表-通过id查询")
	@ApiOperation(value="数据字典-工种表-通过id查询", notes="数据字典-工种表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		DictPerson dictPerson = dictPersonService.getById(id);
		if(dictPerson==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(dictPerson);

	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "数据字典表-子表通过主表ID查询")
	@ApiOperation(value="数据字典表-子表主表ID查询", notes="数据字典表-子表-通主表ID查询")
	@GetMapping(value = "/queryDictPersonItemByMainId")
	public Result<?> queryDictPersonItemListByMainId(@RequestParam(name="id",required=true) String id) {
		List<DictPersonItem> dictPersonItemList = dictPersonItemService.selectByMainId(id);
		return Result.OK(dictPersonItemList);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param dictPerson
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DictPerson dictPerson) {
      // Step.1 组装查询条件查询数据
      QueryWrapper<DictPerson> queryWrapper = QueryGenerator.initQueryWrapper(dictPerson, request.getParameterMap());
      LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

      //Step.2 获取导出数据
      List<DictPerson> queryList = dictPersonService.list(queryWrapper);
      // 过滤选中数据
      String selections = request.getParameter("selections");
      List<DictPerson> dictPersonList = new ArrayList<DictPerson>();
      if(oConvertUtils.isEmpty(selections)) {
          dictPersonList = queryList;
      }else {
          List<String> selectionList = Arrays.asList(selections.split(","));
          dictPersonList = queryList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
      }

      // Step.3 组装pageList
      List<DictPersonPage> pageList = new ArrayList<DictPersonPage>();
      for (DictPerson main : dictPersonList) {
          DictPersonPage vo = new DictPersonPage();
          BeanUtils.copyProperties(main, vo);
          List<DictPersonItem> dictPersonItemList = dictPersonItemService.selectByMainId(main.getId());
          vo.setDictPersonItemList(dictPersonItemList);
          pageList.add(vo);
      }

      // Step.4 AutoPoi 导出Excel
      ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "数据字典-工种表列表");
      mv.addObject(NormalExcelConstants.CLASS, DictPersonPage.class);
      mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("数据字典-工种表数据", "导出人:"+sysUser.getRealname(), "数据字典-工种表"));
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
      return mv;
    }

    /**
    * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
      Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
      for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
          MultipartFile file = entity.getValue();// 获取上传文件对象
          ImportParams params = new ImportParams();
          params.setTitleRows(2);
          params.setHeadRows(1);
          params.setNeedSave(true);
          try {
              List<DictPersonPage> list = ExcelImportUtil.importExcel(file.getInputStream(), DictPersonPage.class, params);
              for (DictPersonPage page : list) {
                  DictPerson po = new DictPerson();
                  BeanUtils.copyProperties(page, po);
                  dictPersonService.saveMain(po, page.getDictPersonItemList());
              }
              return Result.OK("文件导入成功！数据行数:" + list.size());
          } catch (Exception e) {
              log.error(e.getMessage(),e);
              return Result.error("文件导入失败:"+e.getMessage());
          } finally {
              try {
                  file.getInputStream().close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
          }
      }
      return Result.OK("文件导入失败！");
    }

}
