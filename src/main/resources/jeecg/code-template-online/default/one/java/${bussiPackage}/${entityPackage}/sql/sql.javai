INSERT INTO sys_permission ( id, parent_id, `name`, menu_type, perms, perms_type, sort_no, always_show, is_route, is_leaf, keep_alive, hidden, `status`, del_flag, rule_flag, create_by, create_time, update_by, update_time, internal_or_external, app_show ) VALUES ( REPLACE ( UUID(), '-', '' ), "replace_pid", "新增按钮", 2, "${entityName?uncap_first}:add", 1, 1.00, 0, 1, 1, 0, 0, 1, 0, 0, 'admin', NOW(), 'admin', NOW(), 0, 0 );

INSERT INTO sys_permission ( id, parent_id, `name`, menu_type, perms, perms_type, sort_no, always_show, is_route, is_leaf, keep_alive, hidden, `status`, del_flag, rule_flag, create_by, create_time, update_by, update_time, internal_or_external, app_show ) VALUES ( REPLACE ( UUID(), '-', '' ), "replace_pid", "编辑按钮", 2, "${entityName?uncap_first}:edit", 1, 1.00, 0, 1, 1, 0, 0, 1, 0, 0, 'admin', NOW(), 'admin', NOW(), 0, 0 );

INSERT INTO sys_permission ( id, parent_id, `name`, menu_type, perms, perms_type, sort_no, always_show, is_route, is_leaf, keep_alive, hidden, `status`, del_flag, rule_flag, create_by, create_time, update_by, update_time, internal_or_external, app_show ) VALUES ( REPLACE ( UUID(), '-', '' ), "replace_pid", "删除按钮", 2, "${entityName?uncap_first}:delete", 1, 1.00, 0, 1, 1, 0, 0, 1, 0, 0, 'admin', NOW(), 'admin', NOW(), 0, 0 );

INSERT INTO sys_permission ( id, parent_id, `name`, menu_type, perms, perms_type, sort_no, always_show, is_route, is_leaf, keep_alive, hidden, `status`, del_flag, rule_flag, create_by, create_time, update_by, update_time, internal_or_external, app_show ) VALUES ( REPLACE ( UUID(), '-', '' ), "replace_pid", "批量操作按钮", 2, "${entityName?uncap_first}:batch", 1, 1.00, 0, 1, 1, 0, 0, 1, 0, 0, 'admin', NOW(), 'admin', NOW(), 0, 0 );

INSERT INTO sys_permission ( id, parent_id, `name`, menu_type, perms, perms_type, sort_no, always_show, is_route, is_leaf, keep_alive, hidden, `status`, del_flag, rule_flag, create_by, create_time, update_by, update_time, internal_or_external, app_show ) VALUES ( REPLACE ( UUID(), '-', '' ), "replace_pid", "批量删除按钮", 2, "${entityName?uncap_first}:deleteBatch", 1, 1.00, 0, 1, 1, 0, 0, 1, 0, 0, 'admin', NOW(), 'admin', NOW(), 0, 0 );

INSERT INTO sys_permission ( id, parent_id, `name`, menu_type, perms, perms_type, sort_no, always_show, is_route, is_leaf, keep_alive, hidden, `status`, del_flag, rule_flag, create_by, create_time, update_by, update_time, internal_or_external, app_show ) VALUES ( REPLACE ( UUID(), '-', '' ), "replace_pid", "导入按钮", 2, "${entityName?uncap_first}:import", 1, 1.00, 0, 1, 1, 0, 0, 1, 0, 0, 'admin', NOW(), 'admin', NOW(), 0, 0 );

INSERT INTO sys_permission ( id, parent_id, `name`, menu_type, perms, perms_type, sort_no, always_show, is_route, is_leaf, keep_alive, hidden, `status`, del_flag, rule_flag, create_by, create_time, update_by, update_time, internal_or_external, app_show ) VALUES ( REPLACE ( UUID(), '-', '' ), "replace_pid", "导出按钮", 2, "${entityName?uncap_first}:export", 1, 1.00, 0, 1, 1, 0, 0, 1, 0, 0, 'admin', NOW(), 'admin', NOW(), 0, 0 );

UPDATE sys_permission SET is_leaf = 0 where id = "replace_pid";



