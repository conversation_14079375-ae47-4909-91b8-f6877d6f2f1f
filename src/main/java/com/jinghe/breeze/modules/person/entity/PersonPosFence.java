package com.jinghe.breeze.modules.person.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 人员定位电子围栏信息表
 * @Author: chenliang
 * @Date:   2022-08-23
 * @Version: V1.0
 */
@Data
@TableName("person_pos_fence")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="person_pos_fence对象", description="人员定位电子围栏信息表")
public class PersonPosFence implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**电子围栏名称*/
	@Excel(name = "电子围栏名称", width = 15)
    @ApiModelProperty(value = "电子围栏名称")
    private java.lang.String fenceName;
	/**绘制区域类型*/
	@Excel(name = "绘制区域类型", width = 15, dicCode = "draw_type")
	@Dict(dicCode = "draw_type")
    @ApiModelProperty(value = "绘制区域类型")
    private java.lang.String drawType;
	/**启用状态*/
	@Excel(name = "启用状态", width = 15, dicCode = "is_used")
	@Dict(dicCode = "is_used")
    @ApiModelProperty(value = "启用状态")
    private java.lang.String isUsed;
	/**经纬度*/
	@Excel(name = "经纬度", width = 15)
    @ApiModelProperty(value = "经纬度")
    private java.lang.String nautica;
}
