package com.jinghe.breeze.modules.data.service.impl;

import com.jinghe.breeze.modules.data.entity.DataFanInfo;
import com.jinghe.breeze.modules.data.mapper.DataFanInfoMapper;
import com.jinghe.breeze.modules.data.service.IDataFanInfoService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: data_fan_info
 * @Author: jeecg-boot
 * @Date:   2024-05-17
 * @Version: V1.0
 */
@Service
public class DataFanInfoServiceImpl extends ServiceImpl<DataFanInfoMapper, DataFanInfo> implements IDataFanInfoService {

}
