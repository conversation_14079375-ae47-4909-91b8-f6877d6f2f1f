package com.jinghe.breeze.modules.project.mapper;

import java.util.Date;

import org.apache.ibatis.annotations.Param;
import com.jinghe.breeze.modules.project.entity.ProjectPlanReport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: project_plan_report
 * @Author: jeecg-boot
 * @Date: 2024-05-10
 * @Version: V1.0
 */
public interface ProjectPlanReportMapper extends BaseMapper<ProjectPlanReport> {

    int updateByCode(@Param("pcode") String pcode,
                      @Param("status") int status,
                      @Param("actualEndDate") Date actualEndDate,
                      @Param("actualStartDate") Date actualStartDate,
                     @Param("actualNum") Integer actualNum);

}
