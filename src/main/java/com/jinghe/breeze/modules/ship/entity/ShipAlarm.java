package com.jinghe.breeze.modules.ship.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: ship_alarm
 * @Author: jeecg-boot
 * @Date:   2024-04-23
 * @Version: V1.0
 */
@Data
@TableName("ship_alarm")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="ship_alarm对象", description="ship_alarm")
public class ShipAlarm implements Serializable {
    private static final long serialVersionUID = 1L;

	/**标识*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "标识")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd  HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd  HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd  HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
	/**删除标识*/
	@Excel(name = "删除标识", width = 15)
    @ApiModelProperty(value = "删除标识")
    private String delFlag;
	/**组织机构编码*/
    @ApiModelProperty(value = "组织机构编码")
    private String sysOrgCode;
	/**项目id*/
	@Excel(name = "项目id", width = 15)
    @ApiModelProperty(value = "项目id")
    private String projectId;
	/**mmsi*/
	@Excel(name = "mmsi", width = 15)
    @ApiModelProperty(value = "mmsi")
    private String mmsi;
	/**船舶名称*/
	@Excel(name = "船舶名称", width = 15)
    @ApiModelProperty(value = "船舶名称")
    private String shipName;
	/**日期*/
	@Excel(name = "日期", width = 15)
    @ApiModelProperty(value = "日期")
    private String alarmDay;
	/**归属（1内部2外部）*/
	@Excel(name = "归属（1内部2外部）", width = 15)
    @ApiModelProperty(value = "归属（1内部2外部）")
    private String belong;
	/**类型*/
	@Excel(name = "类型", width = 15)
    @ApiModelProperty(value = "类型")
    private String shipType;
	/**开始时间*/
	@Excel(name = "开始时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd   HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间")
    private Date endTime;
	/**结束时间*/
	@Excel(name = "开始时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd   HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间")
    private Date firstTime;
	/**船长*/
	@Excel(name = "船长", width = 15)
    @ApiModelProperty(value = "船长")
    private Integer length;
	/**船宽*/
	@Excel(name = "船宽", width = 15)
    @ApiModelProperty(value = "船宽")
    private Integer width;
	/**左舷距*/
	@Excel(name = "左舷距", width = 15)
    @ApiModelProperty(value = "左舷距")
    private Integer shipLeft;
	/**尾距*/
	@Excel(name = "尾距", width = 15)
    @ApiModelProperty(value = "尾距")
    private Integer trail;
	/**吃水*/
	@Excel(name = "吃水", width = 15)
    @ApiModelProperty(value = "吃水")
    private Integer draught;
	/**目的地港口*/
	@Excel(name = "目的地港口", width = 15)
    @ApiModelProperty(value = "目的地港口")
    private String dest;
	/**标准化后的目的地港口*/
	@Excel(name = "标准化后的目的地港口", width = 15)
    @ApiModelProperty(value = "标准化后的目的地港口")
    private String destStd;
	/**预到时间*/
	@Excel(name = "预到时间", width = 15)
    @ApiModelProperty(value = "预到时间")
    private String eta;
	/**标准化后的预到时间*/
	@Excel(name = "标准化后的预到时间", width = 15)
    @ApiModelProperty(value = "标准化后的预到时间")
    private String etaStd;
    /**船舶类型名称*/
    @Excel(name = "船舶类型名称", width = 15)
    @ApiModelProperty(value = "船舶类型名称")
    private String shipTypeName;
    /**是否进入围栏*/
    @Excel(name = "是否进入围栏", width = 15)
    @ApiModelProperty(value = "是否进入围栏")
    private Integer isIn;
}
