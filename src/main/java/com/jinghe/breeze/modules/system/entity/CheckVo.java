package com.jinghe.breeze.modules.system.entity;

import io.swagger.annotations.ApiModelProperty;

public class CheckVo {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(
            value = "表名",
            name = "tableName",
            example = "sys_log"
    )
    private String tableName;
    @ApiModelProperty(
            value = "字段名",
            name = "fieldName",
            example = "id"
    )
    private String fieldName;
    @ApiModelProperty(
            value = "字段值",
            name = "fieldVal",
            example = "1000"
    )
    private String fieldVal;
    @ApiModelProperty(
            value = "数据ID",
            name = "dataId",
            example = "2000"
    )
    private String dataId;

    @ApiModelProperty(
            value = "字段名2",
            name = "fieldName2",
            example = "id"
    )
    private String fieldName2;
    @ApiModelProperty(
            value = "字段值2",
            name = "fieldVal2",
            example = "1000"
    )
    private String fieldVal2;
    public CheckVo() {
    }

    public String getTableName() {
        return this.tableName;
    }

    public String getFieldName() {
        return this.fieldName;
    }

    public String getFieldVal() {
        return this.fieldVal;
    }

    public String getDataId() {
        return this.dataId;
    }

    public void setTableName(final String tableName) {
        this.tableName = tableName;
    }

    public void setFieldName(final String fieldName) {
        this.fieldName = fieldName;
    }

    public void setFieldVal(final String fieldVal) {
        this.fieldVal = fieldVal;
    }

    public String getFieldName2() {
        return fieldName2;
    }

    public void setFieldName2(String fieldName2) {
        this.fieldName2 = fieldName2;
    }

    public String getFieldVal2() {
        return fieldVal2;
    }

    public void setFieldVal2(String fieldVal2) {
        this.fieldVal2 = fieldVal2;
    }

    public void setDataId(final String dataId) {
        this.dataId = dataId;
    }

}
