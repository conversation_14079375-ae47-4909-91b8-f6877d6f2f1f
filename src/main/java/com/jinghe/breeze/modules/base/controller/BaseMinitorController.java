package com.jinghe.breeze.modules.base.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinghe.breeze.common.enums.DelFlagEnum;
import com.jinghe.breeze.common.enums.MinitorTypeEnum;
import com.jinghe.breeze.modules.base.entity.BaseMinitor;
import com.jinghe.breeze.modules.base.service.IBaseMinitorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 监控设备
 * @Author: jeecg-boot
 * @Date: 2024-08-06
 * @Version: V1.0
 */
@Api(tags = "监控设备")
@RestController
@RequestMapping("/base/minitor")
@Slf4j
public class BaseMinitorController extends JeecgController<BaseMinitor, IBaseMinitorService> {
    @Autowired
    private IBaseMinitorService baseMinitorService;

    /**
     * 分页列表查询
     *
     * @param baseMinitor
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "监控设备-分页列表查询")
    @ApiOperation(value = "监控设备-分页列表查询", notes = "监控设备-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(BaseMinitor baseMinitor,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<BaseMinitor> queryWrapper = new QueryWrapper<>();
        if (StrUtil.isNotEmpty(baseMinitor.getMinitorType())) {
            queryWrapper.eq("minitor_type", baseMinitor.getMinitorType());
        }
        if (StrUtil.isNotEmpty(baseMinitor.getInstallArea())) {
            queryWrapper.eq("install_area", baseMinitor.getInstallArea());
        }
        if (StrUtil.isNotEmpty(baseMinitor.getEquipmentSerial())) {
            queryWrapper.and(wrapper -> wrapper.like("minitor_name", baseMinitor.getEquipmentSerial()).or().like("equipment_serial", baseMinitor.getEquipmentSerial()));
        }
        Page<BaseMinitor> page = new Page<BaseMinitor>(pageNo, pageSize);
        IPage<BaseMinitor> pageList = baseMinitorService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param baseMinitor
     * @return
     */
    @AutoLog(value = "监控设备-添加")
    @ApiOperation(value = "监控设备-添加", notes = "监控设备-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody BaseMinitor baseMinitor) {
        baseMinitorService.save(baseMinitor);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param baseMinitor
     * @return
     */
    @AutoLog(value = "监控设备-编辑")
    @ApiOperation(value = "监控设备-编辑", notes = "监控设备-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody BaseMinitor baseMinitor) {
        baseMinitorService.updateById(baseMinitor);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "监控设备-通过id删除")
    @ApiOperation(value = "监控设备-通过id删除", notes = "监控设备-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        baseMinitorService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "监控设备-批量删除")
    @ApiOperation(value = "监控设备-批量删除", notes = "监控设备-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.baseMinitorService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "监控设备-通过id查询")
    @ApiOperation(value = "监控设备-通过id查询", notes = "监控设备-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        BaseMinitor baseMinitor = baseMinitorService.getById(id);
        if (baseMinitor == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(baseMinitor);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param baseMinitor
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, BaseMinitor baseMinitor) {
        return super.exportXls(request, baseMinitor, BaseMinitor.class, "监控设备");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, BaseMinitor.class);
    }


    /**
     * 根据安装区域获取AI设备
     */
    @AutoLog(value = "监控设备-AI列表查询")
    @ApiOperation(value = "监控设备-AI列表查询", notes = "监控设备-AI列表查询")
    @GetMapping(value = "/AIlist")
    public Result<?> queryAIByArea() {
        //获取所有AI监控
        List<BaseMinitor> baseMinitors = baseMinitorService.list(Wrappers.<BaseMinitor>lambdaQuery().eq(BaseMinitor::getDelFlag, DelFlagEnum.NORMAL.getType()).
                eq(BaseMinitor::getMinitorType, MinitorTypeEnum.AI.getType()));
        // 创建一个映射来按区域分组闸机和监控
        Map<String, Map<String, List<?>>> groupedByArea = new HashMap<>();
        // 按installArea分组监控
        Map<String, List<BaseMinitor>> minitorsByArea = baseMinitors.stream().collect(Collectors.groupingBy(BaseMinitor::getInstallArea));

        minitorsByArea.forEach((area, minitors) -> {
            //将minitors按设备名称首字母排序
            minitors.sort(Comparator.comparing(BaseMinitor::getMinitorName));
            groupedByArea.computeIfAbsent(area, k -> new HashMap<>()).put("minitors", minitors);
        });
        return Result.OK(groupedByArea);
    }

    @AutoLog(value = "监控设备-删除标记")
    @ApiOperation(value = "监控设备-删除标记", notes = "监控设备-删除标记")
    @PutMapping(value = "/cleanMark")
    public Result<?> cleanMark(@RequestBody BaseMinitor baseMinitor) {
        BaseMinitor minitor = baseMinitorService.getById(baseMinitor.getId());
        if (minitor == null) {
            return Result.error("未找到对应数据");
        }
        baseMinitor.setPositionX(null);
        baseMinitor.setPositionY(null);
        baseMinitorService.updateById(baseMinitor);
        return Result.OK("清除成功!");
    }

}
