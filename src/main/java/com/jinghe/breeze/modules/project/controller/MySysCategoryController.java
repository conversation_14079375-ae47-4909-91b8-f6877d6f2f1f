package com.jinghe.breeze.modules.project.controller;

import com.jinghe.breeze.modules.project.entity.MySysCategory;
import com.jinghe.breeze.modules.project.service.MySysCategoryService;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/project/myCategory")
public class MySysCategoryController {


    public MySysCategoryController() {

    }

    @Autowired
    private MySysCategoryService categoryService;

    @RequestMapping(value = {"/queryTreeList"}, method = {RequestMethod.GET})
    public Result<?> getAllCategories(@RequestParam(name = "code", required = true) String code,
                                      @RequestParam(name = "pid", required = false) String pid) {

        List<MySysCategory> mySysCategoriesList = categoryService.gueryTreeList(code, pid);
        return Result.OK(mySysCategoriesList);
    }

}
