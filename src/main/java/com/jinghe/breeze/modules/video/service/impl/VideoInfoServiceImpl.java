package com.jinghe.breeze.modules.video.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.common.constants.enums.VideoFolderEnum;
import com.jinghe.breeze.modules.video.entity.VideoFolder;
import com.jinghe.breeze.modules.video.entity.VideoInfo;
import com.jinghe.breeze.modules.video.entity.vo.VideoFolderNode;
import com.jinghe.breeze.modules.video.mapper.VideoInfoMapper;
import com.jinghe.breeze.modules.video.service.IVideoFolderService;
import com.jinghe.breeze.modules.video.service.IVideoInfoService;
import jodd.util.StringUtil;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: video_info
 * @Author: jeecg-boot
 * @Date: 2024-06-13
 * @Version: V1.0
 */
@Service
public class VideoInfoServiceImpl extends ServiceImpl<VideoInfoMapper, VideoInfo> implements IVideoInfoService {


    @Autowired
    private IVideoFolderService videoFolderService;

    public Result<?> add(List<VideoInfo> videoInfoList) {
        if (videoInfoList.isEmpty()) {
            return Result.error("请添加视频或者图片");
        }
        String folderId = videoInfoList.get(0).getFolderId();
        if (StringUtil.isEmpty(folderId)) {
            return Result.error("未指定视频目录");
        }
        super.saveBatch(videoInfoList);
        return Result.OK("添加成功");
    }

    private void sortNodeList(List<VideoFolderNode> nodeList) {
        //根据sortNo升序排列 如果children 不为空 children也排序
        nodeList.sort(Comparator.comparingInt(VideoFolderNode::getSortNo));
        for (VideoFolderNode node : nodeList) {
            if (node.getChildren() != null && node.getChildren().size() > 0) {
                sortNodeList(node.getChildren());
            }
        }
    }

    //构建树形结构,将树按sort_no排序
    @Override
    public List<VideoFolderNode> getTreeData() {
        List<VideoFolderNode> result = new ArrayList<>();

        VideoFolderNode construction = new VideoFolderNode();
        construction.setId(VideoFolderEnum.CONSTRUCTION.getId());
        construction.setPid(VideoFolderEnum.PLATFORM.getId());
        construction.setName(VideoFolderEnum.CONSTRUCTION.getName());

        VideoFolderNode dolphin = new VideoFolderNode();
        dolphin.setPid(VideoFolderEnum.PLATFORM.getId());
        dolphin.setName(VideoFolderEnum.DOLPHIN.getName());
        dolphin.setId(VideoFolderEnum.DOLPHIN.getId());
        //平台目录
        VideoFolderNode platform = new VideoFolderNode();
        platform.setId("platform");
        platform.setName("平台目录");
        platform.setChildren(new ArrayList<>());
        platform.getChildren().add(construction);
        platform.getChildren().add(dolphin);
        result.add(platform);

        List<VideoFolder> folderList = videoFolderService.list();
        List<VideoFolderNode> nodeList = folderList.stream().map(folder -> {
            VideoFolderNode node = new VideoFolderNode();
            node.setId(folder.getId());
            node.setName(folder.getName());
            node.setPid(folder.getPid());
            node.setLayer(folder.getLayer());
            node.setSortNo(folder.getSortNo());
            return node;
        }).collect(Collectors.toList());
        Map<String, VideoFolderNode> map = new HashMap<>();
        nodeList.forEach(node -> {
            map.put(node.getId(), node);
        });
        //nodeList构建树
        nodeList.forEach(node -> {
            VideoFolderNode parent = map.get(node.getPid());
            if (parent == null) {
                result.add(node);// 根节点
            }
            if (parent != null) {
                if (parent.getChildren() == null) {
                    parent.setChildren(new ArrayList<>());
                }
                parent.getChildren().add(node);
            }
        });
        sortNodeList(nodeList);
        //将树按照sort_no 升序排序
        return result;
    }

    public Result<?> queryPageList(VideoInfo videoInfo, Integer pageNo, Integer pageSize, HttpServletRequest req) {
        QueryWrapper<VideoInfo> queryWrapper = QueryGenerator.initQueryWrapper(videoInfo, req.getParameterMap());
        String folderId = videoInfo.getFolderId();
        if (StringUtil.isEmpty(folderId)) {
            return Result.error("未指定视频目录");
        }
        VideoFolder folder = videoFolderService.getById(folderId);
        if (folder == null && !folderId.equals(VideoFolderEnum.DOLPHIN.getId()) && !folderId.equals(VideoFolderEnum.CONSTRUCTION.getId())) {
            return Result.error("未找到指定的目录");
        }
        Page<VideoInfo> page = new Page<VideoInfo>(pageNo, pageSize);
        IPage<VideoInfo> pageList = super.page(page, queryWrapper);
        return Result.OK(pageList);
    }

//    private List<String> getFilesFromString(String filesString) {
//        if (!StringUtil.isEmpty(filesString)) {
//            filesString = filesString.trim();
//            List<String> fileList = Arrays.stream(filesString.split(","))
//                    .filter(s -> !StringUtil.isEmpty(s)).collect(Collectors.toList());
//            return fileList;
//        }
//        return new ArrayList<>();
//    }

    /**
     * 将字符串列表转换为文件列表
     *
     * @param filesStringList ['[json]','[json]']
     * @return
     */
    private List<String> getFilesFromStringList(List<String> filesStringList) {
        if (filesStringList == null || filesStringList.size() == 0) {
            return new ArrayList<>();
        }
        List<String> files = new ArrayList<>();
        for (String filesString : filesStringList) {
            if (StringUtil.isEmpty(filesString)) {
                continue;
            }
            JSONArray jsonArray;
            try {
                jsonArray = JSONArray.parseArray(filesString);
                if (jsonArray.size() > 0) {
                    jsonArray.forEach(p -> {
                        JSONObject jsonObject = (JSONObject) p;
                        String url = jsonObject.getString("url");
                        files.add(url);
                    });
                }
            } catch (Exception e) {
                continue;
            }
        }
        return files;
    }

    /**
     * 校验folderId是否合法 ,非法在抛出异常
     *
     * @param folderId
     */
    private void validateFolder(String folderId) {
        boolean isConstructFolder = Objects.equals(VideoFolderEnum.CONSTRUCTION.getId(), folderId) ||
                Objects.equals(VideoFolderEnum.DOLPHIN.getId(), folderId);//施工日志,观豚日志目录
        if (!isConstructFolder && videoFolderService.getById(folderId) == null) {
            throw new JeecgBootException("指定的目录不存在");
        }

    }

    /**
     * 将视频或图片列表添加到指定目录
     *
     * @param files    文件网址数组,自动过滤不是图片视频以及空文本
     * @param folderId 目录id
     */
    public void addMedia(List<String> files, String folderId) {
//        .mp4, .avi, .mov, .mkv, .wmv, .flv, .mpeg, .mpg, .m4v, .3gp, .webm, .vob, .ogv
//        .jpg, .jpeg, .png, .gif, .bmp, .tiff, .tif, .svg, .webp, .ico, .heic, .heif, .raw, .cr2, .nef, .orf, .sr2
        if (files == null || files.size() == 0) {
            return;
        }
        validateFolder(folderId);
        List<VideoInfo> videoInfos = new ArrayList<>();
        for (String file : files) {
            VideoInfo videoInfo = new VideoInfo();
            videoInfo.setFile(file);
            videoInfo.setFolderId(folderId);
            boolean isImage = Common.mediaType.isImageFile(file);
            if (isImage) {
                videoInfo.setFileType(Common.commonality.ZERO);
                videoInfos.add(videoInfo);
                continue;
            }
            if (Common.mediaType.isVideoFile(file)) {
                videoInfo.setFileType(Common.commonality.ONE);
                videoInfos.add(videoInfo);
            }
        }
        if (videoInfos.size() > 0) {
            this.saveBatch(videoInfos);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateVideoInfo(List<String> oldFileStringList, List<String> newFileStringList, String folderId) {
        validateFolder(folderId);
        List<String> oldList = getFilesFromStringList(oldFileStringList); //转换为单个的文件的数组
        List<String> newList = getFilesFromStringList(newFileStringList); //同上
        //分别得出需要删除的和需要新增的
        List<String> toAddList = newList.stream().filter(p -> !oldList.contains(p)).collect(Collectors.toList());
        List<String> toDeleteList = oldList.stream().filter(p -> !newList.contains(p)).collect(Collectors.toList());
        addMedia(toAddList, folderId);
        deleteByFiles(toDeleteList, folderId);
    }

    /**
     * 删除多个文件
     *
     * @param files    单个文件的地址
     * @param folderId
     */
    public void deleteByFiles(List<String> files, String folderId) {
        if (files == null || files.size() == 0) {
            return;
        }
        validateFolder(folderId);//校验目录是否合法
        baseMapper.delete(new LambdaQueryWrapper<VideoInfo>().in(VideoInfo::getFile, files));
    }
}


