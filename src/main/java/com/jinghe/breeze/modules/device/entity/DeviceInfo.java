package com.jinghe.breeze.modules.device.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
 * @Description: 设备信息
 * @Author: jeecg-boot
 * @Date: 2024-08-13
 * @Version: V1.0
 */
@ApiModel(value = "device_info对象", description = "设备信息")
@Data
@TableName("device_info")
public class DeviceInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
    /**
     * 删除状态（0，正常，1已删除）
     */
    @Excel(name = "删除状态（0，正常，1已删除）", width = 15)
    @ApiModelProperty(value = "删除状态（0，正常，1已删除）")
    private String delFlag;
    /**
     * 设备名称
     */
    @Excel(name = "设备名称", width = 15)
    @ApiModelProperty(value = "设备名称")
    private String name;
    /**
     * 安装区域
     */
    @Excel(name = "安装区域", width = 15)
    @ApiModelProperty(value = "安装区域")
    private String areaId;
    /**
     * 安装位置
     */
    @Excel(name = "安装位置", width = 15)
    @ApiModelProperty(value = "安装位置")
    private String installPosition;
    /**
     * 安装日期
     */
    @Excel(name = "安装日期", width = 15)
    @ApiModelProperty(value = "安装日期")
    private String installDate;
    /**
     * 坐标点X轴
     */
    @Excel(name = "坐标点X轴", width = 15)
    @ApiModelProperty(value = "坐标点X轴")
    private String positionX;
    /**
     * 坐标点Y轴
     */
    @Excel(name = "坐标点Y轴", width = 15)
    @ApiModelProperty(value = "坐标点Y轴")
    private String positionY;
    /**
     * 设备类型
     */
    @Excel(name = "设备类型", width = 15)
    @ApiModelProperty(value = "设备类型")
    private String deviceType;
    /**
     * 备注
     */
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 设备序列号
     */
    @Excel(name = "设备序列号", width = 15)
    @ApiModelProperty(value = "设备序列号")
    private String equipmentSerial;


    /**
     * 监控类型
     */
    @Excel(name = "监控类型", width = 15)
    @ApiModelProperty(value = "监控类型")
    private String monitorType;

    /**
     * 监控设备服务地址
     */
    @Excel(name = "监控设备服务地址", width = 500)
    @ApiModelProperty(value = "监控设备服务地址")
    private String equipmentUrl;

    @ExcelCollection(name = "三维可视化X坐标")
    @ApiModelProperty(value = "三维可视化X坐标")
    private String modelX;

    @ExcelCollection(name = "三维可视化Y坐标")
    @ApiModelProperty(value = "三维可视化Y坐标")
    private String modelY;

    @ExcelCollection(name = "三维可视化Z坐标")
    @ApiModelProperty(value = "三维可视化Z坐标")
    private String modelZ;
}
