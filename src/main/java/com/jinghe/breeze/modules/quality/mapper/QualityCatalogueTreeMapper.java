package com.jinghe.breeze.modules.quality.mapper;

import org.apache.ibatis.annotations.Param;
import com.jinghe.breeze.modules.quality.entity.QualityCatalogueTree;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: quality_catalogue_tree
 * @Author: jeecg-boot
 * @Date:   2025-06-20
 * @Version: V1.0
 */
public interface QualityCatalogueTreeMapper extends BaseMapper<QualityCatalogueTree> {

	/**
	 * 编辑节点状态
	 * @param id
	 * @param status
	 */
	void updateTreeNodeStatus(@Param("id") String id,@Param("status") String status);

}
