package com.jinghe.breeze.modules.safety.controller;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.jinghe.breeze.modules.safety.entity.SafeInspectionRecords;
import com.jinghe.breeze.modules.safety.service.ISafeInspectionRecordsService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 安全检查
 * @Author: jeecg-boot
 * @Date:   2025-06-24
 * @Version: V1.0
 */
@Api(tags="安全检查")
@RestController
@RequestMapping("/safety/safeInspectionRecords")
@Slf4j
public class SafeInspectionRecordsController extends JeecgController<SafeInspectionRecords, ISafeInspectionRecordsService> {
	@Autowired
	private ISafeInspectionRecordsService safeInspectionRecordsService;
	
	/**
	 * 分页列表查询
	 *
	 * @param safeInspectionRecords
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "安全检查-分页列表查询")
	@ApiOperation(value="安全检查-分页列表查询", notes="安全检查-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(SafeInspectionRecords safeInspectionRecords,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<SafeInspectionRecords> queryWrapper = QueryGenerator.initQueryWrapper(safeInspectionRecords, req.getParameterMap());
		if (!ObjectUtils.isEmpty(safeInspectionRecords.getStartDate())){
			queryWrapper.between("inspection_date",safeInspectionRecords.getStartDate(),safeInspectionRecords.getEndDate());
		}
		Page<SafeInspectionRecords> page = new Page<SafeInspectionRecords>(pageNo, pageSize);
		IPage<SafeInspectionRecords> pageList = safeInspectionRecordsService.page(page, queryWrapper);
		safeInspectionRecordsService.fullDetails(pageList.getRecords());
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param safeInspectionRecords
	 * @return
	 */
	@AutoLog(value = "安全检查-添加")
	@ApiOperation(value="安全检查-添加", notes="安全检查-添加")
	@RequiresPermissions("safeInspectionRecords:add")
	@PostMapping(value = "/add")
	public Result<?> add(@Validated @RequestBody SafeInspectionRecords safeInspectionRecords) {
		safeInspectionRecordsService.createOrUpdate(safeInspectionRecords);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param safeInspectionRecords
	 * @return
	 */
	@AutoLog(value = "安全检查-编辑")
	@ApiOperation(value="安全检查-编辑", notes="安全检查-编辑")
	@RequiresPermissions("safeInspectionRecords:edit")
	@PutMapping(value = "/edit")
	public Result<?> edit(@Validated @RequestBody SafeInspectionRecords safeInspectionRecords) {
		safeInspectionRecordsService.createOrUpdate(safeInspectionRecords);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "安全检查-通过id删除")
	@ApiOperation(value="安全检查-通过id删除", notes="安全检查-通过id删除")
	@RequiresPermissions("safeInspectionRecords:delete")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		safeInspectionRecordsService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "安全检查-批量删除")
	@ApiOperation(value="安全检查-批量删除", notes="安全检查-批量删除")
	@RequiresPermissions("safeInspectionRecords:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.safeInspectionRecordsService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "安全检查-通过id查询")
	@ApiOperation(value="安全检查-通过id查询", notes="安全检查-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		SafeInspectionRecords safeInspectionRecords = safeInspectionRecordsService.getById(id);
		if(safeInspectionRecords==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(safeInspectionRecords);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param safeInspectionRecords
    */
    @RequiresPermissions("safeInspectionRecords:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SafeInspectionRecords safeInspectionRecords) {
        return super.exportXls(request, safeInspectionRecords, SafeInspectionRecords.class, "安全检查");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("safeInspectionRecords:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SafeInspectionRecords.class);
    }

}
