package com.jinghe.breeze.modules.ship.controller;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.modules.data.service.IDataShipInfoService;
import com.jinghe.breeze.modules.ship.service.IShipInfoService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.jinghe.breeze.modules.ship.entity.ShipOutgoing;
import com.jinghe.breeze.modules.ship.service.IShipOutgoingService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.util.oConvertUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: ship_outgoing
 * @Author: jeecg-boot
 * @Date: 2024-04-23
 * @Version: V1.0
 */
@Api(tags = "ship_outgoing")
@RestController
@RequestMapping("/ship/shipOutgoing")
@Slf4j
public class ShipOutgoingController extends JeecgController<ShipOutgoing, IShipOutgoingService> {
    @Autowired
    private IShipOutgoingService shipOutgoingService;

    @Autowired
    private IDataShipInfoService dataShipInfoService;

    @Autowired
    private IShipInfoService shipInfoService;

    /**
     * 分页列表查询
     *
     * @param shipOutgoing
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "ship_outgoing-分页列表查询")
    @ApiOperation(value = "ship_outgoing-分页列表查询", notes = "ship_outgoing-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(ShipOutgoing shipOutgoing,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   @RequestParam(name = "startTime", required = false) String startTime,
                                   @RequestParam(name = "endTime", required = false) String endTime,
                                   HttpServletRequest req) {
        //退场 时,单位名设置为 '-'  进场0  离场1    进场大于退场
        QueryWrapper<ShipOutgoing> queryWrapper = QueryGenerator.initQueryWrapper(shipOutgoing, req.getParameterMap());
        SimpleDateFormat sdf = new SimpleDateFormat(Common.date.SECONDS);
        if (oConvertUtils.isNotEmpty(startTime)) {
            try {
                Date start = sdf.parse(startTime + " 00:00:00");
                Date end = sdf.parse(endTime + " 23:59:59");
                queryWrapper.between("date", start, end);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        queryWrapper.orderByDesc("is_out")
                .orderByDesc("date");
        Page<ShipOutgoing> page = new Page<ShipOutgoing>(pageNo, pageSize);
        IPage<ShipOutgoing> pageList = shipOutgoingService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param shipOutgoing
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @AutoLog(value = "ship_outgoing-添加")
    @ApiOperation(value = "ship_outgoing-添加", notes = "ship_outgoing-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody ShipOutgoing shipOutgoing) {
        return shipOutgoingService.add(shipOutgoing);
    }
//此业务没有编辑功能,只有添加和删除.

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @AutoLog(value = "ship_outgoing-通过id删除")
    @ApiOperation(value = "ship_outgoing-通过id删除", notes = "ship_outgoing-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        shipOutgoingService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "ship_outgoing-批量删除")
    @ApiOperation(value = "ship_outgoing-批量删除", notes = "ship_outgoing-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.shipOutgoingService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "ship_outgoing-通过id查询")
    @ApiOperation(value = "ship_outgoing-通过id查询", notes = "ship_outgoing-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        ShipOutgoing shipOutgoing = shipOutgoingService.getById(id);
        if (shipOutgoing == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(shipOutgoing);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param shipOutgoing
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ShipOutgoing shipOutgoing) {
        return super.exportXls(request, shipOutgoing, ShipOutgoing.class, "ship_outgoing");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ShipOutgoing.class);
    }

}
