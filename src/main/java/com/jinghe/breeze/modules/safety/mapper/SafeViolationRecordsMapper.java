package com.jinghe.breeze.modules.safety.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import com.jinghe.breeze.modules.safety.entity.SafeViolationRecords;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: safe_violation_records
 * @Author: jeecg-boot
 * @Date:   2025-06-22
 * @Version: V1.0
 */
public interface SafeViolationRecordsMapper extends BaseMapper<SafeViolationRecords> {

}
