package com.jinghe.breeze.modules.project.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: project_wbs
 * @Author: jeecg-boot
 * @Date: 2024-04-17
 * @Version: V1.0
 */
@Data
@TableName("project_wbs")
@ApiModel(value = "project_wbs对象", description = "project_wbs")
public class ProjectWbs implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 标识
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "标识")
    private java.lang.String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
    /**
     * 删除标识
     */
    @Excel(name = "删除标识", width = 15)
    @ApiModelProperty(value = "删除标识")
    private java.lang.Integer delFlag;
    /**
     * 组织机构编码
     */
    @ApiModelProperty(value = "组织机构编码")
    private java.lang.String sysOrgCode;
    /**
     * 项目id
     */
    @Excel(name = "项目id", width = 15)
    @ApiModelProperty(value = "项目id")
    private java.lang.String projectId;
    /**
     * WBS编码
     */
    @Excel(name = "WBS编码", width = 15)
    @ApiModelProperty(value = "WBS编码")
    private java.lang.String code;
    /**
     * Wbs名称
     */
    @Excel(name = "Wbs名称", width = 15)
    @ApiModelProperty(value = "Wbs名称")
    private java.lang.String name;
    /**
     * 父级id 第一层级为0
     */
    @Excel(name = "父级id 第一层级为0", width = 15)
    @ApiModelProperty(value = "父级id 第一层级为0")
    private java.lang.String pid;
    /**
     * 同级排序越大越后
     */
    @Excel(name = "同级排序越大越后", width = 15)
    @ApiModelProperty(value = "同级排序越大越后")
    private java.lang.Integer sortNo;
    /**
     * 所在层级从0开始
     */
    @Excel(name = "所在层级从0开始", width = 15)
    @ApiModelProperty(value = "所在层级从0开始")
    private java.lang.Integer layer;
    /**
     * Wbs类型（）关联分类字典
     */
    @Excel(name = "Wbs类型（）关联分类字典", width = 15, dicCode = "wbs_type")
    @Dict(dicCode = "wbs_type")
    @ApiModelProperty(value = "Wbs类型（）关联分类字典")
    private java.lang.String type;
    /**
     * 是否有子节点
     */
    @Excel(name = "是否有子节点", width = 15, dicCode = "yn")
    @Dict(dicCode = "yn")
    @ApiModelProperty(value = "是否有子节点")
    private int hasChild;

    /**
     * 是否有子节点
     */
    @Excel(name = "是否有子节点", width = 15, dicCode = "yn")
    @Dict(dicCode = "yn")
    @ApiModelProperty(value = "是否有子节点")
    private java.lang.Integer isPlan;


    @TableField(exist = false)  // 如果不存储在数据库中
    private boolean upEnable;

    @TableField(exist = false)
    private boolean downEnable;

    public boolean isUpEnable() {
        return upEnable;
    }

    public void setUpEnable(boolean upEnable) {
        this.upEnable = upEnable;
    }

    public boolean isDownEnable() {
        return downEnable;
    }

    public void setDownEnable(boolean downEnable) {
        this.downEnable = downEnable;
    }
}
