package com.jinghe.breeze.modules.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.modules.project.entity.ProjectPlanExcelData;
import com.jinghe.breeze.modules.project.entity.ProjectPlanReport;
import com.jinghe.breeze.modules.project.service.IProjectPlanReportService;
import jodd.util.StringUtil;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.oConvertUtils;
import com.jinghe.breeze.modules.project.entity.ProjectPlan;
import com.jinghe.breeze.modules.project.mapper.ProjectPlanMapper;
import com.jinghe.breeze.modules.project.service.IProjectPlanService;
import org.jeecg.modules.system.entity.SysDict;
import org.jeecg.modules.system.entity.SysDictItem;
import org.jeecg.modules.system.service.ISysDictItemService;
import org.jeecg.modules.system.service.ISysDictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.util.StringUtils;

/**
 * @Description: project_plan
 * @Author: jeecg-boot
 * @Date: 2024-04-17
 * @Version: V1.0
 */
@Service
public class ProjectPlanServiceImpl extends ServiceImpl<ProjectPlanMapper, ProjectPlan> implements IProjectPlanService {

    @Autowired
    private ISysDictService sysDictService;
    @Autowired
    private IProjectPlanReportService projectPlanReportService;

    @Autowired
    private ISysDictItemService sysDictItemService; //ISysDictItemService

    @Override
    public void addProjectPlan(ProjectPlan projectPlan) {
        if (oConvertUtils.isEmpty(projectPlan.getPid())) {
            projectPlan.setPid(IProjectPlanService.ROOT_PID_VALUE);
        } else {
            //如果当前节点父ID不为空 则设置父节点的hasChildren 为1
            ProjectPlan parent = baseMapper.selectById(projectPlan.getPid());
            if (parent != null && !"1".equals(parent.getHasChild())) {
                parent.setHasChild("1");
                baseMapper.updateById(parent);
            }
        }
        baseMapper.insert(projectPlan);
    }

    private void setHasChild(ProjectPlan projectPlan) {
        Integer layer = projectPlan.getLayer();
        QueryWrapper<ProjectPlan> wrapper = new QueryWrapper<>();
        wrapper.eq("layer", layer - 1);
        List<ProjectPlan> list = baseMapper.selectList(wrapper);
        if (list != null && list.size() > 0) {
            projectPlan.setHasChild("1");
        } else {
            projectPlan.setHasChild("0");
        }
    }

    public void processSetHasChild(List<ProjectPlan> projectPlanList) {
        projectPlanList.forEach(p -> setHasChild(p));
    }

    @Override
    public void updateProjectPlan(ProjectPlan projectPlan) {
        ProjectPlan entity = this.getById(projectPlan.getId());
        if (entity == null) {
            throw new JeecgBootException("未找到对应实体");
        }
        String old_pid = entity.getPid();
        String new_pid = projectPlan.getPid();
        if (!old_pid.equals(new_pid)) {
            updateOldParentNode(old_pid);
            if (oConvertUtils.isEmpty(new_pid)) {
                projectPlan.setPid(IProjectPlanService.ROOT_PID_VALUE);
            }
            if (!IProjectPlanService.ROOT_PID_VALUE.equals(projectPlan.getPid())) {
                baseMapper.updateTreeNodeStatus(projectPlan.getPid(), IProjectPlanService.HASCHILD);
            }
        }
        baseMapper.updateById(projectPlan);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProjectPlan(String id) throws JeecgBootException {
        //查询选中节点下所有子节点一并删除
        id = this.queryTreeChildIds(id);
        if (id.indexOf(",") > 0) {
            StringBuffer sb = new StringBuffer();
            String[] idArr = id.split(",");
            for (String idVal : idArr) {
                if (idVal != null) {
                    ProjectPlan projectPlan = this.getById(idVal);
                    String pidVal = projectPlan.getPid();
                    //查询此节点上一级是否还有其他子节点
                    List<ProjectPlan> dataList = baseMapper.selectList(new QueryWrapper<ProjectPlan>().eq("pid", pidVal).
                            notIn("id", Arrays.asList(idArr)));
                    if ((dataList == null || dataList.size() == 0) && !Arrays.asList(idArr).contains(pidVal)
                            && !sb.toString().contains(pidVal)) {
                        //如果当前节点原本有子节点 现在木有了，更新状态
                        sb.append(pidVal).append(",");
                    }
                }
            }
            //批量删除节点
            baseMapper.deleteBatchIds(Arrays.asList(idArr));
            //修改已无子节点的标识
            String[] pidArr = sb.toString().split(",");
            for (String pid : pidArr) {
                this.updateOldParentNode(pid);
            }
        } else {
            ProjectPlan projectPlan = this.getById(id);
            if (projectPlan == null) {
                throw new JeecgBootException("未找到对应实体");
            }
            updateOldParentNode(projectPlan.getPid());
            baseMapper.deleteById(id);
        }
    }

    @Override
    public List<ProjectPlan> queryTreeListNoPage(QueryWrapper<ProjectPlan> queryWrapper) {
        List<ProjectPlan> dataList = baseMapper.selectList(queryWrapper);
        List<ProjectPlan> mapList = new ArrayList<>();
        for (ProjectPlan data : dataList) {
            String pidVal = data.getPid();
            //递归查询子节点的根节点
            if (pidVal != null && !"0".equals(pidVal)) {
                ProjectPlan rootVal = this.getTreeRoot(pidVal);
                if (rootVal != null && !mapList.contains(rootVal)) {
                    mapList.add(rootVal);
                }
            } else {
                if (!mapList.contains(data)) {
                    mapList.add(data);
                }
            }
        }
        return mapList;
    }


    public ProjectPlan buildTree(ProjectPlan plan, List<ProjectPlan> planList) {
//        List<ProjectPlan> children = planList.stream()
//                .filter(c -> c.getPid().equals(plan.getId()))
//                .map(c -> buildTree(c, planList))
//                .collect(Collectors.toList());
//        plan.setChildren(children);
        return plan;

    }

    @Override
    public void buildTreeWithoutParent(List<ProjectPlan> list, String rootPidValue) {
        List<ProjectPlan> root = list.stream()
                .filter(c -> c.getPid().equals(rootPidValue))
                .map(c -> buildTree(c, list))
                .collect(Collectors.toList());
        list.clear();
        list.addAll(root);
    }


    /**
     * @param code
     * @return 返回的Map key 为 sysDictItem 中的ItemText ,value 为ItemValue
     */
    private Map<String, String> getDictMap(String code) {
        Map<String, String> dictMap = new HashMap<>();
        QueryWrapper<SysDict> sysDictQueryWrapper = new QueryWrapper<>();
        sysDictQueryWrapper.eq("dict_code", code);
        List<SysDict> sysDictList = sysDictService.list(sysDictQueryWrapper);
        if (sysDictList == null || sysDictList.size() == 0) {
            return dictMap;
        }
        String id = sysDictList.get(0).getId();
        List<SysDictItem> sysDictItemList = sysDictItemService.list(new QueryWrapper<SysDictItem>().eq("dict_id", id));
        if (sysDictItemList == null || sysDictItemList.size() == 0) {
            return dictMap;
        }
        for (SysDictItem sysDictItem : sysDictItemList) {
            dictMap.put(sysDictItem.getItemText(), sysDictItem.getItemValue());
        }
        return dictMap;
    }

    private Map<String, String> parseRelationShip(String inputText) {
        //任务关系格式多种情况
        //   1.3.1[FS+2days],1.3.2[SS+0days]    多个的
        //   1.3.1                              这种默认是加上[FS+0days]
        //   1.3.1[FS+2days]                    单个的
        Map<String, String> dictMap = new HashMap<>();
        List<String> codes = new ArrayList<>();
        List<String> days = new ArrayList<>();
        List<String> relationships = new ArrayList<>();
        if (inputText == null) {
            dictMap.put("codes", null);
            dictMap.put("days", null);
            dictMap.put("relationships", null);
            return dictMap;
        }
        String[] arr = inputText.split(",");
        for (String s : arr) {
            if (!s.contains("[")) {
                s = (s + "[FS+0days]");
            }
            s = s.toUpperCase();
            String[] tmpArr = s.split("\\[");
            codes.add(tmpArr[0].trim());
            if (tmpArr[1].contains("FS")) {
                relationships.add("FS");
            } else if (tmpArr[1].contains("SS")) {
                relationships.add("SS");
            } else {
                throw new JeecgBootException("解析任务关系字符串出错" + inputText);
            }
            String dayText = tmpArr[1].split("\\+")[1].split("DAY")[0];
            days.add(dayText);
        }
        dictMap.put("codes", String.join(",", codes));
        dictMap.put("days", String.join(",", days));
        dictMap.put("relationships", String.join(",", relationships));
        return dictMap;
    }

    /**
     * excel中的大纲级别已经废弃 ,改用这个函数来获取layer字段
     * @param code
     * @return
     */
    private Integer getLayer(String code) {
        if (StringUtil.isEmpty(code)) {
            throw new JeecgBootException("wbs编码不能为空");
        }
        List<String> list =  Arrays.asList(code.trim().split("\\."));
        return list.size();
    }

    @Transactional(rollbackFor = Exception.class)
    public void importExcelData(List<ProjectPlanExcelData> projectPlanExcelDataList, Integer version) {
        if (version != null) {
            version = version + 1;
        } else {
            Integer maxVersion = (Integer) baseMapper.selectObjs(new QueryWrapper<ProjectPlan>().select("max(version)"))
                    .stream().findFirst().orElse(-1);
            version = maxVersion + 1;
        }
        Map<String, String> dictMap = getDictMap("wbs_type");
        List<ProjectPlan> projectPlanList = new ArrayList<>();
        for (ProjectPlanExcelData projectPlanExcelData : projectPlanExcelDataList) {
            ProjectPlan projectPlan = new ProjectPlan();
            String typeString = projectPlanExcelData.getTaskType();
            if (typeString != null) {
                typeString = typeString.trim();
                //不存在这个类型，设置为null
                projectPlan.setTaskType(dictMap.getOrDefault(typeString, null));
            } else {
                projectPlan.setTaskType(null);
            }
            projectPlan.setVersion(version); //改为采用前端的版本号
            String isKeytask = projectPlanExcelData.getIsKeyTask();
            if (isKeytask == null) {
                projectPlan.setIsKeytask(0);
            } else if ("是".equals(isKeytask)) {
                projectPlan.setIsKeytask(1);
            } else if ("否".equals(isKeytask)) {
                projectPlan.setIsKeytask(0);
            } else {
                throw new JeecgBootException("类型必须为是或否或空");
            }
            String planDayString = projectPlanExcelData.getPlanDay();
            if (planDayString == null) {
                projectPlan.setPlanDay(null);
            } else {
                try {
                    Integer planDay = Integer.parseInt(planDayString.trim().replace("days", "").replace("day", ""));
                    projectPlan.setPlanDay(planDay);
                } catch (Exception e) {
                    throw new JeecgBootException("解析计划天数失败" + planDayString);
                }
            }
            String lastWork = projectPlanExcelData.getFollowTaskCode();
            Map<String, String> mapDict = parseRelationShip(lastWork);
            projectPlan.setFollowTaskCodes(mapDict.get("codes"));
            projectPlan.setFollowTaskRelationship(mapDict.get("relationships"));
            projectPlan.setDelayDays(mapDict.get("days"));

            projectPlan.setRemark(projectPlanExcelData.getRemark());
            projectPlan.setName(projectPlanExcelData.getName());
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy年MM月dd日");
            try {
                Date startDate = formatter.parse(projectPlanExcelData.getStartDate());
                Date endDate = formatter.parse(projectPlanExcelData.getEndDate());
                projectPlan.setStartDate(startDate);
                projectPlan.setEndDate(endDate);
            } catch (Exception e) {
                throw new JeecgBootException("时间格式不正确，默认为yyyy年MM月dd日");
            }
            String tmpCode = projectPlanExcelData.getCode();
            if (StringUtil.isEmpty(tmpCode)) {
                throw new JeecgBootException("编码不能为空");
            }
            projectPlan.setCode(tmpCode);
//            Integer layer = projectPlanExcelData.getLayer();
            Integer layer = getLayer(tmpCode);
            projectPlan.setLayer(layer);
            projectPlan.setHasChild("0");//先统一设置为没有子
            if (layer == 1) {
                projectPlan.setPid(IProjectPlanService.ROOT_PID_VALUE);
            }
            //获取对应的id;
            QueryWrapper<ProjectPlan> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("code", projectPlan.getCode());
            projectPlanList.add(projectPlan);
        }
        projectPlanList.forEach(p -> {
            baseMapper.insert(p);
        });


        for (ProjectPlan plan : projectPlanList) { //找plan的父
            String id = plan.getId();
            String planCode = plan.getCode();
            String tmpCode = planCode;
            Integer layer = plan.getLayer();
            if (planCode.contains(".")) {
                tmpCode = planCode.substring(0, planCode.lastIndexOf("."));
            }
            final String parentCode = tmpCode;
            Optional<ProjectPlan> planOptional = projectPlanList.stream()
                    .filter(x -> x.getCode().equals(parentCode) && x.getLayer() == (layer - 1))
                    .findFirst();
            if (!planOptional.isPresent()) {
                continue;
            }
            ProjectPlan parent = planOptional.get();
            String pid = parent.getId();
            plan.setPid(pid);
            parent.setHasChild("1");
        }
        projectPlanList.forEach(p -> {
            baseMapper.updateById(p);
        });
    }

    @Override
    public Result<?> queryList(Integer version) {
        LambdaQueryWrapper<ProjectPlan> planQuery = new LambdaQueryWrapper<>();
        planQuery.orderByDesc(ProjectPlan::getVersion);
        if (StringUtils.isEmpty(version)){
            planQuery.last("LIMIT 1");
            /*查询最后一个版本*/
            ProjectPlan one = super.getOne(planQuery);
            if (oConvertUtils.isNotEmpty(one)) {
                planQuery = new LambdaQueryWrapper<>();
                version = one.getVersion();
            }
        }
        planQuery.eq(ProjectPlan::getVersion, version);
        List<ProjectPlan> projectPlanList = super.list(planQuery);
        if (projectPlanList.size() == 0) {
            return Result.OK();
        }
        List<String> codes = projectPlanList.stream().map(x -> x.getCode()).collect(Collectors.toList());
        LambdaQueryWrapper<ProjectPlanReport> reportWrapper = new LambdaQueryWrapper<>();
        reportWrapper.in(ProjectPlanReport::getBaseCode, codes);
        reportWrapper.eq(ProjectPlanReport::getDelFlag, Common.delete_flag.OK);
        List<ProjectPlanReport> list = projectPlanReportService.list(reportWrapper);
        for (ProjectPlan projectPlan : projectPlanList) {
            Optional<ProjectPlanReport> first = list.stream().filter(x -> x.getBaseCode().equals(projectPlan.getCode())).findFirst();
            if (first.isPresent()) {
                projectPlan.setProjectPlanReport(first.get());
                break;
            }
//            Integer layer = projectPlan.getLayer();
//            if (layer == null || layer <= 1) {
//                //如果层级是1或者没有 则表示是顶级pid = "0"
//                projectPlan.setLayer(1);
//                projectPlan.setPid(ROOT_PID_VALUE);
//                continue;
//            }
//            //否则根据自身code 去掉最后一个`.` 查找自己的父级
//            String code = projectPlan.getCode();
//            String parentCode = code.substring(0, code.lastIndexOf("."));
//            Optional<ProjectPlan> planOptional = projectPlanList.stream()
//                    .filter(x -> x.getCode().equals(parentCode) && x.getLayer() == (layer - 1))
//                    .findFirst();
//            if (!planOptional.isPresent()) {
//                return Result.error(String.format("未找到父级记录,id=%s", projectPlan.getId()));
//            }
//            projectPlan.setPid(planOptional.get().getId());
        }
        return Result.OK(projectPlanList);
    }

    @Override
    public Result<?> version() {
        LambdaQueryWrapper<ProjectPlan> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.groupBy(ProjectPlan::getVersion);
        List<ProjectPlan> projectPlans = baseMapper.selectList(queryWrapper);
        return Result.OK(projectPlans);
    }


    /**
     * 根据所传pid查询旧的父级节点的子节点并修改相应状态值
     *
     * @param pid
     */
    private void updateOldParentNode(String pid) {
        if (!IProjectPlanService.ROOT_PID_VALUE.equals(pid)) {
            Integer count = baseMapper.selectCount(new QueryWrapper<ProjectPlan>().eq("pid", pid));
            if (count == null || count <= 1) {
                baseMapper.updateTreeNodeStatus(pid, IProjectPlanService.NOCHILD);
            }
        }
    }


    /**
     * 递归查询节点的根节点
     *
     * @param pidVal
     * @return
     */
    private ProjectPlan getTreeRoot(String pidVal) {
        ProjectPlan data = baseMapper.selectById(pidVal);
        if (data != null && !"0".equals(data.getPid())) {
            return this.getTreeRoot(data.getPid());
        } else {
            return data;
        }
    }

    /**
     * 根据id查询所有子节点id
     *
     * @param ids
     * @return
     */
    private String queryTreeChildIds(String ids) {
        //获取id数组
        String[] idArr = ids.split(",");
        StringBuffer sb = new StringBuffer();
        for (String pidVal : idArr) {
            if (pidVal != null) {
                if (!sb.toString().contains(pidVal)) {
                    if (sb.toString().length() > 0) {
                        sb.append(",");
                    }
                    sb.append(pidVal);
                    this.getTreeChildIds(pidVal, sb);
                }
            }
        }
        return sb.toString();
    }

    /**
     * 递归查询所有子节点
     *
     * @param pidVal
     * @param sb
     * @return
     */
    private StringBuffer getTreeChildIds(String pidVal, StringBuffer sb) {
        List<ProjectPlan> dataList = baseMapper.selectList(new QueryWrapper<ProjectPlan>().eq("pid", pidVal));
        if (dataList != null && dataList.size() > 0) {
            for (ProjectPlan tree : dataList) {
                if (!sb.toString().contains(tree.getId())) {
                    sb.append(",").append(tree.getId());
                }
                this.getTreeChildIds(tree.getId(), sb);
            }
        }
        return sb;
    }

}
