package com.jinghe.breeze.modules.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghe.breeze.modules.base.entity.BaseGate;
import com.jinghe.breeze.modules.base.entity.BaseGateEquipment;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: base_gate
 * @Author: jeecg-boot
 * @Date: 2024-08-06
 * @Version: V1.0
 */
public interface IBaseGateService extends IService<BaseGate> {

    /**
     * 添加一对多
     */
    public void saveMain(BaseGate baseGate, List<BaseGateEquipment> baseGateEquipmentList);

    /**
     * 修改一对多
     */
    public void updateMain(BaseGate baseGate, List<BaseGateEquipment> baseGateEquipmentList);

    /**
     * 删除一对多
     */
    public void delMain(String id);

    /**
     * 批量删除一对多
     */
    public void delBatchMain(Collection<? extends Serializable> idList);

}
