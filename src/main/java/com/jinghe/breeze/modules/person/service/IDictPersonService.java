package com.jinghe.breeze.modules.person.service;

import com.jinghe.breeze.modules.person.entity.DictPersonItem;
import com.jinghe.breeze.modules.person.entity.DictPerson;
import com.baomidou.mybatisplus.extension.service.IService;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 数据字典-工种表
 * @Author: chenliang
 * @Date:   2022-08-31
 * @Version: V1.0
 */
public interface IDictPersonService extends IService<DictPerson> {

	/**
	 * 添加一对多
	 * 
	 */
	public void saveMain(DictPerson dictPerson,List<DictPersonItem> dictPersonItemList) ;
	
	/**
	 * 修改一对多
	 * 
	 */
	public void updateMain(DictPerson dictPerson,List<DictPersonItem> dictPersonItemList);
	
	/**
	 * 删除一对多
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);

}
