package com.jinghe.breeze.modules.project.service;

import com.jinghe.breeze.modules.project.entity.ProjectPbs;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.util.List;

/**
 * @Description: project_pbs
 * @Author: jeecg-boot
 * @Date: 2024-04-17
 * @Version: V1.0
 */
public interface IProjectPbsService extends IService<ProjectPbs> {

	/** 根节点父ID的值 */
	public static final String ROOT_PID_VALUE = "0";

	/** 树节点有子节点状态值 */
	public static final String HASCHILD = "1";

	/** 树节点无子节点状态值 */
	public static final String NOCHILD = "0";

	/** 新增节点 */
	void addProjectPbs(ProjectPbs projectPbs);

	/** 修改节点 */
	void updateProjectPbs(ProjectPbs projectPbs) throws JeecgBootException;

	/** 删除节点 */
	void deleteProjectPbs(String id) throws JeecgBootException;

	/** 查询所有数据，无分页 */
	List<ProjectPbs> queryTreeListNoPage(QueryWrapper<ProjectPbs> queryWrapper);

	void setCanMoveUpDown(List<ProjectPbs> pbsList, List<ProjectPbs> allList);

	List<ProjectPbs> buildTree(List<ProjectPbs> projectPbsList);

	Result<?> getBaseMapData();

	void movePbs(ProjectPbs pbs, boolean isUp);
}
