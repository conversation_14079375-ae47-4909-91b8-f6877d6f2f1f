package com.jinghe.breeze.modules.settlement.service;

import com.jinghe.breeze.modules.settlement.entity.ContractInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghe.breeze.modules.settlement.entity.ContractInfoDetail;
import org.jeecg.common.api.vo.Result;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: contract_info
 * @Author: jeecg-boot
 * @Date: 2024-05-31
 * @Version: V1.0
 */
public interface IContractInfoService extends IService<ContractInfo> {

    Result<?> addOrEdit(ContractInfo contractInfo);

    List<ContractInfoDetail> getDetails(String id);

    Result<?> removeWithDetailsById(String id);

    BigDecimal getSettledMoney(String contractId);

    List<ContractInfo> getSettledContractList(String code, String contractId);

//    void setSettledMoney(List<Object> contractInfoList);
    void setSettledMoney(ContractInfo contractInfo);
}
