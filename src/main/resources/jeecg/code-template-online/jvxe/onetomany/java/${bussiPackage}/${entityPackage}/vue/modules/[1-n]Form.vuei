<#include "/common/utils.ftl">
<#list subTables as sub>
<#if sub.foreignRelationType=='1'>
#segment#${sub.entityName}Form.vue
<template>
  <j-form-container :disabled="disabled">
      <a-form :form="form" slot="detail">
        <a-row>
<#assign form_cat_tree = false>
<#assign form_cat_back = "">
<#assign form_span = 24>
<#if tableVo.fieldRowNum == 2>
  <#assign form_span = 12>
<#elseif tableVo.fieldRowNum==3>
  <#assign form_span = 8>
<#elseif tableVo.fieldRowNum==4>
  <#assign form_span = 6>
</#if>
<#list sub.colums as po>
<#if po.isShow =='Y' && po.fieldName != 'id'>
<#assign form_field_dictCode="">
	<#if po.dictTable?default("")?trim?length gt 1 && po.dictText?default("")?trim?length gt 1 && po.dictField?default("")?trim?length gt 1>
		<#assign form_field_dictCode="${po.dictTable},${po.dictText},${po.dictField}">
	<#elseif po.dictField?default("")?trim?length gt 1>
		<#assign form_field_dictCode="${po.dictField}">
	</#if>
	<#if po.classType =='textarea'>
          <a-col :span="24">
            <a-form-item label="${po.filedComment}" :labelCol="labelCol2" :wrapperCol="wrapperCol2">
	<#else>
          <a-col :span="${form_span}">
            <a-form-item label="${po.filedComment}" :labelCol="labelCol" :wrapperCol="wrapperCol">
	</#if>
	<#if po.classType =='date'>
              <j-date placeholder="请选择${po.filedComment}" v-decorator="['${po.fieldName}'${autoWriteRules(po)}]" :trigger-change="true" style="width: 100%"/>
	<#elseif po.classType =='datetime'>
              <j-date placeholder="请选择${po.filedComment}" v-decorator="['${po.fieldName}'${autoWriteRules(po)}]" :trigger-change="true" :show-time="true" date-format="YYYY-MM-DD HH:mm:ss" style="width: 100%"/>
	<#elseif po.classType =='time'>
              <j-time placeholder="请选择${po.filedComment}" v-decorator="['${po.fieldName}'${autoWriteRules(po)}]" :trigger-change="true" style="width: 100%" <#if po.readonly=='Y'>disabled</#if>/>
	<#elseif po.classType =='popup'>
              <j-popup
                v-decorator="['${po.fieldName}'${autoWriteRules(po)}]"
                :trigger-change="true"
                org-fields="${po.dictField}"
                dest-fields="${Format.underlineToHump(po.dictText)}"
                code="${po.dictTable}"
                @callback="popupCallback"/>
    <#elseif po.classType =='sel_depart'>
              <j-select-depart v-decorator="['${po.fieldName}'${autoWriteRules(po)}]" multi/>
<#elseif po.classType =='switch'>
              <j-switch v-decorator="['${po.fieldName}'${autoWriteRules(po)}]" <#if po.dictField?default("")?trim?length gt 1>:options="${po.dictField}"</#if>></j-switch>
	<#elseif po.classType =='pca'>
		      <j-area-linkage type="cascader" v-decorator="['${po.fieldName}'${autoWriteRules(po)}]" placeholder="请输入省市区"/>
	<#elseif po.classType =='markdown'>
	          <j-markdown-editor v-decorator="[${autoStringSuffix(po)},{initialValue:''}]" id="${po.fieldName}"></j-markdown-editor>
    <#elseif po.classType =='password'>
	          <a-input-password v-decorator="['${po.fieldName}'${autoWriteRules(po)}]" placeholder="请输入${po.filedComment}"/>
	<#elseif po.classType =='sel_user'>
              <j-select-user-by-dep v-decorator="['${po.fieldName}'${autoWriteRules(po)}]"/>
	<#elseif po.classType =='textarea'>
              <a-textarea v-decorator="[${autoStringSuffix(po)}${autoWriteRules(po)}]" rows="4" placeholder="请输入${po.filedComment}"/>
	<#elseif po.classType=='list' || po.classType=='radio'>
              <j-dict-select-tag type="${po.classType}" v-decorator="['${po.fieldName}'${autoWriteRules(po)}]" :trigger-change="true" dictCode="${form_field_dictCode}" placeholder="请选择${po.filedComment}"/>
	<#elseif po.classType=='list_multi' || po.classType=='checkbox'>
              <j-multi-select-tag type="${po.classType}" v-decorator="['${po.fieldName}'${autoWriteRules(po)}]" :trigger-change="true" dictCode="${form_field_dictCode}" placeholder="请选择${po.filedComment}"/>
	<#elseif po.classType=='sel_search'>
              <j-search-select-tag v-decorator="['${po.fieldName}'${autoWriteRules(po)}]" dict="${form_field_dictCode}" />
    <#elseif po.classType=='cat_tree'>
    	<#assign form_cat_tree = true>
              <j-category-select v-decorator="['${po.fieldName}'${autoWriteRules(po)}]" pcode="${po.dictField?default("")}" placeholder="请选择${po.filedComment}" <#if po.dictText?default("")?trim?length gt 1>back="${po.dictText}" @change="handleCategoryChange"</#if>/>
    	<#if po.dictText?default("")?trim?length gt 1>
    	<#assign form_cat_back = "${po.dictText}">
    	</#if>
	<#elseif po.fieldDbType=='int' || po.fieldDbType=='double' || po.fieldDbType=='BigDecimal'>
              <a-input-number v-decorator="['${po.fieldName}'${autoWriteRules(po)}]" placeholder="请输入${po.filedComment}" style="width: 100%"/>
	<#elseif po.classType=='file'>
              <j-upload v-decorator="['${po.fieldName}'${autoWriteRules(po)}]" :trigger-change="true"></j-upload>
	<#elseif po.classType=='image'>
              <j-image-upload isMultiple v-decorator="['${po.fieldName}'${autoWriteRules(po)}]"></j-image-upload>
	<#elseif po.classType=='umeditor'>
              <j-editor v-decorator="[${autoStringSuffix(po)},{trigger:'input'}]"/>
	<#else>
              <a-input v-decorator="[${autoStringSuffix(po)}${autoWriteRules(po)}]" placeholder="请输入${po.filedComment}"></a-input>
    </#if>
            </a-form-item>
            <#if form_cat_tree && form_cat_back?length gt 1>
            <a-form-item v-show="false">
              <a-input v-decorator="['${form_cat_back}']"></a-input>
            </a-form-item>
            </#if>
          </a-col>
</#if>
</#list>
        </a-row>
      </a-form>
  </j-form-container>
</template>
<script>
  import pick from 'lodash.pick'
  import { getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'
  import JFormContainer from '@/components/jeecg/JFormContainer'

  export default {
    name: '${sub.entityName}Form',
    components: {
      JFormContainer
    },
    props:{
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        form: this.$form.createForm(this),
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 6 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        labelCol2: {
          xs: { span: 24 },
          sm: { span: 3 },
        },
        wrapperCol2: {
          xs: { span: 24 },
          sm: { span: 20 },
        },
        <#include "/common/validatorRulesTemplate/sub.ftl">
        confirmLoading: false,
      }
    },
    methods:{
      initFormData(url,id){
        this.clearFormData()
        if(!id){
          this.edit({})
        }else{
          getAction(url,{id:id}).then(res=>{
            if(res.success){
              let records = res.result
              if(records && records.length>0){
                this.edit(records[0])
              }
            }
          })
        }
      },
      edit(record){
        this.model = Object.assign({}, record)
        console.log("${sub.entityName}Form-edit",this.model);
        let fieldval = pick(this.model<#list sub.colums as po><#if po.fieldName !='id'>,${autoStringSuffix(po)}</#if></#list>)
        this.$nextTick(() => {
          this.form.setFieldsValue(fieldval)
        })
      },
      getFormData(){
        let formdata_arr = []
        this.form.validateFields((err, values) => {
          if (!err) {
            let formdata = Object.assign(this.model, values)
            let isNullObj = true
            Object.keys(formdata).forEach(key=>{
              if(formdata[key]){
                isNullObj = false
              }
            })
            if(!isNullObj){
              formdata_arr.push(formdata)
            }
          }else{
            this.$emit("validateError","${sub.ftlDescription}表单校验未通过");
          }
        })
        console.log("${sub.ftlDescription}表单数据集",formdata_arr);
        return formdata_arr;
      },
      popupCallback(row){
        this.form.setFieldsValue(pick(row<#list sub.colums as po><#if po.fieldName !='id'>,${autoStringSuffix(po)}</#if></#list>))
      },
      clearFormData(){
        this.form.resetFields()
        this.model={}
      },
      <#if form_cat_tree>
      handleCategoryChange(value,backObj){
        this.form.setFieldsValue(backObj);
      }
      </#if>
    }
  }
</script>
</#if>
</#list>
