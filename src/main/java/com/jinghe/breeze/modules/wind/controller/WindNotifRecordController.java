package com.jinghe.breeze.modules.wind.controller;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.modules.wind.entity.WindNotification;
import com.jinghe.breeze.modules.wind.service.IWindNotificationService;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.jinghe.breeze.modules.wind.entity.WindNotifRecord;
import com.jinghe.breeze.modules.wind.service.IWindNotifRecordService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: wind_notif_record
 * @Author: jeecg-boot
 * @Date: 2024-05-23
 * @Version: V1.0
 */
@Api(tags = "wind_notif_record")
@RestController
@RequestMapping("/wind/windNotifRecord")
@Slf4j
public class WindNotifRecordController extends JeecgController<WindNotifRecord, IWindNotifRecordService> {
    @Autowired
    private IWindNotifRecordService windNotifRecordService;

    @Autowired
    private IWindNotificationService windNotificationService;

    /**
     * 分页列表查询
     *
     * @param windNotifRecord
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "wind_notif_record-分页列表查询")
    @ApiOperation(value = "wind_notif_record-分页获取当前登录用户避风待办列表", notes = "wind_notif_record-分页获取当前登录用户避风待办列表")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(WindNotifRecord windNotifRecord,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userId = sysUser.getId();
        QueryWrapper<WindNotifRecord> queryWrapper = QueryGenerator.initQueryWrapper(windNotifRecord, req.getParameterMap());
        queryWrapper.eq("del_flag", Common.delete_flag.OK)
                .eq("user_id", userId);
        Page<WindNotifRecord> page = new Page<WindNotifRecord>(pageNo, pageSize);
        IPage<WindNotifRecord> pageList = windNotifRecordService.page(page, queryWrapper);
        List<String> idList = pageList.getRecords().stream().map(WindNotifRecord::getNotificationId).collect(Collectors.toList());
        List<WindNotification> notificationList = windNotificationService.list(new LambdaQueryWrapper<WindNotification>()
                .in(WindNotification::getId, idList));
        for (WindNotifRecord record : pageList.getRecords()) {
            notificationList.stream().filter(notification -> notification.getId()
                    .equals(record.getNotificationId())).findFirst().ifPresent(record::setWindNotification);
        }
        return Result.OK(pageList);
    }

    @ApiOperation(value = "wind_notif_record-根据用户名获取未失效的避风记录", notes = "wind_notif_record-根据用户名获取未失效的避风记录")
    @GetMapping(value = "/getListByLoginUser")
    public Result<?> getListByLoginUser() {
        return Result.OK(windNotifRecordService.getListByLoginUser());
    }

    /**
     * 添加
     *
     * @param windNotifRecord
     * @return
     */
    @AutoLog(value = "wind_notif_record-添加")
    @ApiOperation(value = "wind_notif_record-添加", notes = "wind_notif_record-添加")
//	@RequiresPermissions("windNotifRecord:add")
    @PostMapping(value = "/add")
    public Result<?> add(@Validated @RequestBody WindNotifRecord windNotifRecord) {
        windNotifRecordService.save(windNotifRecord);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param windNotifRecord
     * @return
     */
    @AutoLog(value = "wind_notif_record-编辑")
    @ApiOperation(value = "wind_notif_record-编辑", notes = "wind_notif_record-编辑")
//	@RequiresPermissions("windNotifRecord:edit")
    @PutMapping(value = "/edit")
    public Result<?> edit(@Validated @RequestBody WindNotifRecord windNotifRecord) {
        windNotifRecordService.updateById(windNotifRecord);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "wind_notif_record-通过id删除")
    @ApiOperation(value = "wind_notif_record-通过id删除", notes = "wind_notif_record-通过id删除")
//	@RequiresPermissions("windNotifRecord:delete")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        windNotifRecordService.update(new LambdaUpdateWrapper<WindNotifRecord>()
                .eq(WindNotifRecord::getId, id)
                .set(WindNotifRecord::getDelFlag, Common.delete_flag.ERROR));
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "wind_notif_record-批量删除")
    @ApiOperation(value = "wind_notif_record-批量删除", notes = "wind_notif_record-批量删除")
//	@RequiresPermissions("windNotifRecord:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.windNotifRecordService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "wind_notif_record-通过id查询")
    @ApiOperation(value = "wind_notif_record-通过id查询", notes = "wind_notif_record-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        WindNotifRecord windNotifRecord = windNotifRecordService.getById(id);
        if (windNotifRecord == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(windNotifRecord);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param windNotifRecord
     */
//    @RequiresPermissions("windNotifRecord:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WindNotifRecord windNotifRecord) {
        return super.exportXls(request, windNotifRecord, WindNotifRecord.class, "wind_notif_record");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
//    @RequiresPermissions("windNotifRecord:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WindNotifRecord.class);
    }

}
