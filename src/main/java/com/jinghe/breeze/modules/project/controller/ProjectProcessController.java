package com.jinghe.breeze.modules.project.controller;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jodd.util.StringUtil;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.jinghe.breeze.modules.project.entity.ProjectProcess;
import com.jinghe.breeze.modules.project.service.IProjectProcessService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: project_process
 * @Author: jeecg-boot
 * @Date: 2024-04-11
 * @Version: V1.0
 */
@Api(tags = "project_process")
@RestController
@RequestMapping("/project/projectProcess")
@Slf4j
public class ProjectProcessController extends JeecgController<ProjectProcess, IProjectProcessService> {
    @Autowired
    private IProjectProcessService projectProcessService;

    /**
     * 分页列表查询
     *
     * @param projectProcess
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "project_process-分页列表查询")
    @ApiOperation(value = "project_process-分页列表查询", notes = "project_process-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(ProjectProcess projectProcess,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<ProjectProcess> queryWrapper = QueryGenerator.initQueryWrapper(projectProcess, req.getParameterMap());
        Page<ProjectProcess> page = new Page<ProjectProcess>(pageNo, pageSize);
        IPage<ProjectProcess> pageList = projectProcessService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param projectProcess
     * @return
     */
    @AutoLog(value = "project_process-查询当前记录最大权重")
    @ApiOperation(value = "project_process-查询当前记录最大权重", notes = "project_process-查询当前记录最大权重")
    @PostMapping(value = "/queryMaxWeight")
    public Result<?> getMaxWeight(@RequestBody ProjectProcess projectProcess) {
        Integer myWeight = 0;
        if (projectProcess.getId() != null) {
            projectProcess = projectProcessService.getById(projectProcess.getId());
            if (projectProcess == null) {
                return Result.error("未找到对应数据");
            }
            myWeight = projectProcessService.getById(projectProcess.getId()).getWeight();
        }
        String type = projectProcess.getType();
        if (StringUtil.isEmpty(type)) {
            return Result.error("需要传入type");
        }
        double totalWeight = projectProcessService.list(new LambdaQueryWrapper<ProjectProcess>()
                        .eq(ProjectProcess::getType, type))
                .stream().mapToDouble(ProjectProcess::getWeight).sum();
        totalWeight = 100 - (totalWeight - myWeight);
        return Result.OK(totalWeight);
    }

    /**
     * 添加
     *
     * @param projectProcess
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @AutoLog(value = "project_process-添加")
    @ApiOperation(value = "project_process-添加", notes = "project_process-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody ProjectProcess projectProcess) {

        String type = projectProcess.getType();
        LambdaQueryWrapper<ProjectProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectProcess::getWorkContent, projectProcess.getWorkContent());
        queryWrapper.eq(ProjectProcess::getType,projectProcess.getType());
        if (projectProcessService.list(queryWrapper).size() > 0) {
            return Result.error("工序名称重复!");
        }
        queryWrapper.clear();
        queryWrapper.eq(ProjectProcess::getType,projectProcess.getType());
        queryWrapper.eq(ProjectProcess::getProcessCode, projectProcess.getProcessCode());
        if (projectProcessService.list(queryWrapper).size() > 0) {
            return Result.error("工序编码重复!");
        }
        queryWrapper.clear();
        queryWrapper.eq(ProjectProcess::getType, type);//筛选同type的所有记录
        List<ProjectProcess> projectProcessList = projectProcessService.list(queryWrapper);
        if (projectProcess.getWeight() < 0) {
            return Result.error("权重不能为负数!");
        }
        double totalWeight = projectProcessList.stream().mapToDouble(ProjectProcess::getWeight).sum();
        totalWeight = totalWeight + projectProcess.getWeight(); //加上自身
        if (totalWeight > 100) {
            return Result.error("同类型下权重总和不能超过100!");
        }
        if (projectProcess.getIsProgress().equals(1)) {
            //查找projectProcessList 中setIsProgress ==1 的项
            List<ProjectProcess> isProgressList = projectProcessList.stream().filter(p -> p.getIsProgress().equals(1)).collect(Collectors.toList());
            if (isProgressList.size() > 0 && !isProgressList.get(0).getId().equals(projectProcess.getId())) {
                //本类下已有 setIsProgress ==1 的项，且不是本条记录
                return Result.error("本分类下已有完工标志的的工序,提交失败");
            }
        }
        projectProcessService.save(projectProcess);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param projectProcess
     * @return
     */
    @Transactional
    @AutoLog(value = "project_process-编辑")
    @ApiOperation(value = "project_process-编辑", notes = "project_process-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody ProjectProcess projectProcess) {
        //此处需要校验 一个type下必须只有一个is_progress==1  且权重总和不能超过100
        String id = projectProcess.getId();
        String type = projectProcess.getType();
        LambdaQueryWrapper<ProjectProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectProcess::getType, type);//筛选同type的所有记录
        List<ProjectProcess> projectProcessList = projectProcessService.list(queryWrapper);
        double totalWeight = projectProcessList.stream().filter(p -> !p.getId().equals(id)).mapToDouble(ProjectProcess::getWeight).sum();
        totalWeight = totalWeight + projectProcess.getWeight();
        if (totalWeight > 100) {
            return Result.error("同类型下权重总和不能超过100!");
        }
        if (projectProcess.getIsProgress().equals(1)) {
            //查找projectProcessList 中setIsProgress ==1 的项
            List<ProjectProcess> isProgressList = projectProcessList.stream().filter(p -> p.getIsProgress().equals(1)).collect(Collectors.toList());
            if (isProgressList.size() > 0 && !isProgressList.get(0).getId().equals(projectProcess.getId())) {
                //本类下已有 setIsProgress ==1 的项，且不是本条记录
                return Result.error("本分类下已有完工标志的的工序,提交失败");
            }
        }
        projectProcessService.updateById(projectProcess);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "project_process-通过id删除")
    @ApiOperation(value = "project_process-通过id删除", notes = "project_process-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        projectProcessService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "project_process-批量删除")
    @ApiOperation(value = "project_process-批量删除", notes = "project_process-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.projectProcessService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "project_process-通过id查询")
    @ApiOperation(value = "project_process-通过id查询", notes = "project_process-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        ProjectProcess projectProcess = projectProcessService.getById(id);
        if (projectProcess == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(projectProcess);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param projectProcess
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ProjectProcess projectProcess) {
        return super.exportXls(request, projectProcess, ProjectProcess.class, "project_process");
    }

    /**
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ProjectProcess.class);
    }


    /**
     * 获取所有工序
     * @return
     */
    @AutoLog(value = "获取所有工序")
    @ApiOperation(value = "获取所有工序", notes = "project_process-分页列表查询")
    @GetMapping(value = "/listAll")
    public Result<?> listAll() {
        return projectProcessService.listAll();
    }

}
