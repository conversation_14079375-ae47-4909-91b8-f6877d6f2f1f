package com.jinghe.breeze.modules.quality.mapper;

import java.util.List;
import com.jinghe.breeze.modules.quality.entity.QualityEvaluationInformationDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 验评资料详情
 * @Author: jeecg-boot
 * @Date:   2025-06-27
 * @Version: V1.0
 */
public interface QualityEvaluationInformationDetailMapper extends BaseMapper<QualityEvaluationInformationDetail> {

	public boolean deleteByMainId(@Param("mainId") String mainId);
    
	public List<QualityEvaluationInformationDetail> selectByMainId(@Param("mainId") String mainId);
}
