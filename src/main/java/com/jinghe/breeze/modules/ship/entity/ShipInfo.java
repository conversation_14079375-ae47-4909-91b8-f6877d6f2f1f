package com.jinghe.breeze.modules.ship.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: ship_info
 * @Author: jeecg-boot
 * @Date: 2024-04-19
 * @Version: V1.0
 */
@Data
@TableName("ship_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ship_info对象", description = "ship_info")
public class ShipInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 标识
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "标识")
    private java.lang.String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
    /**
     * 删除标识
     */
    @Excel(name = "删除标识", width = 15)
    @ApiModelProperty(value = "删除标识")
    private java.lang.Integer delFlag;
    /**
     * 组织机构编码
     */
    @ApiModelProperty(value = "组织机构编码")
    private java.lang.String sysOrgCode;
    /**
     * 项目id
     */
    @Excel(name = "项目id", width = 15)
    @ApiModelProperty(value = "项目id")
    private java.lang.String projectId;
    /**
     * 关联船舶信息库id
     */
    @Excel(name = "关联船舶信息库id", width = 15)
    @ApiModelProperty(value = "关联船舶信息库id")
    private java.lang.String shipDataId;
    /**
     * 船东
     */
    @Excel(name = "船东", width = 15)
    @ApiModelProperty(value = "船东")
    private java.lang.String shipOwner;
    /**
     * 船舶名称
     */
    @Excel(name = "船舶名称", width = 15)
    @ApiModelProperty(value = "船舶名称")
    private java.lang.String name;
    /**
     * 船舶类型
     */
    @Excel(name = "船舶类型", width = 15)
    @ApiModelProperty(value = "船舶类型")
    private java.lang.String typeValue;
    /**
     * mmsi
     */
    @Excel(name = "mmsi", width = 15)
    @ApiModelProperty(value = "mmsi")
    private java.lang.String mmsi;
    /**
     * 在/离场状态
     */

    @TableField(exist = false)
    @Excel(name = "在/离场状态", width = 15)
    @ApiModelProperty(value = "在/离场状态")
    private java.lang.String status;
    /**
     * 开启出航预警
     */
    @Excel(name = "开启出航预警", width = 15)
    @ApiModelProperty(value = "开启出航预警")
    private java.lang.Integer isAlarm;
    @Excel(name = "information", width = 15)
    @ApiModelProperty(value = "information")
    private java.lang.String information;
}
