package com.jinghe.breeze.modules.project.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.jinghe.breeze.modules.project.entity.ProjectUnit;
import com.jinghe.breeze.modules.project.service.IProjectUnitService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: project_unit
 * @Author: jeecg-boot
 * @Date:   2024-05-07
 * @Version: V1.0
 */
@Api(tags="project_unit")
@RestController
@RequestMapping("/project/projectUnit")
@Slf4j
public class ProjectUnitController extends JeecgController<ProjectUnit, IProjectUnitService> {
	@Autowired
	private IProjectUnitService projectUnitService;
	
	/**
	 * 分页列表查询
	 *
	 * @param projectUnit
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "project_unit-分页列表查询")
	@ApiOperation(value="project_unit-分页列表查询", notes="project_unit-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(ProjectUnit projectUnit,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<ProjectUnit> queryWrapper = QueryGenerator.initQueryWrapper(projectUnit, req.getParameterMap());
		queryWrapper.orderByAsc("code");
		Page<ProjectUnit> page = new Page<ProjectUnit>(pageNo, pageSize);
		IPage<ProjectUnit> pageList = projectUnitService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	 /**
	  * 无分页查询倒序
	  */
	 @ApiOperation(value = "project_unit-无分页查询倒序", notes = "project_unit-无分页查询倒序")
	 @GetMapping(value = "/listNoPage")
	 public Result<?> listNoPage() {
		 List<ProjectUnit> list = projectUnitService.listNoPage();
		 Page<ProjectUnit> page = new Page<ProjectUnit>(1, list.size());
		 page.setRecords(list);
		 return Result.OK(page);
	 }
	
	/**
	 *   添加
	 *
	 * @param projectUnit
	 * @return
	 */
	@AutoLog(value = "project_unit-添加")
	@ApiOperation(value="project_unit-添加", notes="project_unit-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody ProjectUnit projectUnit) {
		projectUnitService.save(projectUnit);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param projectUnit
	 * @return
	 */
	@AutoLog(value = "project_unit-编辑")
	@ApiOperation(value="project_unit-编辑", notes="project_unit-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody ProjectUnit projectUnit) {
		projectUnitService.updateById(projectUnit);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "project_unit-通过id删除")
	@ApiOperation(value="project_unit-通过id删除", notes="project_unit-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		projectUnitService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "project_unit-批量删除")
	@ApiOperation(value="project_unit-批量删除", notes="project_unit-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.projectUnitService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "project_unit-通过id查询")
	@ApiOperation(value="project_unit-通过id查询", notes="project_unit-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		ProjectUnit projectUnit = projectUnitService.getById(id);
		if(projectUnit==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(projectUnit);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param projectUnit
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ProjectUnit projectUnit) {
        return super.exportXls(request, projectUnit, ProjectUnit.class, "project_unit");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ProjectUnit.class);
    }

}
