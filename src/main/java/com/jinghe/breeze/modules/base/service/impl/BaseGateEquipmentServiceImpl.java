package com.jinghe.breeze.modules.base.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghe.breeze.modules.base.entity.BaseGateEquipment;
import com.jinghe.breeze.modules.base.mapper.BaseGateEquipmentMapper;
import com.jinghe.breeze.modules.base.service.IBaseGateEquipmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: base_gate_equipment
 * @Author: jeecg-boot
 * @Date: 2024-08-06
 * @Version: V1.0
 */
@Service
public class BaseGateEquipmentServiceImpl extends ServiceImpl<BaseGateEquipmentMapper, BaseGateEquipment> implements IBaseGateEquipmentService {

    @Autowired
    private BaseGateEquipmentMapper baseGateEquipmentMapper;

    @Override
    public List<BaseGateEquipment> selectByMainId(String mainId) {
        return baseGateEquipmentMapper.selectByMainId(mainId);
    }
}
