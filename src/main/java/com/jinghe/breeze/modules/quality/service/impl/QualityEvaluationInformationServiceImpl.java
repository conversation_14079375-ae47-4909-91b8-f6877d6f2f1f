package com.jinghe.breeze.modules.quality.service.impl;

import com.jinghe.breeze.modules.quality.entity.QualityEvaluationInformation;
import com.jinghe.breeze.modules.quality.entity.QualityEvaluationInformationDetail;
import com.jinghe.breeze.modules.quality.mapper.QualityEvaluationInformationDetailMapper;
import com.jinghe.breeze.modules.quality.mapper.QualityEvaluationInformationMapper;
import com.jinghe.breeze.modules.quality.service.IQualityEvaluationInformationService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;
import java.util.Collection;

/**
 * @Description: 验评资料
 * @Author: jeecg-boot
 * @Date:   2025-06-27
 * @Version: V1.0
 */
@Service
public class QualityEvaluationInformationServiceImpl extends ServiceImpl<QualityEvaluationInformationMapper, QualityEvaluationInformation> implements IQualityEvaluationInformationService {

	@Autowired
	private QualityEvaluationInformationMapper qualityEvaluationInformationMapper;
	@Autowired
	private QualityEvaluationInformationDetailMapper qualityEvaluationInformationDetailMapper;
	
	@Override
	@Transactional
	public void saveMain(QualityEvaluationInformation qualityEvaluationInformation, List<QualityEvaluationInformationDetail> qualityEvaluationInformationDetailList) {
		qualityEvaluationInformationMapper.insert(qualityEvaluationInformation);
		if(qualityEvaluationInformationDetailList!=null && qualityEvaluationInformationDetailList.size()>0) {
			for(QualityEvaluationInformationDetail entity:qualityEvaluationInformationDetailList) {
				//外键设置
				entity.setMainId(qualityEvaluationInformation.getId());
				qualityEvaluationInformationDetailMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional
	public void updateMain(QualityEvaluationInformation qualityEvaluationInformation,List<QualityEvaluationInformationDetail> qualityEvaluationInformationDetailList) {
		qualityEvaluationInformationMapper.updateById(qualityEvaluationInformation);
		
		//1.先删除子表数据
		qualityEvaluationInformationDetailMapper.deleteByMainId(qualityEvaluationInformation.getId());
		
		//2.子表数据重新插入
		if(qualityEvaluationInformationDetailList!=null && qualityEvaluationInformationDetailList.size()>0) {
			for(QualityEvaluationInformationDetail entity:qualityEvaluationInformationDetailList) {
				//外键设置
				entity.setMainId(qualityEvaluationInformation.getId());
				qualityEvaluationInformationDetailMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional
	public void delMain(String id) {
		qualityEvaluationInformationDetailMapper.deleteByMainId(id);
		qualityEvaluationInformationMapper.deleteById(id);
	}

	@Override
	@Transactional
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			qualityEvaluationInformationDetailMapper.deleteByMainId(id.toString());
			qualityEvaluationInformationMapper.deleteById(id);
		}
	}
	
}
