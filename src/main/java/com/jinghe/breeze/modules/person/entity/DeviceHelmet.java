package com.jinghe.breeze.modules.person.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 设备信息表
 * @Author:
 * @Date: 2022-08-09
 * @Version: V1.0
 */
@ApiModel(value = "device_helmet对象", description = "安全帽设备")
@Data
@TableName("device_helmet")
public class DeviceHelmet implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
    /**
     * 设备名称
     */

    @ApiModelProperty(value = "删除标识")
    private String delFlag;

    @Excel(name = "设备名称", width = 15)
    @ApiModelProperty(value = "设备名称")
    private java.lang.String deviceName;
    /**
     * 设备编号
     */
    @Excel(name = "设备编号", width = 15)
    @ApiModelProperty(value = "设备编号")
    private java.lang.String deviceCode;
    /**
     * 设备定位数据上传间隔
     */
    @Excel(name = "设备定位数据上传间隔", width = 15)
    @ApiModelProperty(value = "设备定位数据上传间隔")
    private java.lang.Integer gap;
    /**
     * 设备电量
     */
    @Excel(name = "设备电量", width = 15)
    @ApiModelProperty(value = "设备电量")
    private java.lang.String electricity;
    /**
     * 网络状态
     */
    @Excel(name = "网络状态", width = 15, dicCode = "network_status")
    @Dict(dicCode = "network_status")
    @ApiModelProperty(value = "网络状态")
    private java.lang.Integer isOnline;
    /**
     * 使用状态
     */
    @Excel(name = "使用状态", width = 15, dicCode = "use_status")
    @Dict(dicCode = "use_status")
    @ApiModelProperty(value = "使用状态")
    @TableField(exist = false)
    private java.lang.Integer isUsed;

    /**
     * 绑定人
     */
    @TableField(exist = false)
    @Dict(dictTable = "person_info", dicText = "name", dicCode = "id")
    private java.lang.String bindUserId;
    /**
     * 上次定位时间
     */
    @TableField(exist = false)
    private java.util.Date lastTime;
    /**
     * 经纬度
     */
    @TableField(exist = false)
    private java.lang.String lastLocation;


}
