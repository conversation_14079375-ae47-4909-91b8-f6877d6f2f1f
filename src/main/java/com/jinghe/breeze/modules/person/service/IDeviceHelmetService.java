package com.jinghe.breeze.modules.person.service;

import com.jinghe.breeze.modules.person.entity.DeviceHelmet;
import com.jinghe.breeze.modules.person.dto.LocationDataDTO;
import com.jinghe.breeze.modules.person.dto.EnrichedLocationDataDTO;
import com.baomidou.mybatisplus.extension.service.IService;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 设备信息表
 * @Author: chenliang
 * @Date:   2022-08-09
 * @Version: V1.0
 */
public interface IDeviceHelmetService extends IService<DeviceHelmet> {

	void deviceBootable();

	/**
	 * 接收设备位置数据并存储到Redis
	 * @param locationDataList 位置数据列表
	 */
	void receiveLocationData(List<LocationDataDTO> locationDataList);

	/**
	 * 获取所有人员的最新位置信息
	 * @return 增强的位置数据列表
	 */
	List<EnrichedLocationDataDTO> getAllPersonnelLocations();

	/**
	 * 刷新围栏缓存
	 * 当围栏数据更新时调用此方法立即刷新缓存
	 */
	void refreshFenceCache();
}
