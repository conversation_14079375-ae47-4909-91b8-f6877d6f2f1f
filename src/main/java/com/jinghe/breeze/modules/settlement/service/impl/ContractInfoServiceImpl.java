package com.jinghe.breeze.modules.settlement.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.modules.settlement.entity.ContractInfo;
import com.jinghe.breeze.modules.settlement.entity.ContractInfoDetail;
import com.jinghe.breeze.modules.settlement.entity.Settlement;
import com.jinghe.breeze.modules.settlement.mapper.ContractInfoMapper;
import com.jinghe.breeze.modules.settlement.service.IContractInfoDetailService;
import com.jinghe.breeze.modules.settlement.service.IContractInfoService;
import com.jinghe.breeze.modules.settlement.service.ISettlementService;
import jodd.util.StringUtil;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: contract_info
 * @Author: jeecg-boot
 * @Date: 2024-05-31
 * @Version: V1.0
 */
@Service
public class ContractInfoServiceImpl extends ServiceImpl<ContractInfoMapper, ContractInfo> implements IContractInfoService {
    @Autowired
    private IContractInfoDetailService contractInfoDetailService;

    @Autowired
    private IContractInfoService contractInfoService;

    @Autowired
    private ISettlementService settlementService;

    @Transactional(rollbackFor = Exception.class)
    public Result<?> addOrEdit(@Validated @RequestBody ContractInfo contractInfo) {
        List<ContractInfoDetail> details = contractInfo.getChildren();
        String sucMsg;
        contractInfo.setHasChild(details.size() > 0 ? 1 : 0);
        contractInfo.setPid("0"); //设置父节点和是否有子节点
        if (contractInfo.getId() == null) {
            sucMsg = "添加成功";
            baseMapper.insert(contractInfo);
        } else {
            sucMsg = "修改成功";
            baseMapper.updateById(contractInfo);
        }
        String contractId = contractInfo.getId();
        if (details.size() == 0) {
            return Result.OK(sucMsg);
        }
        details.forEach(p -> {
            p.setContractId(contractId);
            String code = p.getCode();
            if (ObjectUtil.isEmpty(code)) {
                throw new RuntimeException("编码不能为空");
            }
            code = code.replaceAll(" ", "");
            if (code.length() == 0) {
                throw new RuntimeException("编码不能为空");
            }
            p.setCode(code);

            p.setLayer(code.split("\\.").length - 1);
        });
        contractInfoDetailService.saveOrUpdateBatch(details);
        Map<String, ContractInfoDetail> detailMap = details.stream().collect(Collectors.toMap(ContractInfoDetail::getCode, p -> p));
        details.forEach(p -> {
            p.setHasChild(0);
        });
        details.forEach(p -> {
            //更具code 获取 p的pid
            if (p.getLayer() == 0) {
                p.setPid(p.getContractId());
            } else {
                String code = p.getCode();
                String parentCode = code.substring(0, code.lastIndexOf("."));
                ContractInfoDetail parent = detailMap.get(parentCode);
                if (parent != null) {
                    p.setPid(parent.getId());
                    parent.setHasChild(1);
                }
            }
        });
        contractInfoDetailService.saveOrUpdateBatch(details);
        return Result.OK(sucMsg);
    }

    public List<ContractInfoDetail> getDetails(String contractId) {
        return contractInfoDetailService.list(new LambdaQueryWrapper<ContractInfoDetail>().eq(ContractInfoDetail::getContractId, contractId));
    }


    @Transactional(rollbackFor = Exception.class)
    public Result<?> removeWithDetailsById(String id) {
        baseMapper.deleteById(id);
        List<String> ids = contractInfoDetailService.list(new LambdaQueryWrapper<ContractInfoDetail>()
                        .eq(ContractInfoDetail::getContractId, id))
                .stream().map(ContractInfoDetail::getId).collect(Collectors.toList());
        String msg = contractInfoDetailService.removeByIds(ids) ? "删除成功" : "删除失败";
        return Result.OK(msg);
    }


    public BigDecimal getSettledMoney(String contractId) {
        BigDecimal total = settlementService.list(new LambdaQueryWrapper<Settlement>()
                        .eq(Settlement::getContractId, contractId)
                        .select(Settlement::getCurrentSettlement))//过滤字段
                .stream()
                .map(Settlement::getCurrentSettlement).reduce(BigDecimal.ZERO, BigDecimal::add);
        return total;
    }


    private List<Object> buildTree(List<ContractInfo> contractInfoList, List<ContractInfoDetail> contractInfoDetailList) {
        List<Object> ret = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        contractInfoDetailList.forEach(p -> {
            p.setChildren(new ArrayList<>());
            p.setHasChild(0);
            map.put(p.getId(), p);
        });
        contractInfoList.forEach(p -> {
            p.setPid("0");
            p.setHasChild(1);
            p.setChildren(new ArrayList<>());
            map.put(p.getId(), p);
            ret.add(p);//根节点
        });
        List<ContractInfoDetail> selected = new ArrayList<>();
        for (ContractInfoDetail detail : contractInfoDetailList) {
            Object p = map.getOrDefault(detail.getPid(), null);
            if (p == null) {
                continue;
            }
            if (p.getClass() == ContractInfo.class) {
                ((ContractInfo) p).setHasChild(1);
                ((ContractInfo) p).getChildren().add(detail);
                continue;
            }
            if (p.getClass() == ContractInfoDetail.class) {
                ((ContractInfoDetail) p).getChildren().add(detail);
                ((ContractInfoDetail) p).setHasChild(1);
                selected.add((ContractInfoDetail) p);
            }
        }
        contractInfoDetailService.setSettledMoney(selected);
        return ret;
    }

    private List<ContractInfoDetail> buildTree(List<ContractInfoDetail> details) {
        Map<String, ContractInfoDetail> map = details.stream().collect(Collectors.toMap(ContractInfoDetail::getId, p -> {
            p.setHasChild(0);
            return p;
        }));
        List<ContractInfoDetail> retList = new ArrayList<>();
        details.forEach(p -> {
            if (StringUtil.isEmpty(p.getPid())) {
                return;
            }
            ContractInfoDetail parent = map.get(p.getPid());
            if (parent == null) {
                retList.add(p);//根节点
                return;
            }
            if (parent.getChildren() == null) {
                parent.setChildren(new ArrayList<>());
                parent.setHasChild(1);
                parent.getChildren().add(p);
                return;
            }
            parent.getChildren().add(p);
            parent.setHasChild(1);
        });
        return retList;
    }

    private ContractInfo buildTree(ContractInfo contractInfo, List<Settlement> settlementList) {
        String contractId = contractInfo.getId();
        List<Settlement> filterSettlementList = settlementList.stream()
                .filter(p -> p.getContractId().equals(contractId)).collect(Collectors.toList());
        List<String> codes = new ArrayList<>();
        filterSettlementList.forEach(p -> {
            codes.addAll(getAllParentCode(p.getCode()));
        });
        //获取到所有相关联的 details 包括自身和自身的所有上级
        List<ContractInfoDetail> detailList = contractInfoDetailService.list(new LambdaQueryWrapper<ContractInfoDetail>()
                .eq(ContractInfoDetail::getContractId, contractId)
                .in(ContractInfoDetail::getCode, codes));
        contractInfoDetailService.setSettledMoney(detailList);
        List<ContractInfoDetail> detailTree = buildTree(detailList);
        ContractInfo node = contractInfoService.getById(contractId);
        node.setHasChild(1);
        node.setChildren(new ArrayList<>());
        node.getChildren().addAll(detailTree);
        node.setSettledMoney(filterSettlementList.stream().map(Settlement::getCurrentSettlement).reduce(BigDecimal.ZERO, BigDecimal::add));
        node.setPayMoney(contractInfo.getPayMoney());
        node.setInvoiceMoney(contractInfo.getInvoiceMoney());
        return node;
    }

    private List<String> getAllParentCode(String code) {
        code = code.trim();
        List<String> retList = new ArrayList<>();
        if (!code.contains(".")) {
            retList.add(code);
            return retList;
        }
        List<String> items = Arrays.asList(code.split("\\."));
        for (int i = 0; i < items.size(); i++) {
            retList.add(String.join(".", items.subList(0, i + 1)));
        }
        return retList;
    }

    public List<ContractInfo> getSettledContractList(String code, String contractId) {
        LambdaQueryWrapper<Settlement> settlementWrapper = new LambdaQueryWrapper<Settlement>();
        if (!StringUtil.isEmpty(contractId)) {
            settlementWrapper.eq(Settlement::getContractId, contractId);
        }
        if (!StringUtil.isEmpty(code)) {
            settlementWrapper.like(Settlement::getCode, code);
        }
        List<Settlement> settlementList = settlementService.list(settlementWrapper);
        List<String> contractIds = settlementList.stream().map(Settlement::getContractId).distinct().collect(Collectors.toList());
        if (contractIds.isEmpty()) {
            return new ArrayList<>();
        }
        List<ContractInfo> contractInfoList = contractInfoService.list(new LambdaQueryWrapper<ContractInfo>().in(ContractInfo::getId, contractIds));
        List<Settlement> batchSettlementList = settlementService.list(new LambdaQueryWrapper<Settlement>().in(Settlement::getContractId, contractIds).groupBy(Settlement::getBatch));
        List<ContractInfo> retList = new ArrayList<>();
        for (String id : contractIds) {
            ContractInfo contractInfo = contractInfoList.stream().filter(p -> p.getId().equals(id)).findFirst().orElse(null);
            List<Settlement> filterBatchSettlementList = batchSettlementList.stream().filter(p -> p.getContractId().equals(id)).collect(Collectors.toList());
            if (contractInfo != null) {
                contractInfo.setPayMoney(filterBatchSettlementList.stream().map(Settlement::getPayMoney).map(bd -> Objects.nonNull(bd) ? bd : BigDecimal.ZERO).reduce(BigDecimal.ZERO, BigDecimal::add));
                contractInfo.setInvoiceMoney(filterBatchSettlementList.stream().map(Settlement::getInvoiceMoney).map(bd -> Objects.nonNull(bd) ? bd : BigDecimal.ZERO).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            retList.add(buildTree(contractInfo, settlementList));
        }
        return retList;
    }

//    public void setSettledMoney(List<Object> contractInfoList) {
//        for (Object info : contractInfoList) {
//            String id;
//            if (info.getClass() == ContractInfo.class) {
//                id = ((ContractInfo) info).getId();
//                List<Settlement> settlementList = settlementService.list(new LambdaQueryWrapper<Settlement>()
//                        .eq(Settlement::getContractId, id)
//                        .select(Settlement::getCurrentSettlement));
//                BigDecimal total = settlementList.stream().map(Settlement::getCurrentSettlement).reduce(BigDecimal.ZERO, BigDecimal::add);
//                ((ContractInfo) info).setSettledMoney(total);
//            } else if (info.getClass() == ContractInfoDetail.class) {
//                String contractId = ((ContractInfoDetail) info).getContractId();
//                ((ContractInfoDetail) info).setSettledMoney(getSettledMoney(contractId));
//            }
//        }
//    }

    public void setSettledMoney(ContractInfo contractInfo) {
        List<Settlement> settlementList = settlementService.list(new LambdaQueryWrapper<Settlement>()
                .eq(Settlement::getContractId, contractInfo.getId())
                .select(Settlement::getCurrentSettlement));
        BigDecimal total = settlementList.stream().map(Settlement::getCurrentSettlement).reduce(BigDecimal.ZERO, BigDecimal::add);
        contractInfo.setSettledMoney(total);
    }
}
