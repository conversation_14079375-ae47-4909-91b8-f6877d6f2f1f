package com.jinghe.breeze.modules.wind.controller;

import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.modules.person.service.IPersonInfoService;
import com.jinghe.breeze.modules.project.service.IProjectUnitService;
import com.jinghe.breeze.modules.sailingcheck.service.IShipSailingRecordService;
import com.jinghe.breeze.modules.ship.entity.ShipOutgoing;
import com.jinghe.breeze.modules.ship.service.IShipInfoService;
import com.jinghe.breeze.modules.wind.entity.WindReportDetail;
import com.jinghe.breeze.modules.wind.service.IWindNotificationService;
import com.jinghe.breeze.modules.wind.service.IWindReportDetailService;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.vo.LoginUser;
import com.jinghe.breeze.modules.wind.entity.WindReport;
import com.jinghe.breeze.modules.wind.service.IWindReportService;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: wind_report
 * @Author: jeecg-boot
 * @Date: 2024-05-24
 * @Version: V1.0
 */
@Api(tags = "wind_report")
@RestController
@RequestMapping("/wind/windReport")
@Slf4j
public class WindReportController extends JeecgController<WindReport, IWindReportService> {
    @Autowired
    private IWindReportService windReportService;
    @Autowired
    private IWindReportDetailService windReportDetailService;
    @Autowired
    private IWindNotificationService windNotificationService;
    @Autowired
    private IPersonInfoService personInfoService;
    @Autowired
    private IProjectUnitService projectUnitService;
    @Autowired
    private IShipSailingRecordService shipSailingRecordService;

    @Autowired
    private IShipInfoService shipInfoService;

    /**
     * 分页列表查询
     *
     * @param windReport
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "wind_report-分页列表查询")
    @ApiOperation(value = "wind_report-分页列表查询", notes = "wind_report-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(WindReport windReport,
                                   @RequestParam(name = "name", required = false) String name,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        // 项目经理要求只允许查看同单位的记录
        Page<WindReport> page = windReportService.getReportList(windReport, name, pageNo, pageSize);
        return Result.OK(page);
    }

    /**
     * 添加
     *
     * @param windReport
     * @return
     */
    @AutoLog(value = "wind_report-添加")
    @ApiOperation(value = "wind_report-添加", notes = "wind_report-添加")
    @RequiresPermissions("windReport:add")
    @PostMapping(value = "/add")
    @Transactional(rollbackFor = Exception.class)
    public Result<?> add(@Validated @RequestBody WindReport windReport) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        try {
            windReportService.report(windReport, sysUser.getId());
            return Result.OK("添加成功！");
        } catch (JeecgBootException e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 编辑
     *
     * @param windReport
     * @return
     */
    @AutoLog(value = "wind_report-编辑")
    @ApiOperation(value = "wind_report-编辑", notes = "wind_report-编辑")
    @RequiresPermissions("windReport:edit")
    @PutMapping(value = "/edit")
    public Result<?> edit(@Validated @RequestBody WindReport windReport) {
        try {
            windReportService.editReport(windReport);
            return Result.OK("编辑成功!");
        } catch (JeecgBootException e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "wind_report-通过id删除")
    @ApiOperation(value = "wind_report-通过id删除", notes = "wind_report-通过id删除")
    // @RequiresPermissions("windReport:delete")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        windReportService.removeReportAndDetails(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "wind_report-批量删除")
    @ApiOperation(value = "wind_report-批量删除", notes = "wind_report-批量删除")
    @RequiresPermissions("windReport:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.windReportService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "wind_report-通过id查询")
    @ApiOperation(value = "wind_report-通过id查询", notes = "wind_report-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        WindReport windReport = windReportService.getById(id);
        if (windReport == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(windReport);
    }

    @AutoLog(value = "wind_report-通过避风通知id查询报备记录")
    @ApiOperation(value = "wind_report-通过避风通知id查询", notes = "wind_report-通过避风通知id查询")
    @GetMapping(value = "/queryByNotifyId")
    public Result<?> queryByNotifyId(@RequestParam(name = "notifyId", required = true) String notifyId) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        WindReport windReport = windReportService.getOne(new LambdaQueryWrapper<WindReport>()
                .eq(WindReport::getNotifyId, notifyId)
                .eq(WindReport::getUserId, sysUser.getId()), false);
        if (windReport == null) {
            return Result.error("未找到对应数据");
        }
        windReport.setDetails(windReportDetailService.list(
                new LambdaQueryWrapper<WindReportDetail>().eq(WindReportDetail::getReportId, windReport.getId())));
        return Result.OK(windReport);
    }

    @ApiOperation(value = "wind_report_获取登录账号所在单位的所有船只", notes = "wind_report_获取登录账号所在单位的所有船只")
    @GetMapping(value = "/getShipList")
    public Result<?> getShipList() {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<ShipOutgoing> list = shipSailingRecordService.shipList(9, sysUser.getId());
        if (list == null) {
            return Result.OK();
        }
        return Result.OK(list);
        // List<String> idList = list.stream().map(ShipOutgoing::getShipDataId).collect(Collectors.toList());
        // if (idList.isEmpty()) {
        //     return Result.OK(new ArrayList<>());
        // }
        // List<ShipInfo> shipInfoList = shipInfoService.list(new LambdaQueryWrapper<ShipInfo>()
        //         .in(ShipInfo::getId, idList));
        // return Result.OK(shipInfoList);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param windReport
     */
    @RequiresPermissions("windReport:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WindReport windReport) {
        return super.exportXls(request, windReport, WindReport.class, "wind_report");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("windReport:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WindReport.class);
    }

    @ApiOperation(value = "wind_report App获取最近一个月的报备信息", notes = "App获取最近一个月的报备信息")
    @GetMapping(value = "/getRecentReport")
    public Result<?> getRecentReport() {
        return Result.OK(windReportService.getRecentReport());
    }
}
