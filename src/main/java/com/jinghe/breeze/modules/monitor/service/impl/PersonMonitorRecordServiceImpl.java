package com.jinghe.breeze.modules.monitor.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghe.breeze.common.utils.DateUtils;
import com.jinghe.breeze.modules.monitor.entity.PersonMonitorRecord;
import com.jinghe.breeze.modules.monitor.mapper.PersonMonitorRecordMapper;
import com.jinghe.breeze.modules.monitor.service.IPersonMonitorRecordService;
import com.jinghe.breeze.modules.monitor.vo.DataObjectVo;
import com.jinghe.breeze.modules.monitor.vo.MonitorRecordPageVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @Description: monitor_person_record
 * @Author: jeecg-boot
 * @Date: 2024-08-07
 * @Version: V1.0
 */
@Service
public class PersonMonitorRecordServiceImpl extends ServiceImpl<PersonMonitorRecordMapper, PersonMonitorRecord> implements IPersonMonitorRecordService {

    @Autowired
    private PersonMonitorRecordMapper personMonitorRecordMapper;

    @Override
    public List<DataObjectVo> statisticsByHalfMonth() {
        List<DataObjectVo> personStatistics = personMonitorRecordMapper.statisticsByHalfMonth();
        //需要补齐无数据的日期
        List<DataObjectVo> objects = null;
        try {
            objects = DateUtils.completeAndSortData(15, personStatistics);
        } catch (Exception e) {
            log.error("数据异常", e);
        }
        return objects;
    }

    @Override
    public List<DataObjectVo> statisticsByWeek(String gateId) {
        List<DataObjectVo> personStatistics = personMonitorRecordMapper.statisticsByWeek(gateId);
        //需要补齐无数据的日期
        List<DataObjectVo> objects = null;
        try {
            objects = DateUtils.completeAndSortData(7, personStatistics);
        } catch (Exception e) {
            log.error("数据异常", e);
        }
        return objects;
    }

    @Override
    public IPage<MonitorRecordPageVo> getAttendanceRecords(Page<PersonMonitorRecord> page, String gateId) {
        IPage<MonitorRecordPageVo> recordPageVoIPage = personMonitorRecordMapper.getAttendanceRecords(page, gateId);
        List<MonitorRecordPageVo> list = recordPageVoIPage.getRecords();
        list.stream().forEach(item -> {
            // 获取进场时间和离场时间
            Date inTime = item.getInTime();
            Date outTime = item.getOutTime();

            // 当前时间
            Date currentTime = new Date();

            if (ObjectUtil.isNotEmpty(inTime)) {
                // 情况 1: 有进场时间但没有离场时间，使用当前时间减去进场时间
                if (ObjectUtil.isEmpty(outTime)) {
                    String duration = DateUtils.ShowTimeInterval(inTime, currentTime);
                    item.setDuration(duration);
                } else {
                    // 情况 2: 有进场时间和离场时间
                    if (outTime.before(inTime)) {
                        // 情况 2a: 离场时间早于进场时间，使用当前时间减去进场时间
                        String duration = DateUtils.ShowTimeInterval(inTime, currentTime);
                        item.setDuration(duration);
                    } else {
                        // 情况 2b: 正常的离场时间减去进场时间
                        String duration = DateUtils.ShowTimeInterval(inTime, outTime);
                        item.setDuration(duration);
                    }
                }
            } else if (ObjectUtil.isNotEmpty(outTime)) {
                // 情况 3: 没有进场时间但有离场时间，使用离场时间减去 00:00
                // 假设 00:00 的日期为离场时间的当天日期的开始时间
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(outTime);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                Date zeroTime = calendar.getTime();

                String duration = DateUtils.ShowTimeInterval(zeroTime, outTime);
                item.setDuration(duration);
            }
        });
        return recordPageVoIPage;
    }


}
