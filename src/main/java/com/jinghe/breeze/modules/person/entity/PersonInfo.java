package com.jinghe.breeze.modules.person.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: person_info
 * @Author: jeecg-boot
 * @Date: 2024-05-10
 * @Version: V1.0
 */
@Data
@TableName("person_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "person_info对象", description = "person_info")
public class PersonInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
    /**
     * 人员姓名
     */
    @Excel(name = "人员姓名", width = 15)
    @ApiModelProperty(value = "人员姓名")
    private String name;
    /**
     * 性别
     */
    @Dict(dicCode = "sex")
    @Excel(name = "性别", width = 15)
    @TableField(updateStrategy = FieldStrategy.IGNORED, insertStrategy = FieldStrategy.IGNORED) //忽略空值检查
    @ApiModelProperty(value = "性别")
    private String sex;
    /**
     * 年龄
     */
    @Excel(name = "年龄", width = 15)
    @ApiModelProperty(value = "年龄")
    private Integer age;
    /**
     * 身份证号
     */
    @Excel(name = "身份证号", width = 15)
    @ApiModelProperty(value = "身份证号")
    @TableField(updateStrategy = FieldStrategy.IGNORED, insertStrategy = FieldStrategy.IGNORED) //忽略空值检查
    private String idCard;
    /**
     * 手机号码
     */
    @Excel(name = "手机号码", width = 15)
    @ApiModelProperty(value = "手机号码")
    private String phone;
    /**
     * 人员类型
     */
    @Dict(dicCode = "person_type")
    @Excel(name = "人员类型", width = 15)
    @ApiModelProperty(value = "人员类型")
    private String personType;
    /**
     * 所属单位
     */
    @Dict(dictTable = "project_unit", dicText = "name", dicCode = "id")
    @Excel(name = "所属单位", width = 15)
    @ApiModelProperty(value = "所属单位")
    private String department;
    /**
     * 单位类型
     */
    @Dict(dicCode = "unit_type")
    @Excel(name = "单位类型", width = 15)
    @ApiModelProperty(value = "单位类型")
    private String departmentType;
    /**
     * 岗位
     */
    @Dict(dicCode = "job")
    @Excel(name = "岗位", width = 15)
    @ApiModelProperty(value = "岗位")
    private String job;
    /**
     * 进场时间
     */
    @Excel(name = "进场时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @TableField(updateStrategy = FieldStrategy.IGNORED, insertStrategy = FieldStrategy.IGNORED) //忽略空值检查
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "进场时间")
    private Date enterDate;
    /**
     * 退场时间
     */
    @Excel(name = "退场时间", width = 15, format = "yyyy-MM-dd")
    @TableField(updateStrategy = FieldStrategy.IGNORED, insertStrategy = FieldStrategy.IGNORED) //忽略空值检查
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "退场时间")
    private Date leaveDate;
    /**
     * 照片
     */
    @Excel(name = "照片", width = 15)
    @ApiModelProperty(value = "照片")
    private String picture;

    @Excel(name = "关联账号id", width = 15)
    @ApiModelProperty(value = "关联账号id")
    private String userId;

    @Excel(name = "关联账号", width = 15)
    @ApiModelProperty(value = "关联账号")
    private String userName;

    @Excel(name = "编号", width = 15)
    @ApiModelProperty(value = "编号")
    private String code;


    @Excel(name = "安全帽编码", width = 15)
    @ApiModelProperty(value = "安全帽编码")
    private String deviceCode;
    /**
     * 1自动录入 2手动
     */
    @Excel(name = "是否自动录入", width = 15)
    @ApiModelProperty(value = "是否自动录入")
    @TableField(updateStrategy = FieldStrategy.IGNORED, insertStrategy = FieldStrategy.IGNORED) //忽略空值检查
    private Integer autoInput;

    @Excel(name = "安全教育是否合格", width = 15)
    @Dict(dicCode = "safe_education")
    @ApiModelProperty(value = "安全教育是否合格")
    private String safeEducation;

    /**
     * in 进场  out退场
     */
    @Excel(name = "进退场状态", width = 15)
    @Dict(dicCode = "enter_status")
    @ApiModelProperty(value = "进退场状态")
    @TableField(updateStrategy = FieldStrategy.IGNORED, insertStrategy = FieldStrategy.IGNORED) //忽略空值检查
    private String enterStatus;

    /**
     * 1 黑名单 0不是
     */
    @Excel(name = "黑名单", width = 15)
    @ApiModelProperty(value = "黑名单")
    private Integer isBlacklist;
}
