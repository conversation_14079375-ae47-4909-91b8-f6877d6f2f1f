package com.jinghe.breeze.modules.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.modules.project.entity.ProjectInfo;
import com.jinghe.breeze.modules.project.mapper.ProjectInfoMapper;
import com.jinghe.breeze.modules.project.service.IProjectInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @Description: project_info
 * @Author: jeecg-boot
 * @Date: 2024-04-17
 * @Version: V1.0
 */
@Service
public class ProjectInfoServiceImpl extends ServiceImpl<ProjectInfoMapper, ProjectInfo> implements IProjectInfoService {

    @Autowired
    private IProjectInfoService projectInfoService;

    @Override
    public Map<String, String> getProjectInfo() {
        Map<String, String> map = new HashMap<>();
        ProjectInfo projectInfo = projectInfoService.getOne(new LambdaQueryWrapper<ProjectInfo>()
                .eq(ProjectInfo::getDelFlag, Common.delete_flag.OK), false);
        if (projectInfo == null) {
            map.put("workedDays", "--");
            map.put("plannedDuration", "--");
            map.put("plannedCompletionDate", "--");
            return map;
        }
        //getActualStartDate 可能为null
        //getActualEndDate 可能为null
        //其他两个时间不可能为null
        Date actualStartDate = projectInfo.getActualStartDate();
        String workedDaysString = "--";
        long diff = projectInfo.getEndDate().getTime() - projectInfo.getStartDate().getTime();
        long plannedDuration = TimeUnit.MILLISECONDS.toDays(diff);
        map.put("plannedDuration", String.valueOf(plannedDuration)); //计划工期
        String plannedCompletionDate = new SimpleDateFormat("yyyy-MM-dd").format(projectInfo.getEndDate());
        map.put("plannedCompletionDate", plannedCompletionDate);//计划完工
        map.put("remark", projectInfo.getRemark());
        if (actualStartDate != null) {
            diff = new Date().getTime() - projectInfo.getActualStartDate().getTime();
            long workedDays = TimeUnit.MILLISECONDS.toDays(diff);
            workedDaysString = String.valueOf(workedDays);
        }
        map.put("workedDays", workedDaysString);
        return map;
    }
}
