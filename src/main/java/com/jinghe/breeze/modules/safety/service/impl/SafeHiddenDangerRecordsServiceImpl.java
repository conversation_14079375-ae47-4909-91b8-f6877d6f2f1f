package com.jinghe.breeze.modules.safety.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.common.enums.DelFlagEnum;
import com.jinghe.breeze.modules.quality.entity.QualityInspectionRecords;
import com.jinghe.breeze.modules.quality.entity.QualityIssueRectifications;
import com.jinghe.breeze.modules.quality.service.IQualityIssueRectificationsService;
import com.jinghe.breeze.modules.safety.entity.SafeHiddenDangerRecords;
import com.jinghe.breeze.modules.safety.entity.SafeInspectionRecords;
import com.jinghe.breeze.modules.safety.mapper.SafeHiddenDangerRecordsMapper;
import com.jinghe.breeze.modules.safety.service.ISafeHiddenDangerRecordsService;
import org.apache.shiro.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 隐患整改
 * @Author: jeecg-boot
 * @Date:   2025-06-24
 * @Version: V1.0
 */
@Service
public class SafeHiddenDangerRecordsServiceImpl extends ServiceImpl<SafeHiddenDangerRecordsMapper, SafeHiddenDangerRecords> implements ISafeHiddenDangerRecordsService {
    @Autowired
    private ISafeHiddenDangerRecordsService safeHiddenDangerRecordsService;


}
