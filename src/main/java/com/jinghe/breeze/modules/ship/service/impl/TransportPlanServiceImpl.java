package com.jinghe.breeze.modules.ship.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.common.job.WeatherJob;
import com.jinghe.breeze.common.redis.utils.RedisTool;
import com.jinghe.breeze.modules.sea.entity.SeaWeatherVo;
import com.jinghe.breeze.modules.ship.entity.TransportPlan;
import com.jinghe.breeze.modules.ship.mapper.TransportPlanMapper;
import com.jinghe.breeze.modules.ship.service.ITransportPlanService;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: transport_plan
 * @Author: jeecg-boot
 * @Date: 2024-06-06
 * @Version: V1.0
 */
@Service
public class TransportPlanServiceImpl extends ServiceImpl<TransportPlanMapper, TransportPlan>
        implements ITransportPlanService {
//    @Autowired
//    private ShipHelper shipHelper;

//    @Autowired
//    private TransportPlanMapper transportPlanMapper;

    @Resource
    private RedisTool redisTool;

//    @Autowired
//    private ShipService shipService;

    @Autowired
    private WeatherJob weatherJob;

    @Override
    public Result<?> getList(String transportCode, Integer pageNo, Integer pageSize, String status) {
        IPage<TransportPlan> iPage = new Page<>(pageNo, pageSize);

        LambdaQueryWrapper<TransportPlan> queryWrapper = new LambdaQueryWrapper<>();
        if (!StringUtils.isEmpty(status)) {
            queryWrapper.eq(TransportPlan::getStatus, status);
        }
        if (!StringUtils.isEmpty(transportCode)) {
            // IPage<TransportPlan> page = transportPlanMapper.findList(iPage);
            // return Result.OK(page);
            queryWrapper.like(TransportPlan::getTransportCode, "%" + transportCode + "%");
        }
        IPage<TransportPlan> page = super.page(iPage, queryWrapper);
        return Result.OK(page);
    }

    @Override
    public Result<?> getUnderwayList() {
        LambdaQueryWrapper<TransportPlan> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TransportPlan::getStatus, Common.transportPlan.UNDERWAY);
        queryWrapper.orderByAsc(TransportPlan::getArriveDate);
        List<TransportPlan> list = this.list(queryWrapper);
        return Result.OK(list);
    }

    @Override
    public Result<?> getOrderDetail(String id) throws IOException, NoSuchAlgorithmException {
        TransportPlan transportOrder = this.getById(id);
        if (transportOrder == null || StringUtils.isEmpty(transportOrder.getMmsi())) {
            return Result.OK();
        }
        if (redisTool.hasKey(Common.transportPlan.UNDERWAY + transportOrder.getMmsi())) {
            Object o = redisTool.get(Common.transportPlan.UNDERWAY + transportOrder.getMmsi());
            return Result.OK(o);
        } else {

            // ShipHelper shipHelper = new ShipHelper(null, null, null);
//            shipHelper.createNewSession();
//            Ship ship = shipHelper.getShip(transportOrder.getMmsi());
//            float lon = ((float) ship.getLon()) / 1000000.0f;
//            float lat = ((float) ship.getLat()) / 1000000.0f;
            float lon=0L;
            float lat=0L;
            // Weather weather = shipHelper.getWeather(transportOrder.getMmsi(), lon, lat);
            // float lon = ((float) ship.getLon())/1000000.0f;
            // float lat = ((float) ship.getLat())/1000000.0f;
            SeaWeatherVo vo = new SeaWeatherVo();
            weatherJob.getSeaWeather(String.valueOf(lon), String.valueOf(lat), vo);
            Map result = new HashMap<>();
            result.put("order", transportOrder);
//            result.put("ship", ship);
            result.put("weather", vo);
            redisTool.set(Common.transportPlan.UNDERWAY + transportOrder.getMmsi(), result, 60 * 60 * 2);
            return Result.OK(result);

            // List<Ship> shipList = shipService.findByMmsi(transportOrder.getMmsi());
            // if(!shipList.isEmpty()){
            // return Result.error("未找到船舶信息");
            // }else{
            // Ship ship = shipList.get(0);
            // float lon = ((float) ship.getLon())/1000000.0f;
            // float lat = ((float) ship.getLat())/1000000.0f;
            // Weather weather = shipHelper.getWeather(transportOrder.getMmsi(), lon, lat);
            // Map result = new HashMap<>();
            // result.put("order", transportOrder);
            // result.put("ship", ship);
            // result.put("weather", weather);
            // redisTool.set(Common.transportPlan.UNDERWAY + transportOrder.getMmsi(),
            // result, 1000L * 60 * 60*2);
            // return Result.OK(result);
            // }
        }

    }
}
