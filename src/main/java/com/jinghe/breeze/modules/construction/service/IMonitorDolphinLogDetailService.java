package com.jinghe.breeze.modules.construction.service;

import com.jinghe.breeze.modules.construction.entity.PolarPlotRequestData;
import com.jinghe.breeze.modules.construction.entity.MonitorDolphinLogDetail;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Map;

/**
 * @Description: 观豚日志列表
 * @Author: jeecg-boot
 * @Date:   2024-05-11
 * @Version: V1.0
 */
public interface IMonitorDolphinLogDetailService extends IService<MonitorDolphinLogDetail> {


    Map<String, Integer> getPolarData(PolarPlotRequestData polarData);
}
