package com.jinghe.breeze.modules.person.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jinghe.breeze.modules.person.entity.PersonInfo;
import com.jinghe.breeze.modules.person.dto.TrajectoryDTO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * @Description: person_info
 * @Author: jeecg-boot
 * @Date:   2024-05-10
 * @Version: V1.0
 */
public interface IPersonInfoService extends IService<PersonInfo> {

    void saveInfo(PersonInfo personInfo);

    Map<String,String> queryByUsername(String username);

    void updatePersonInfo(PersonInfo personInfo);

    List<PersonInfo> listNoPage(QueryWrapper<PersonInfo> queryWrapper);
    LinkedHashMap<String, List<PersonInfo>> getPersonInfoNoPage();

    /**
     * 获取人员轨迹回放数据（异步）
     * @param personId 人员ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 轨迹数据列表的异步结果
     */
    CompletableFuture<List<TrajectoryDTO>> getPersonTrajectoryAsync(String personId, String startDate, String endDate);

    void addToBlackList(PersonInfo personInfo,boolean isBlack);
}
