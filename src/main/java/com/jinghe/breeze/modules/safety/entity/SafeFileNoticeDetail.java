package com.jinghe.breeze.modules.safety.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.*;
/**
 * @Description: safe_file_notice_detail
 * @Author: jeecg-boot
 * @Date:   2025-06-26
 * @Version: V1.0
 */
@Data
@TableName("safe_file_notice_detail")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="safe_file_notice_detail对象", description="safe_file_notice_detail")
public class SafeFileNoticeDetail implements Serializable {
    private static final long serialVersionUID = 1L;

	/**标识*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "标识")
    private String id;
	/**创建人*/

    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**更新人*/

    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
	/**delFlag*/
	@Excel(name = "delFlag", width = 15)
    @ApiModelProperty(value = "delFlag")
    private Integer delFlag;
	/**组织机构编码*/
    @ApiModelProperty(value = "组织机构编码")
    private String sysOrgCode;
	/**文件接收人*/
	@Excel(name = "文件接收人", width = 15)
    @ApiModelProperty(value = "文件接收人")
    @Dict(dictTable = "person_info", dicText = "name", dicCode = "user_id")
    private String recipient;
	/**主表id*/
	@Excel(name = "主表id", width = 15)
    @ApiModelProperty(value = "主表id")
    private String baseId;
	/**已读状态*/
	@Excel(name = "已读状态", width = 15)
    @ApiModelProperty(value = "已读状态")
    @Dict(dicCode = "read_status")
    private String readStatus;
	/**已读时间*/
	@Excel(name = "已读时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "已读时间")
    private Date readTime;
}
