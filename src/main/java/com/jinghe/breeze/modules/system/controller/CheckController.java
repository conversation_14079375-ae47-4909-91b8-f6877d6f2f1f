package com.jinghe.breeze.modules.system.controller;

import com.jinghe.breeze.modules.system.entity.CheckVo;
import com.jinghe.breeze.modules.system.mapper.ProjectSysDictMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.system.controller.DuplicateCheckController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping({"/sys/duplicate"})
@Api(
        tags = {"重复校验"}
)
public class CheckController {
    private static final Logger log = LoggerFactory.getLogger(DuplicateCheckController.class);
    @Autowired
    ProjectSysDictMapper sysDictMapper;

    public CheckController() {
    }

    @RequestMapping(
            value = {"/check2"},
            method = {RequestMethod.GET}
    )
    @ApiOperation("重复校验接口")
    public Result<Object> doDuplicateCheck(CheckVo duplicateCheckVo, HttpServletRequest request) {
        Long num = null;
        log.info("----duplicate check------：" + duplicateCheckVo.toString());
        if (StringUtils.isNotBlank(duplicateCheckVo.getDataId())) {
            num = this.sysDictMapper.duplicateCheckCountSql(duplicateCheckVo);
        } else {
            num = this.sysDictMapper.duplicateCheckCountSqlNoDataId(duplicateCheckVo);
        }

        if (num != null && num != 0L) {
            log.info("该值不可用，系统中已存在！");
            return Result.error("该值不可用，系统中已存在！");
        } else {
            return Result.ok("该值可用！");
        }
    }

}
