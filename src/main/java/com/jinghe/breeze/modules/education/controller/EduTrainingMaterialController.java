package com.jinghe.breeze.modules.education.controller;
import com.jinghe.breeze.modules.education.entity.EduTrainingMaterial;
import com.jinghe.breeze.modules.education.service.IEduQuestionService;
import com.jinghe.breeze.modules.education.service.IEduTrainingMaterialService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 培训资料
 * @Author: jeecg-boot
 * @Date:   2025-06-23
 * @Version: V1.0
 */
@Api(tags="培训资料")
@RestController
@RequestMapping("/education/trainingMaterial")
@Slf4j
public class EduTrainingMaterialController extends JeecgController<EduTrainingMaterial, IEduTrainingMaterialService> {
	@Autowired
	private IEduTrainingMaterialService trainingMaterialService;
	
	/**
	 * 分页列表查询
	 *
	 * @param eduTrainingMaterial
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "培训资料-分页列表查询")
	@ApiOperation(value="培训资料-分页列表查询", notes="培训资料-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(EduTrainingMaterial eduTrainingMaterial,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<EduTrainingMaterial> queryWrapper = QueryGenerator.initQueryWrapper(eduTrainingMaterial, req.getParameterMap());
		Page<EduTrainingMaterial> page = new Page<EduTrainingMaterial>(pageNo, pageSize);
		IPage<EduTrainingMaterial> pageList = trainingMaterialService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param eduTrainingMaterial
	 * @return
	 */
	@AutoLog(value = "培训资料-添加")
	@ApiOperation(value="培训资料-添加", notes="培训资料-添加")
//	@RequiresPermissions("eduTrainingMaterial:add")
	@PostMapping(value = "/add")
	public Result<?> add(@Validated @RequestBody EduTrainingMaterial eduTrainingMaterial) {
		trainingMaterialService.save(eduTrainingMaterial);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param eduTrainingMaterial
	 * @return
	 */
	@AutoLog(value = "培训资料-编辑")
	@ApiOperation(value="培训资料-编辑", notes="培训资料-编辑")
	@RequiresPermissions("eduTrainingMaterial:edit")
	@PutMapping(value = "/edit")
	public Result<?> edit(@Validated @RequestBody EduTrainingMaterial eduTrainingMaterial) {
		trainingMaterialService.updateById(eduTrainingMaterial);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "培训资料-通过id删除")
	@ApiOperation(value="培训资料-通过id删除", notes="培训资料-通过id删除")
	@RequiresPermissions("trainingMaterial:delete")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		trainingMaterialService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "培训资料-批量删除")
	@ApiOperation(value="培训资料-批量删除", notes="培训资料-批量删除")
	@RequiresPermissions("trainingMaterial:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.trainingMaterialService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "培训资料-通过id查询")
	@ApiOperation(value="培训资料-通过id查询", notes="培训资料-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		EduTrainingMaterial eduTrainingMaterial = trainingMaterialService.getById(id);
		if(eduTrainingMaterial ==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(eduTrainingMaterial);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param eduTrainingMaterial
    */
    @RequiresPermissions("eduTrainingMaterial:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, EduTrainingMaterial eduTrainingMaterial) {
        return super.exportXls(request, eduTrainingMaterial, EduTrainingMaterial.class, "培训资料");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("trainingMaterial:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, EduTrainingMaterial.class);
    }

}
