package com.jinghe.breeze.modules.quality.controller;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.jinghe.breeze.modules.quality.entity.QualityIssueRectifications;
import com.jinghe.breeze.modules.quality.service.IQualityIssueRectificationsService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 质量整改
 * @Author: jeecg-boot
 * @Date:   2025-06-21
 * @Version: V1.0
 */
@Api(tags="质量整改")
@RestController
@RequestMapping("/quality/qualityIssueRectifications")
@Slf4j
public class QualityIssueRectificationsController extends JeecgController<QualityIssueRectifications, IQualityIssueRectificationsService> {
	@Autowired
	private IQualityIssueRectificationsService qualityIssueRectificationsService;
	
	/**
	 * 分页列表查询
	 *
	 * @param qualityIssueRectifications
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "质量整改-分页列表查询")
	@ApiOperation(value="质量整改-分页列表查询", notes="质量整改-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(QualityIssueRectifications qualityIssueRectifications,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<QualityIssueRectifications> queryWrapper = QueryGenerator.initQueryWrapper(qualityIssueRectifications, req.getParameterMap());
		Page<QualityIssueRectifications> page = new Page<QualityIssueRectifications>(pageNo, pageSize);
		IPage<QualityIssueRectifications> pageList = qualityIssueRectificationsService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param qualityIssueRectifications
	 * @return
	 */
	@AutoLog(value = "质量整改-添加")
	@ApiOperation(value="质量整改-添加", notes="质量整改-添加")
//	@RequiresPermissions("qualityIssueRectifications:add")
	@PostMapping(value = "/add")
	public Result<?> add(@Validated @RequestBody QualityIssueRectifications qualityIssueRectifications) {
        qualityIssueRectifications.setBpmStatus("1");
		qualityIssueRectificationsService.save(qualityIssueRectifications);
		return Result.OK(qualityIssueRectifications);
	}
	
	/**
	 *  编辑
	 *
	 * @param qualityIssueRectifications
	 * @return
	 */
	@AutoLog(value = "质量整改-编辑")
	@ApiOperation(value="质量整改-编辑", notes="质量整改-编辑")
//	@RequiresPermissions("qualityIssueRectifications:edit")
	@PutMapping(value = "/edit")
	public Result<?> edit(@Validated @RequestBody QualityIssueRectifications qualityIssueRectifications) {
		qualityIssueRectificationsService.updateById(qualityIssueRectifications);
		return Result.OK(qualityIssueRectifications);
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "质量整改-通过id删除")
	@ApiOperation(value="质量整改-通过id删除", notes="质量整改-通过id删除")
//	@RequiresPermissions("qualityIssueRectifications:delete")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		qualityIssueRectificationsService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "质量整改-批量删除")
	@ApiOperation(value="质量整改-批量删除", notes="质量整改-批量删除")
//	@RequiresPermissions("qualityIssueRectifications:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.qualityIssueRectificationsService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "质量整改-通过id查询")
	@ApiOperation(value="质量整改-通过id查询", notes="质量整改-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		QualityIssueRectifications qualityIssueRectifications = qualityIssueRectificationsService.getById(id);
		if(qualityIssueRectifications==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(qualityIssueRectifications);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param qualityIssueRectifications
    */
//    @RequiresPermissions("qualityIssueRectifications:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, QualityIssueRectifications qualityIssueRectifications) {
        return super.exportXls(request, qualityIssueRectifications, QualityIssueRectifications.class, "质量整改");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
//    @RequiresPermissions("qualityIssueRectifications:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, QualityIssueRectifications.class);
    }

}
