package com.jinghe.breeze.modules.quickentry.service;

import com.jinghe.breeze.modules.quickentry.entity.QuickEntryRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;

/**
 * @Description: quick_entry_record
 * @Author: jeecg-boot
 * @Date:   2024-06-26
 * @Version: V1.0
 */
public interface IQuickEntryRecordService extends IService<QuickEntryRecord> {

    Result<?> queryByLoginUser();
    Result<?> saveInfo(QuickEntryRecord record);
}
