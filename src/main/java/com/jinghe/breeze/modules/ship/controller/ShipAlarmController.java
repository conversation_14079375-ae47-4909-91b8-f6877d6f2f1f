package com.jinghe.breeze.modules.ship.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.jinghe.breeze.modules.ship.entity.ShipAlarm;
import com.jinghe.breeze.modules.ship.service.IShipAlarmService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: ship_alarm
 * @Author: jeecg-boot
 * @Date:   2024-04-23
 * @Version: V1.0
 */
@Api(tags="ship_alarm")
@RestController
@RequestMapping("/ship/shipAlarm")
@Slf4j
public class ShipAlarmController extends JeecgController<ShipAlarm, IShipAlarmService> {
	@Autowired
	private IShipAlarmService shipAlarmService;
	
	/**
	 * 分页列表查询
	 *
	 * @param shipAlarm
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "ship_alarm-分页列表查询")
	@ApiOperation(value="ship_alarm-分页列表查询", notes="ship_alarm-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(ShipAlarm shipAlarm,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<ShipAlarm> queryWrapper = QueryGenerator.initQueryWrapper(shipAlarm, req.getParameterMap());
//		LambdaQueryWrapper<ShipAlarm> queryWrapper = new LambdaQueryWrapper<>();
//		queryWrapper.eq(ShipAlarm::getAlarmDay,shipAlarm.getAlarmDay());
//		if (){
//
//		}
		Page<ShipAlarm> page = new Page<ShipAlarm>(pageNo, pageSize);
		IPage<ShipAlarm> pageList = shipAlarmService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	 /**
	  * 分析统计
	  *
	  * @param
	  * @param
	  * @param
	  * @param
	  * @return
	  */
	 @AutoLog(value = "分析统计")
	 @ApiOperation(value="", notes="分析统计")
	 @GetMapping(value = "/queryStatistics")
	 public Result<?> queryStatistics(ShipAlarm shipAlarm) {
		 return shipAlarmService.queryStatistics(shipAlarm);
	 }

	/**
	 *   添加
	 *
	 * @param shipAlarm
	 * @return
	 */
	@AutoLog(value = "ship_alarm-添加")
	@ApiOperation(value="ship_alarm-添加", notes="ship_alarm-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody ShipAlarm shipAlarm) {
		shipAlarmService.save(shipAlarm);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param shipAlarm
	 * @return
	 */
	@AutoLog(value = "ship_alarm-编辑")
	@ApiOperation(value="ship_alarm-编辑", notes="ship_alarm-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody ShipAlarm shipAlarm) {
		shipAlarmService.updateById(shipAlarm);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "ship_alarm-通过id删除")
	@ApiOperation(value="ship_alarm-通过id删除", notes="ship_alarm-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		shipAlarmService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "ship_alarm-批量删除")
	@ApiOperation(value="ship_alarm-批量删除", notes="ship_alarm-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.shipAlarmService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "ship_alarm-通过id查询")
	@ApiOperation(value="ship_alarm-通过id查询", notes="ship_alarm-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		ShipAlarm shipAlarm = shipAlarmService.getById(id);
		if(shipAlarm==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(shipAlarm);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param shipAlarm
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ShipAlarm shipAlarm) {
        return super.exportXls(request, shipAlarm, ShipAlarm.class, "ship_alarm");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ShipAlarm.class);
    }


	 /**
	  * 告警船舶(实时请求船讯网)
	  *
	  * @param id
	  * @return
	  */
	 @AutoLog(value = "告警船舶")
	 @ApiOperation(value="告警船舶", notes="告警船舶")
	 @GetMapping(value = "/queryAlarm")
	 public Result<?> queryAlarm() {
		 return shipAlarmService.queryAlarm();
	 }
}
