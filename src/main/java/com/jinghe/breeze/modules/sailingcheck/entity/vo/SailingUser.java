package com.jinghe.breeze.modules.sailingcheck.entity.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="登船用户信息", description="登船用户信息")
public class SailingUser {

    private String userId;

    private String userName;

    private String name;

    private String departmentId;

    private String departmentName;

    private String result;

    private String remark;

    private String status;

    private String backShipName;

    private String backShipMMSI;

}
