package com.jinghe.breeze.modules.ship.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: transport_plan
 * @Author: jeecg-boot
 * @Date:   2024-06-06
 * @Version: V1.0
 */
@Data
@TableName("transport_plan")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="transport_plan对象", description="transport_plan")
public class TransportPlan implements Serializable {
    private static final long serialVersionUID = 1L;

	/**标识*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "标识")
    private String id;
	/**创建人*/

    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**更新人*/

    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
	/**删除标识*/
	@Excel(name = "删除标识", width = 15)

    @ApiModelProperty(value = "删除标识")
    private Integer deleteFlag;
	/**组织机构编码*/

    @ApiModelProperty(value = "组织机构编码")
    private String sysOrgCode;
	/**项目id*/
	@Excel(name = "项目id", width = 15)

    @ApiModelProperty(value = "项目id")
    private String projectId;
	/**运输状态*/
	@Excel(name = "运输状态", width = 15, dicCode = "transport_status")
	@Dict(dicCode = "transport_status")

    @ApiModelProperty(value = "运输状态")
    private String status;
	/**单号*/
	@Excel(name = "单号", width = 15)

    @ApiModelProperty(value = "单号")
    private String transportCode;
	/**运输货物*/
	@Excel(name = "运输货物", width = 15)

    @ApiModelProperty(value = "运输货物")
    private String transportGoods;
	/**船舶名称*/
	@Excel(name = "船舶名称", width = 15)

    @ApiModelProperty(value = "船舶名称")
    private String shipName;
	/**出发港口*/
	@Excel(name = "出发港口", width = 15)

    @ApiModelProperty(value = "出发港口")
    private String departPort;
	/**mmsi*/
	@Excel(name = "mmsi", width = 15)

    @ApiModelProperty(value = "mmsi")
    private String mmsi;
	/**预计出发时间*/
	@Excel(name = "预计出发时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")

    @ApiModelProperty(value = "预计出发时间")
    private Date startDate;
	/**目的地港口*/
	@Excel(name = "目的地港口", width = 15)

    @ApiModelProperty(value = "目的地港口")
    private String destinationPort;
	/**预计到达时间*/
	@Excel(name = "预计到达时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")

    @ApiModelProperty(value = "预计到达时间")
    private Date arriveDate;
	/**承运人*/
	@Excel(name = "承运人", width = 15)

    @ApiModelProperty(value = "承运人")
    private String carrier;
	/**电话*/
	@Excel(name = "电话", width = 15)

    @ApiModelProperty(value = "电话")
    private String phone;
	/**备注*/
	@Excel(name = "备注", width = 15)

    @ApiModelProperty(value = "备注")
    private String remark;
	/**附件*/
	@Excel(name = "附件", width = 15)

    @ApiModelProperty(value = "附件")
    private String file;
}
