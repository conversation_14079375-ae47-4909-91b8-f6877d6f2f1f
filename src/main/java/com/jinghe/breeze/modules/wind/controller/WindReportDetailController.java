package com.jinghe.breeze.modules.wind.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.jinghe.breeze.modules.wind.entity.WindReportDetail;
import com.jinghe.breeze.modules.wind.service.IWindReportDetailService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: wind_report_detail
 * @Author: jeecg-boot
 * @Date:   2024-05-24
 * @Version: V1.0
 */
@Api(tags="wind_report_detail")
@RestController
@RequestMapping("/wind/windReportDetail")
@Slf4j
public class WindReportDetailController extends JeecgController<WindReportDetail, IWindReportDetailService> {
	@Autowired
	private IWindReportDetailService windReportDetailService;
	
	/**
	 * 分页列表查询
	 *
	 * @param windReportDetail
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "wind_report_detail-分页列表查询")
	@ApiOperation(value="wind_report_detail-分页列表查询", notes="wind_report_detail-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(WindReportDetail windReportDetail,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<WindReportDetail> queryWrapper = QueryGenerator.initQueryWrapper(windReportDetail, req.getParameterMap());
		Page<WindReportDetail> page = new Page<WindReportDetail>(pageNo, pageSize);
		IPage<WindReportDetail> pageList = windReportDetailService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param windReportDetail
	 * @return
	 */
	@AutoLog(value = "wind_report_detail-添加")
	@ApiOperation(value="wind_report_detail-添加", notes="wind_report_detail-添加")
//	@RequiresPermissions("windReportDetail:add")
	@PostMapping(value = "/add")
	public Result<?> add(@Validated @RequestBody WindReportDetail windReportDetail) {
		windReportDetailService.save(windReportDetail);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param windReportDetail
	 * @return
	 */
	@AutoLog(value = "wind_report_detail-编辑")
	@ApiOperation(value="wind_report_detail-编辑", notes="wind_report_detail-编辑")
//	@RequiresPermissions("windReportDetail:edit")
	@PutMapping(value = "/edit")
	public Result<?> edit(@Validated @RequestBody WindReportDetail windReportDetail) {
		windReportDetailService.updateById(windReportDetail);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "wind_report_detail-通过id删除")
	@ApiOperation(value="wind_report_detail-通过id删除", notes="wind_report_detail-通过id删除")
//	@RequiresPermissions("windReportDetail:delete")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		windReportDetailService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "wind_report_detail-批量删除")
	@ApiOperation(value="wind_report_detail-批量删除", notes="wind_report_detail-批量删除")
//	@RequiresPermissions("windReportDetail:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.windReportDetailService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "wind_report_detail-通过id查询")
	@ApiOperation(value="wind_report_detail-通过id查询", notes="wind_report_detail-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		WindReportDetail windReportDetail = windReportDetailService.getById(id);
		if(windReportDetail==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(windReportDetail);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param windReportDetail
    */
//    @RequiresPermissions("windReportDetail:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WindReportDetail windReportDetail) {
        return super.exportXls(request, windReportDetail, WindReportDetail.class, "wind_report_detail");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
//    @RequiresPermissions("windReportDetail:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WindReportDetail.class);
    }

}
