package com.jinghe.breeze.modules.safety.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.*;
/**
 * @Description: 危大工程
 * @Author: jeecg-boot
 * @Date:   2025-06-24
 * @Version: V1.0
 */
@Data
@TableName("safe_dangerous_project")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="safe_dangerous_project对象", description="危大工程")
public class SafeDangerousProject implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**创建人*/

    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/

    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**所属部门*/

    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
	/**危大工程名称*/
	@Excel(name = "危大工程名称", width = 15)
    @NotNull(message = "危大工程名称不能为空!")

    @ApiModelProperty(value = "危大工程名称")
    private String projectName;
	/**施工负责人*/
	@Excel(name = "施工负责人", width = 15, dictTable = "person_info", dicText = "name", dicCode = "id")
	@Dict(dictTable = "person_info", dicText = "name", dicCode = "id")
    @NotNull(message = "施工负责人不能为空!")

    @ApiModelProperty(value = "施工负责人")
    private String constructionLeader;
	/**安全负责人*/
	@Excel(name = "安全负责人", width = 15, dictTable = "person_info", dicText = "name", dicCode = "id")
	@Dict(dictTable = "person_info", dicText = "name", dicCode = "id")
    @NotNull(message = "安全负责人不能为空!")

    @ApiModelProperty(value = "安全负责人")
    private String safetyLeader;
	/**危大开始时间*/
	@Excel(name = "危大开始时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @NotNull(message = "危大开始时间不能为空!")

    @ApiModelProperty(value = "危大开始时间")
    private Date startDate;
	/**预计完成时间*/
	@Excel(name = "预计完成时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @NotNull(message = "预计完成时间不能为空!")

    @ApiModelProperty(value = "预计完成时间")
    private Date expectedEndDate;
	/**施工方案*/
	@Excel(name = "施工方案", width = 15)
    @NotNull(message = "施工方案不能为空!")

    @ApiModelProperty(value = "施工方案")
    private String constructionPlanFile;
	/**隐患整改*/
	@Excel(name = "隐患整改", width = 15)
    @NotNull(message = "隐患整改不能为空!")

    @ApiModelProperty(value = "隐患整改")
    private String hazardRectification;
	/**当前阶段*/
	@Excel(name = "当前阶段", width = 15, dicCode = "current_stage")
	@Dict(dicCode = "current_stage")
    @NotNull(message = "当前阶段不能为空!")

    @ApiModelProperty(value = "当前阶段")
    private String currentStage;
	/**监护人*/
	@Excel(name = "监护人", width = 15, dictTable = "person_info", dicText = "name", dicCode = "id")
	@Dict(dictTable = "person_info", dicText = "name", dicCode = "id")
    @NotNull(message = "监护人不能为空!")

    @ApiModelProperty(value = "监护人")
    private String guardian;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "危大开始时间")
    @TableField(exist = false)
    private String startTime;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "危大开始时间")
    @TableField(exist = false)
    private String endTime;

}
