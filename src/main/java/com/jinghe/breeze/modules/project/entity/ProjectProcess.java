package com.jinghe.breeze.modules.project.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: project_process
 * @Author: jeecg-boot
 * @Date: 2024-04-24
 * @Version: V1.0
 */
@Data
@TableName("project_process")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "project_process对象", description = "project_process")
public class ProjectProcess implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private java.lang.String updateBy;
    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**
     * 组织机构编码
     */
    @ApiModelProperty(value = "组织机构编码")
    private java.lang.String sysOrgCode;
    /**
     * 删除标识
     */
    @Excel(name = "删除标识", width = 15)
    @ApiModelProperty(value = "删除标识")
    private java.lang.Integer delFlag;
    /**
     * 项目id
     */
    @Excel(name = "项目id", width = 15)
    @ApiModelProperty(value = "项目id")
    private java.lang.String projectId;
    /**
     * 是否为形象进度工序（1是/2否）
     */
    @Excel(name = "是否为形象进度工序（1是/2否）", width = 15, dicCode = "yn")
    @Dict(dicCode = "yn")
    @ApiModelProperty(value = "是否为形象进度工序（1是/2否）")
    private java.lang.Integer isProgress;
    /**
     * 工序类型（关联分类字典process_type）
     */
    @Excel(name = "工序类型（关联分类字典process_type）", width = 15)
    @ApiModelProperty(value = "工序类型（关联分类字典process_type）")
    private java.lang.String type;


    /**
     * 理论耗时
     */
    @Excel(name = "理论耗时", width = 15)
    @ApiModelProperty(value = "理论耗时")
    @TableField(updateStrategy = FieldStrategy.IGNORED, insertStrategy = FieldStrategy.IGNORED) //忽略空值检查
    private BigDecimal theoryHours;
    /**
     * 工序名称
     */
    @Excel(name = "工序名称", width = 15)
    @ApiModelProperty(value = "工序名称")
    private java.lang.String name;

    /**
     * 工作内容
     */
    @Excel(name = "工作内容", width = 15)
    @ApiModelProperty(value = "工作内容")
    @TableField(updateStrategy = FieldStrategy.IGNORED, insertStrategy = FieldStrategy.IGNORED) //忽略空值检查
    private java.lang.String workContent;
    /**
     * 工序编码
     */
    @Excel(name = "工序编码", width = 15)
    @ApiModelProperty(value = "工序编码")
    private java.lang.String processCode;
    /**
     * 权重
     */
    @Excel(name = "权重", width = 15)
    @ApiModelProperty(value = "权重")
    private Integer weight;
    /**
     * 工序图标
     */
    @Excel(name = "工序图标", width = 15)
    @ApiModelProperty(value = "工序图标")
    private java.lang.String icon;

    /**
     * 工序图标
     */
    @ApiModelProperty(value = "工序对应模型构件")
    private java.lang.String modelSign;

    /**
     * 是否开启预警
     */
    @Excel(name = "是否开启预警", width = 15, dicCode = "yn")
    @Dict(dicCode = "yn")
    @ApiModelProperty(value = "是否开启预警")
    private java.lang.Integer warningEnable;
    /**
     * 预警信息json文本
     */
    @Excel(name = "预警信息json文本", width = 15)
    @ApiModelProperty(value = "预警信息json文本")
    private java.lang.String information;

    @TableField(exist = false)
    private String planName;

    @TableField(exist = false)
    private String planCode;

}
