package com.jinghe.breeze.modules.settlement.service;

import com.jinghe.breeze.modules.settlement.entity.ContractInfoDetail;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: contract_info_detail
 * @Author: jeecg-boot
 * @Date:   2024-05-31
 * @Version: V1.0
 */
public interface IContractInfoDetailService extends IService<ContractInfoDetail> {

    void setSettledInfo(List<ContractInfoDetail> details);

    /**
     *
     * @param id
     * @param isRoot  true 则id表示合同id ,否则为明细 id
     * @return
     */
    List<ContractInfoDetail> getSettedDetailChildList(String id,boolean isRoot);

    List<ContractInfoDetail> queryContractInfoSettlement(String contractId, String code,String settlementId,String name);

    void setSettledMoney(List<ContractInfoDetail> contractInfoDetails);
}
