package com.jinghe.breeze.modules.construction.controller;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.modules.construction.entity.MonitorDolphinLogDetail;
import com.jinghe.breeze.modules.construction.service.IMonitorDolphinLogDetailService;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.jinghe.breeze.modules.construction.entity.MonitorDolphinLog;
import com.jinghe.breeze.modules.construction.service.IMonitorDolphinLogService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: 观豚日志
 * @Author: jeecg-boot
 * @Date: 2024-05-11
 * @Version: V1.0
 */
@Api(tags = "观豚日志")
@RestController
@RequestMapping("/construction/monitorDolphinLog")
@Slf4j
public class MonitorDolphinLogController extends JeecgController<MonitorDolphinLog, IMonitorDolphinLogService> {
    @Autowired
    private IMonitorDolphinLogService monitorDolphinLogService;

    @Autowired
    private IMonitorDolphinLogDetailService monitorDolphinLogDetailService;


    /**
     * 分页列表查询
     *
     * @param monitorDolphinLog
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "观豚日志-分页列表查询")
    @ApiOperation(value = "观豚日志-分页列表查询", notes = "观豚日志-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(MonitorDolphinLog monitorDolphinLog,
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(name = "start", required = false) String startTime,
            @RequestParam(name = "end", required = false) String endTime,
            @RequestParam(name = "myself", required = false, defaultValue = "false") Boolean myself,
            HttpServletRequest req) {
        QueryWrapper<MonitorDolphinLog> queryWrapper = QueryGenerator.initQueryWrapper(monitorDolphinLog,
                req.getParameterMap());
        queryWrapper.orderByDesc("watch_date").orderByDesc("create_time");
        SimpleDateFormat sdf = new SimpleDateFormat(Common.date.SECONDS);
        if (oConvertUtils.isNotEmpty(startTime)) {
            try {
                Date start = sdf.parse(startTime + " 00:00:00");
                Date end = sdf.parse(endTime + " 23:59:59");
                queryWrapper.between("watch_date", start, end);
            } catch (ParseException e) {
                log.debug(startTime, endTime, "格式化日期出错");
            }
        }
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (myself) {
            queryWrapper.eq("create_by", sysUser.getUsername());
        }
        Page<MonitorDolphinLog> page = new Page<MonitorDolphinLog>(pageNo, pageSize);
        IPage<MonitorDolphinLog> pageList = monitorDolphinLogService.page(page, queryWrapper);
        pageList.getRecords().forEach(monitorDolphinLog1 -> {
            List<MonitorDolphinLogDetail> detailList = monitorDolphinLogDetailService
                    .list(new LambdaQueryWrapper<MonitorDolphinLogDetail>()
                            .eq(MonitorDolphinLogDetail::getWatchId, monitorDolphinLog1.getId()));
            monitorDolphinLog1.setDetails(detailList);
        });
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param monitorDolphinLog
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @AutoLog(value = "观豚日志-添加")
    @ApiOperation(value = "观豚日志-添加", notes = "观豚日志-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody MonitorDolphinLog monitorDolphinLog) {
        //


        return monitorDolphinLogService.addLog(monitorDolphinLog);
    }

    @ApiOperation(value = "根据yyyy-MM-dd获取对当月有海豚的日期列表", notes = "根据yyyy-MM-dd获取对当月有海豚的日期列表")
    @RequestMapping(value = "/getExitsDolphinDates", method = RequestMethod.GET)
    public Result<List<Date>> getExitsDolphinDates(@RequestParam(required = true) String queryDateString) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate date = LocalDate.parse(queryDateString, formatter);
        LocalDateTime startOfMonth = date.with(TemporalAdjusters.firstDayOfMonth()).atStartOfDay();
        LocalDateTime endOfMonth = date.with(TemporalAdjusters.lastDayOfMonth()).atTime(23, 59, 59);
        LambdaQueryWrapper<MonitorDolphinLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.between(MonitorDolphinLog::getWatchDate, startOfMonth, endOfMonth)
                .eq(MonitorDolphinLog::getExsitDolphin, 1);
        List<Date> retList = monitorDolphinLogService.list(queryWrapper).stream()
                .map(MonitorDolphinLog::getWatchDate).distinct().collect(Collectors.toList());
        return Result.OK(retList);
    }

    /**
     * 编辑
     *
     * @param monitorDolphinLog
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @AutoLog(value = "观豚日志-编辑")
    @ApiOperation(value = "观豚日志-编辑", notes = "观豚日志-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody MonitorDolphinLog monitorDolphinLog) {
        return monitorDolphinLogService.editLog(monitorDolphinLog);
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "观豚日志-通过id删除")
    @ApiOperation(value = "观豚日志-通过id删除", notes = "观豚日志-通过id删除")
    @DeleteMapping(value = "/delete")
    @Transactional(rollbackFor = Exception.class)
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        MonitorDolphinLog oldMonitorDolphinLog = monitorDolphinLogService.getById(id);
        if (oldMonitorDolphinLog == null) {
            return Result.error("未找到指定记录");
        }
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String username = sysUser.getUsername();
        if (!ObjectUtil.equal(oldMonitorDolphinLog.getCreateBy(), username)) {
            return Result.error("不能删除他人的记录");
        }
        monitorDolphinLogService.removeById(id);
        monitorDolphinLogDetailService.remove(new LambdaQueryWrapper<MonitorDolphinLogDetail>()
                .eq(MonitorDolphinLogDetail::getWatchId, id));
        return Result.OK("删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "观豚日志-通过id查询")
    @ApiOperation(value = "观豚日志-通过id查询", notes = "观豚日志-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        MonitorDolphinLog monitorDolphinLog = monitorDolphinLogService.getById(id);
        if (monitorDolphinLog == null) {
            return Result.error(404, "未找到指定记录");
        }
        List<MonitorDolphinLogDetail> detailList = monitorDolphinLogDetailService
                .list(new LambdaQueryWrapper<MonitorDolphinLogDetail>()
                        .eq(MonitorDolphinLogDetail::getWatchId, id)
                        .eq(MonitorDolphinLogDetail::getDelFlag, Common.delete_flag.OK));
        monitorDolphinLog.setDetails(detailList);
        return Result.OK(monitorDolphinLog);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param monitorDolphinLog
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, MonitorDolphinLog monitorDolphinLog) {
        return super.exportXls(request, monitorDolphinLog, MonitorDolphinLog.class, "观豚日志");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, MonitorDolphinLog.class);
    }

    /**
     * 按月份"YYYY-MM" 格式查询当月的所有记录,以及详情
     *
     * @param monthString
     * @return
     */
    @RequestMapping(value = "/queryByMonth")
    public Result<?> queryByMonth(@RequestParam(name = "monthString", required = true) String monthString) {

     List<MonitorDolphinLog> list =     monitorDolphinLogService.queryByMonth(monthString);
     return Result.OK(list);
    }

    /**
     * 获取登录的账号在指定日期登的船舶名称
     *
     * @return
     */
    @ApiOperation(value = "获取登录账号在指定日期登的船舶名称", notes = "YYYY-MM-DD")
    @RequestMapping(value = "/getSaillingShipName")
    public Result<?> getSaillingShipName(@RequestParam(name = "dateStr", required = true) String dateStr) {
        return monitorDolphinLogService.getSaillingShipName(dateStr);
    }
}
