package com.jinghe.breeze.modules.video.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinghe.breeze.modules.video.entity.VideoInfo;
import com.jinghe.breeze.modules.video.service.IVideoInfoService;
import jodd.util.StringUtil;
import org.jeecg.common.exception.JeecgBootException;
import com.jinghe.breeze.modules.video.entity.VideoFolder;
import com.jinghe.breeze.modules.video.mapper.VideoFolderMapper;
import com.jinghe.breeze.modules.video.service.IVideoFolderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: video_folder
 * @Author: jeecg-boot
 * @Date: 2024-06-12
 * @Version: V1.0
 */
@Service
public class VideoFolderServiceImpl extends ServiceImpl<VideoFolderMapper, VideoFolder> implements IVideoFolderService {

    @Autowired
    private IVideoInfoService videoInfoService;

    private void setCanMoveUpDown(List<VideoFolder> videoFolderList, List<VideoFolder> allList) {
        for (VideoFolder tree : videoFolderList) {
            String pidVal = tree.getPid();
            VideoFolder biggerBrother = allList.stream()
                    .filter(d -> d.getPid().equals(pidVal) && d.getSortNo() > tree.getSortNo()).findFirst().orElse(null);
            tree.setDownEnable(biggerBrother != null);//没有比自己更大的,不能下移
            VideoFolder littleBrother = allList.stream()
                    .filter(d -> d.getPid().equals(pidVal) && d.getSortNo() < tree.getSortNo()).findFirst().orElse(null);
            tree.setUpEnable(littleBrother != null);//没有比自己更小的,不能上移
        }
    }


    public IPage<VideoFolder> getChildList(VideoFolder videoFolder) {
        String id = videoFolder.getPid();
        if (StringUtil.isEmpty(id)) {
            throw new JeecgBootException("未指定父节点");
        }
        List<VideoFolder> list = baseMapper.selectList(new LambdaQueryWrapper<VideoFolder>().eq(VideoFolder::getPid, id).orderByAsc(VideoFolder::getSortNo));
        IPage<VideoFolder> pageList = new Page<>(1, list.size(), list.size());
        List<VideoFolder> allList = baseMapper.selectList(new LambdaQueryWrapper<VideoFolder>());
        setCanMoveUpDown(list, allList);
        pageList.setRecords(list);
        return pageList;
    }

    public IPage<VideoFolder> getChildListBatch(String parentIds) {
        List<String> parentIdList = Arrays.asList(parentIds.trim().split(",")).stream().map(String::trim).filter(s -> !StringUtil.isEmpty(s)).collect(Collectors.toList());
        if (parentIdList.isEmpty()) {
            throw new JeecgBootException("未匹配到父节点");
        }
        List<VideoFolder> list = baseMapper.selectList(new LambdaQueryWrapper<VideoFolder>().in(VideoFolder::getPid, parentIdList).orderByAsc(VideoFolder::getSortNo));
        List<VideoFolder> allList = baseMapper.selectList(new LambdaQueryWrapper<VideoFolder>());
        IPage<VideoFolder> pageList = new Page<>(1, list.size(), list.size());
        setCanMoveUpDown(list, allList);
        pageList.setRecords(list);
        return pageList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addVideoFolder(VideoFolder videoFolder) {
        String pid = videoFolder.getPid();
        if (pid == null) {
            throw new JeecgBootException("未指定父节点");
        }
        VideoFolder parent = baseMapper.selectOne(new LambdaQueryWrapper<VideoFolder>().eq(VideoFolder::getId, pid));
        if (parent == null) {
            throw new JeecgBootException("指定的父节点不存在");
        }
        videoFolder.setLayer(parent.getLayer() + 1);
        int sortNo = baseMapper.selectList(new LambdaQueryWrapper<VideoFolder>().eq(VideoFolder::getPid, pid).select(VideoFolder::getSortNo).orderByDesc(VideoFolder::getSortNo).last("LIMIT 1")).stream().mapToInt(VideoFolder::getSortNo).findFirst().orElse(-1);
        videoFolder.setSortNo(sortNo + 1);
        baseMapper.insert(videoFolder);
        if (!Objects.equals(parent.getHasChild(), "1")) {
            parent.setHasChild("1");
            baseMapper.updateById(parent);
        }
        baseMapper.updateById(parent);
    }

    @Transactional(rollbackFor = Exception.class)
    public void moveFolder(VideoFolder videoFolder, boolean isUp) {
        videoFolder = baseMapper.selectById(videoFolder.getId());
        if (videoFolder == null) {
            throw new JeecgBootException("记录不存在");
        }
        String pid = videoFolder.getPid();
        LambdaQueryWrapper<VideoFolder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(VideoFolder::getPid, pid);
        if (isUp) {
            wrapper.lt(VideoFolder::getSortNo, videoFolder.getSortNo()).orderByDesc(VideoFolder::getSortNo);
        } else { //down
            wrapper.gt(VideoFolder::getSortNo, videoFolder.getSortNo()).orderByAsc(VideoFolder::getSortNo);
        }
        wrapper.last("LIMIT 1");
        VideoFolder target = baseMapper.selectOne(wrapper);
        if (target == null) {
            throw new JeecgBootException(isUp ? "被移动的对象已经是第一个" : "被移动的对象已经是最后一个");
        }
        int tmpSort = target.getSortNo();
        target.setSortNo(videoFolder.getSortNo());
        videoFolder.setSortNo(tmpSort);//交换双方的sort
        baseMapper.updateById(videoFolder);
        baseMapper.updateById(target);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteVideoFolder(String id) throws JeecgBootException {

        VideoFolder folder = baseMapper.selectById(id);
        if (folder == null) {
            throw new JeecgBootException("记录不存在!");
        }
        if (Objects.equals(IVideoFolderService.ROOT_PID_VALUE, folder.getPid())) {
            throw new JeecgBootException("根目录不能删除");
        }
        VideoFolder childFolder = baseMapper.selectOne(new LambdaQueryWrapper<VideoFolder>().eq(VideoFolder::getPid, id).last("LIMIT 1"));
        if (childFolder != null) {
            throw new JeecgBootException("请先删除子目录");
        }
        VideoInfo videoInfo = videoInfoService.getOne(new LambdaQueryWrapper<VideoInfo>().eq(VideoInfo::getFolderId, id).last("LIMIT 1"), false);
        if (videoInfo != null) {
            throw new JeecgBootException("请先删除该目录下的视频!");
        }
        int childCount = baseMapper.selectCount(new LambdaQueryWrapper<VideoFolder>().eq(VideoFolder::getPid, folder.getPid()));
        if (childCount == 1) { //自身父级只有一个子节点,则更新has_child
            baseMapper.update(null, new UpdateWrapper<VideoFolder>().set("has_child", "0").eq("id", folder.getPid()));
        }
        baseMapper.deleteById(id);
    }

    /**
     * 获取前两层的信息
     *
     * @return
     */
    public List<VideoFolder> getTwoLayerNodeList() {
        VideoFolder folder = baseMapper.selectOne(new LambdaQueryWrapper<VideoFolder>().eq(VideoFolder::getPid, "0"));
        if (folder == null) {
            folder = new VideoFolder();
            folder.setPid("0");
            folder.setLayer(0);
            folder.setSortNo(0);
            folder.setName("自定义目录");
            baseMapper.insert(folder);
        }
        List<VideoFolder> mapList = new ArrayList<>();
        List<VideoFolder> allList = baseMapper.selectList(new LambdaQueryWrapper<VideoFolder>());
        VideoFolder root = allList.stream().filter(d -> IVideoFolderService.ROOT_PID_VALUE.equals(d.getPid())).findFirst().orElse(null);
        if (root != null) {
            mapList.add(root);//加入了根节点
        } else {
            return new ArrayList<>(); //根节点不存在返回空
        }
        List<VideoFolder> children = allList.stream().sorted(Comparator.comparing(VideoFolder::getSortNo)).filter(d -> Objects.equals(d.getLayer(), 1)).collect(Collectors.toList());
        root.setChildren(children);
        setCanMoveUpDown(mapList, allList);
        return mapList;
    }

    public void editFolder(VideoFolder videoFolder) {
        boolean result = checkDuplicateSameLayer(videoFolder.getId(), videoFolder.getName());
        if (result) {
            baseMapper.updateById(videoFolder);
        } else {
            throw new JeecgBootException("同一级节点下已存在该目录名,请检查");
        }
    }

    /**
     * 校验同级别下是否有重名的记录,记录不存在抛出异常,校验通过返回true
     *
     * @param id
     * @return
     */
    public boolean checkDuplicateSameLayer(String id, String name) {
        if (StringUtil.isEmpty(id) || StringUtil.isEmpty(name)) {
            throw new JeecgBootException("参数错误");
        }
        VideoFolder entity = baseMapper.selectOne(new LambdaQueryWrapper<VideoFolder>().eq(VideoFolder::getId, id));
        if (entity == null) {
            throw new JeecgBootException("记录不存在");
        }
        VideoFolder folder = baseMapper.selectOne(new LambdaQueryWrapper<VideoFolder>()
                .eq(VideoFolder::getLayer, entity.getLayer())
                .eq(VideoFolder::getName, name)
                .ne(VideoFolder::getId, entity.getId())
                .last("Limit 1"));
        return folder == null; //为true 表示校验通过
    }
}
