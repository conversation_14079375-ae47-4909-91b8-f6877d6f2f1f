package com.jinghe.breeze.modules.video.controller;

import com.jinghe.breeze.modules.construction.service.IConstructionLogService;
import com.jinghe.breeze.modules.video.entity.vo.VideoFolderNode;
import com.jinghe.breeze.modules.video.service.IVideoFolderService;
import org.apache.shiro.authz.annotation.RequiresPermissions;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import com.jinghe.breeze.modules.video.entity.VideoInfo;
import com.jinghe.breeze.modules.video.service.IVideoInfoService;

import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: video_info
 * @Author: jeecg-boot
 * @Date: 2024-06-13
 * @Version: V1.0
 */
@Api(tags = "video_info")
@RestController
@RequestMapping("/video/videoInfo")
@Slf4j
public class VideoInfoController extends JeecgController<VideoInfo, IVideoInfoService> {
    @Autowired
    private IVideoInfoService videoInfoService;

    @Autowired
    private IVideoFolderService folderService;

    @Autowired
    private IConstructionLogService constructionLogService;

    /**
     * 分页列表查询
     *
     * @param videoInfo
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "video_info-分页列表查询")
    @ApiOperation(value = "video_info-分页列表查询", notes = "video_info-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(VideoInfo videoInfo,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        return videoInfoService.queryPageList(videoInfo, pageNo, pageSize, req);
    }

    /**
     * 添加视频或图片文件到指定id的目录
     *
     * @param videoInfoList
     * @return
     */
    @AutoLog(value = "video_info-添加")
    @ApiOperation(value = "video_info-添加", notes = "video_info-添加")
    @RequiresPermissions("videoInfo:add")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody List<VideoInfo> videoInfoList) {
        return videoInfoService.add(videoInfoList);
    }

    /**
     * 编辑
     *
     * @param videoInfo
     * @return
     */
    @AutoLog(value = "video_info-编辑")
    @ApiOperation(value = "video_info-编辑", notes = "video_info-编辑")
    @RequiresPermissions("videoInfo:edit")
    @PutMapping(value = "/edit")
    public Result<?> edit(@Validated @RequestBody VideoInfo videoInfo) {
        videoInfoService.updateById(videoInfo);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "video_info-通过id删除")
    @ApiOperation(value = "video_info-通过id删除", notes = "video_info-通过id删除")
    @RequiresPermissions("videoInfo:delete")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        videoInfoService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "video_info-批量删除")
    @ApiOperation(value = "video_info-批量删除", notes = "video_info-批量删除")
    @RequiresPermissions("videoInfo:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.videoInfoService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "video_info-通过id查询")
    @ApiOperation(value = "video_info-通过id查询", notes = "video_info-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        VideoInfo videoInfo = videoInfoService.getById(id);
        if (videoInfo == null) {
            return Result.error(404, "未找到对应数据");
        }
        return Result.OK(videoInfo);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param videoInfo
     */
    @RequiresPermissions("videoInfo:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, VideoInfo videoInfo) {
        return super.exportXls(request, videoInfo, VideoInfo.class, "video_info");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("videoInfo:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, VideoInfo.class);
    }


    @RequestMapping(value = "/getTreeData", method = RequestMethod.GET)
    public Result<?> getTreeData() {
        List<VideoFolderNode> nodeList = videoInfoService.getTreeData();
        return Result.OK(nodeList);
    }

}
