package com.jinghe.breeze.modules.project.controller;

import java.util.Arrays;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.jinghe.breeze.modules.project.service.IProjectCameraService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.jinghe.breeze.modules.project.entity.ProjectCamera;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: project_camera
 * @Author: jeecg-boot
 * @Date: 2024-04-11
 * @Version: V1.0
 */
@Api(tags = "project_camera")
@RestController
@RequestMapping("/project/projectCamera")
@Slf4j
public class ProjectCameraController extends JeecgController<ProjectCamera, IProjectCameraService> {
    @Autowired
    private IProjectCameraService ********************;

    /**
     * 分页列表查询
     *
     * @param projectCamera
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "project_camera-分页列表查询")
    @ApiOperation(value = "project_camera-分页列表查询", notes = "project_camera-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(ProjectCamera projectCamera,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<ProjectCamera> queryWrapper = QueryGenerator.initQueryWrapper(projectCamera, req.getParameterMap());
        Page<ProjectCamera> page = new Page<>(pageNo, pageSize);
        IPage<ProjectCamera> pageList = ********************.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    @AutoLog(value = "project_camera-无分页所有摄像头")
    @ApiOperation(value = "project_camera-无分页所有摄像头", notes = "project_camera-无分页所有摄像头")
    @GetMapping(value = "/getAllCamera")
    public Result<?> getAllCamera() {
        return Result.OK(********************.list());
    }


    /**
     * 添加
     *
     * @param projectCamera
     * @return
     */
    @AutoLog(value = "project_camera-添加")
    @ApiOperation(value = "project_camera-添加", notes = "project_camera-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody ProjectCamera projectCamera) {
        ********************.save(projectCamera);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param projectCamera
     * @return
     */
    @AutoLog(value = "project_camera-编辑")
    @ApiOperation(value = "project_camera-编辑", notes = "project_camera-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody ProjectCamera projectCamera) {
        ********************.updateById(projectCamera);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "project_camera-通过id删除")
    @ApiOperation(value = "project_camera-通过id删除", notes = "project_camera-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        ********************.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "project_camera-批量删除")
    @ApiOperation(value = "project_camera-批量删除", notes = "project_camera-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.********************.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "project_camera-通过id查询")
    @ApiOperation(value = "project_camera-通过id查询", notes = "project_camera-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        ProjectCamera projectCamera = ********************.getById(id);
        if (projectCamera == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(projectCamera);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param projectCamera
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ProjectCamera projectCamera) {
        return super.exportXls(request, projectCamera, ProjectCamera.class, "project_camera");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ProjectCamera.class);
    }

}
