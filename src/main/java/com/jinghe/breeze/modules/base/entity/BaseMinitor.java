package com.jinghe.breeze.modules.base.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
 * @Description: 监控设备
 * @Author: jeecg-boot
 * @Date: 2024-08-06
 * @Version: V1.0
 */
@Data
@TableName("base_minitor")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "base_minitor对象", description = "监控设备")
public class BaseMinitor implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;

    /**删除状态（0，正常，1已删除）*/
    @Excel(name = "删除状态（0，正常，1已删除）", width = 15)
    @ApiModelProperty(value = "删除状态（0，正常，1已删除）")
    private String delFlag;

    /**
     * 监控名称
     */
    @Excel(name = "监控名称", width = 15)
    @ApiModelProperty(value = "监控名称")
    private String minitorName;
    /**
     * 安装区域
     */
    @Excel(name = "安装区域", width = 20)
    @ApiModelProperty(value = "安装区域")
    private String installArea;
    /**
     * 设备序列号
     */
    @Excel(name = "设备序列号", width = 50)
    @ApiModelProperty(value = "设备序列号")
    private String equipmentSerial;
    /**
     * 设备服务地址
     */
    @Excel(name = "设备服务地址", width = 500)
    @ApiModelProperty(value = "设备服务地址")
    private String equipmentUrl;
    /**
     * 监控类型
     */
    @Excel(name = "监控类型", width = 20)
    @ApiModelProperty(value = "监控类型")
    private String minitorType;
    /**
     * 安装位置
     */
    @Excel(name = "安装位置", width = 20)
    @ApiModelProperty(value = "安装位置")
    private String installPosition;
    /**
     * 安装日期
     */
    @Excel(name = "安装日期", width = 20, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "安装日期")
    private java.util.Date installDate;
    /**
     * 备注
     */
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 坐标点X轴
     */
    @Excel(name = "坐标点X轴", width = 30)
    @ApiModelProperty(value = "坐标点X轴")
    private String positionX;
    /**
     * 坐标点Y轴
     */
    @Excel(name = "坐标点Y轴", width = 30)
    @ApiModelProperty(value = "坐标点Y轴")
    private String positionY;

}
