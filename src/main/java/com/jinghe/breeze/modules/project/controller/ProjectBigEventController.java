package com.jinghe.breeze.modules.project.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinghe.breeze.modules.project.entity.ProjectBigEvent;
import com.jinghe.breeze.modules.project.service.IProjectBigEventService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

/**
 * @Description: project_big_event
 * @Author: jeecg-boot
 * @Date: 2024-05-10
 * @Version: V1.0
 */
@Api(tags = "project_big_event")
@RestController
@RequestMapping("/project/projectBigEvent")
@Slf4j
public class ProjectBigEventController extends JeecgController<ProjectBigEvent, IProjectBigEventService> {
    @Autowired
    private IProjectBigEventService projectBigEventService;

    /**
     * 分页列表查询
     *
     * @param projectBigEvent
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "project_big_event-分页列表查询")
    @ApiOperation(value = "project_big_event-分页列表查询", notes = "project_big_event-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(ProjectBigEvent projectBigEvent,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<ProjectBigEvent> queryWrapper = QueryGenerator.initQueryWrapper(projectBigEvent, req.getParameterMap());
        Page<ProjectBigEvent> page = new Page<>(pageNo, pageSize);
        IPage<ProjectBigEvent> pageList = projectBigEventService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param projectBigEvent
     * @return
     */
    @AutoLog(value = "project_big_event-添加")
    @ApiOperation(value = "project_big_event-添加", notes = "project_big_event-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody ProjectBigEvent projectBigEvent) {
        projectBigEventService.save(projectBigEvent);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param projectBigEvent
     * @return
     */
    @AutoLog(value = "project_big_event-编辑")
    @ApiOperation(value = "project_big_event-编辑", notes = "project_big_event-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody ProjectBigEvent projectBigEvent) {
        try {
            projectBigEventService.updateById(projectBigEvent);
            return Result.OK("编辑成功!");
        } catch (JeecgBootException e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "project_big_event-通过id删除")
    @ApiOperation(value = "project_big_event-通过id删除", notes = "project_big_event-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        projectBigEventService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "project_big_event-批量删除")
    @ApiOperation(value = "project_big_event-批量删除", notes = "project_big_event-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.projectBigEventService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "project_big_event-通过id查询")
    @ApiOperation(value = "project_big_event-通过id查询", notes = "project_big_event-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        ProjectBigEvent projectBigEvent = projectBigEventService.getById(id);
        if (projectBigEvent == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(projectBigEvent);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param projectBigEvent
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ProjectBigEvent projectBigEvent) {
        return super.exportXls(request, projectBigEvent, ProjectBigEvent.class, "project_big_event");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ProjectBigEvent.class);
    }

}
