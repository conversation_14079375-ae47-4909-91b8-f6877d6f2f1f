package com.jinghe.breeze.modules.education.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.common.enums.DelFlagEnum;
import com.jinghe.breeze.modules.education.entity.EduQuestion;
import com.jinghe.breeze.modules.education.entity.EduQuestionTags;
import com.jinghe.breeze.modules.education.mapper.EduQuestionMapper;
import com.jinghe.breeze.modules.education.service.IEduQuestionService;
import com.jinghe.breeze.modules.education.service.IEduQuestionTagsService;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 题库
 * @Author: jeecg-boot
 * @Date: 2025-06-24
 * @Version: V1.0
 */
@Service
public class EduQuestionServiceImpl extends ServiceImpl<EduQuestionMapper, EduQuestion> implements IEduQuestionService {

    @Autowired
    private IEduQuestionTagsService eduQuestionTagsService;

    public List<EduQuestion> getRandomQuestionsByTypeAndTags(@NotNull String type,
                                                             @NotNull String tags,
                                                             @NotNull Integer count) {
        List<String> tagList = new ArrayList<>();
        if (StringUtils.hasLength(tags)) {
            tagList = Arrays.stream(tags.split(","))
                    .map(String::trim)
                    .filter(tag -> !tag.isEmpty())
                    .collect(Collectors.toList());
        }

        // 构建基础查询条件：题目类型和删除标识
        LambdaQueryWrapper<EduQuestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EduQuestion::getType, type)
                .eq(EduQuestion::getDelFlag, DelFlagEnum.NORMAL.getType());

        // 如果有标签条件，通过关联表查询
        if (!tagList.isEmpty()) {
            // 先从关联表中查询符合标签条件的题目ID
            LambdaQueryWrapper<EduQuestionTags> tagWrapper = new LambdaQueryWrapper<>();
            tagWrapper.in(EduQuestionTags::getTagValue, tagList)
                    .eq(EduQuestionTags::getDelFlag, DelFlagEnum.NORMAL.getType());
            List<EduQuestionTags> questionTags = eduQuestionTagsService.list(tagWrapper);

            if (questionTags.isEmpty()) {
                String errMsg = String.format("标签 `%s` 下没有找到 `%s` 类型的题目", tags, type);
                throw new RuntimeException(errMsg);
            }

            // 提取题目ID列表
            List<String> questionIds = questionTags.stream()
                    .map(EduQuestionTags::getQuestionId)
                    .distinct()
                    .collect(Collectors.toList());

            // 添加题目ID条件
            queryWrapper.in(EduQuestion::getId, questionIds);
        }

        List<EduQuestion> lst = baseMapper.selectList(queryWrapper);
        if (lst.size() < count) {
            String errMsg = String.format("标签 `%s` 下的 `%s` 类型题目数量不足,需要%d题,只有%d题", tags, type, count, lst.size());
            throw new RuntimeException(errMsg);
        }

        // 随机打乱并返回指定数量的题目
        List<EduQuestion> shuffledList = new ArrayList<>(lst);
        Collections.shuffle(shuffledList);
        return new ArrayList<>(shuffledList.subList(0, count));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveQuestionWithTags(EduQuestion eduQuestion) {
        // 保存题目
        boolean saved = save(eduQuestion);
        if (!saved) {
            return false;
        }

        // 维护题目标签关联表
        return saveQuestionTags(eduQuestion);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateQuestionWithTags(EduQuestion eduQuestion) {
        // 更新题目
        boolean updated = updateById(eduQuestion);
        if (!updated) {
            return false;
        }

        // 删除原有的标签关联
        removeQuestionTags(eduQuestion.getId());

        // 重新添加标签关联
        return saveQuestionTags(eduQuestion);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeQuestionWithTags(String id) {
        // 删除题目
        boolean removed = removeById(id);
        if (!removed) {
            return false;
        }

        // 删除关联的标签
        return removeQuestionTags(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeQuestionsWithTags(List<String> ids) {
        // 删除题目
        boolean removed = removeByIds(ids);
        if (!removed) {
            return false;
        }

        // 删除关联的标签
        LambdaQueryWrapper<EduQuestionTags> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.in(EduQuestionTags::getQuestionId, ids);
        return eduQuestionTagsService.remove(deleteWrapper);
    }

    /**
     * 保存题目标签关联
     */
    private boolean saveQuestionTags(EduQuestion eduQuestion) {
        if (!StringUtils.hasLength(eduQuestion.getTags())) {
            return true; // 没有标签也算成功
        }

        String[] tagArray = eduQuestion.getTags().split(",");
        List<EduQuestionTags> questionTagsList = new ArrayList<>();

        for (String tag : tagArray) {
            if (StringUtils.hasLength(tag.trim())) {
                EduQuestionTags questionTag = new EduQuestionTags();
                questionTag.setQuestionId(eduQuestion.getId());
                questionTag.setTagValue(tag.trim());
                questionTagsList.add(questionTag);
            }
        }

        return questionTagsList.isEmpty() || eduQuestionTagsService.saveBatch(questionTagsList);
    }

    /**
     * 删除题目标签关联
     */
    private boolean removeQuestionTags(String questionId) {
        LambdaQueryWrapper<EduQuestionTags> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(EduQuestionTags::getQuestionId, questionId);
        return eduQuestionTagsService.remove(deleteWrapper);
    }
}
