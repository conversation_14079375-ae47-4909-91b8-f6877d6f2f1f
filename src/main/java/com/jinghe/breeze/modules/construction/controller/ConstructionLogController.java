package com.jinghe.breeze.modules.construction.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.jinghe.breeze.modules.construction.entity.vo.ConstructionLogResult;
import com.jinghe.breeze.modules.construction.entity.vo.ConstructionLogVo;
import org.jeecg.common.api.vo.Result;
import com.jinghe.breeze.modules.construction.entity.ConstructionLog;
import com.jinghe.breeze.modules.construction.service.IConstructionLogService;

import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: construction_log
 * @Author: liuhan
 * @Date: 2024-05-09
 * @Version: V1.0
 */
@Api(tags = "施工日志")
@RestController
@RequestMapping("/construction/constructionLog")
@Slf4j
public class ConstructionLogController extends JeecgController<ConstructionLog, IConstructionLogService> {
    @Autowired
    private IConstructionLogService constructionLogService;

    /**
     *
     *
     * @return
     */
    @AutoLog(value = "列表查询")
    @ApiOperation(value = "列表查询", notes = "列表查询")
    @GetMapping(value = "/list")
    public Result<?> list(@RequestParam(name = "projectId", required = false) String projectId) {
        return constructionLogService.getAllList(projectId);
    }


    /**
     *
     *
     * @return
     */
    @AutoLog(value = "APP列表查询")
    @ApiOperation(value = "APP列表查询", notes = "APP列表查询")
    @GetMapping(value = "/report")
    public Result<?> report(@RequestParam(name = "projectId", required = false) String projectId,
                            @RequestParam(name = "type", required = false) String type) {
        return constructionLogService.report(projectId,type);
    }

    /**
     * 分页列表查询
     *
     * @param
     * @param pbsId
     * @param
     * @param
     * @return
     */
    @AutoLog(value = "通过PBS查询工序列表")
    @ApiOperation(value = "通过PBS查询工序列表", notes = "通过PBS查询工序列表")
    @GetMapping(value = "/queryList")
    public Result<?> queryList(@RequestParam(name = "pbsId") String pbsId,
                               @RequestParam(name = "projectId", required = false) String projectId,
                               @RequestParam(name = "processType", required = false) String processType) {
        return constructionLogService.queryList(pbsId, projectId,processType);
    }

    /**
     * 离线缓存批量提交
     *
     *
     */
    @AutoLog(value = "离线缓存批量提交")
    @ApiOperation(value = "离线缓存批量提交", notes = "离线缓存批量提交")
    @PostMapping(value = "/commitBatch")
    public Result<?> commitBatch(@RequestBody List<ConstructionLog> data) {
        List<ConstructionLogResult> constructionLogResultVos = constructionLogService.commitBatch(data);
        return Result.OK(constructionLogResultVos);
    }

    /**
     * 填报
     *
     * @param
     * @param
     * @param
     * @param
     * @return
     */
    @AutoLog(value = "填报")
    @ApiOperation(value = "填报", notes = "填报")
    @PostMapping(value = "/addList")
    public Result<?> addList(@RequestBody ConstructionLogVo constructionLogVo) {
        constructionLogService.saveList(constructionLogVo);
        return Result.OK();
    }

    /**
     * 清除
     *
     * @param
     * @param
     * @param
     * @param
     * @return
     */
    @AutoLog(value = "清除")
    @ApiOperation(value = "清除", notes = "清除")
    @PostMapping(value = "/clear")
    public Result<?> clear(@RequestBody ConstructionLog constructionLog) {
        constructionLogService.clear(constructionLog);
        return Result.OK();
    }

    /**
     * 添加
     *
     * @param constructionLog
     * @return
     */
    @AutoLog(value = "construction_log-添加")
    @ApiOperation(value = "construction_log-添加", notes = "construction_log-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody ConstructionLog constructionLog) {
        constructionLogService.save(constructionLog);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param constructionLog
     * @return
     */
    @AutoLog(value = "construction_log-编辑")
    @ApiOperation(value = "construction_log-编辑", notes = "construction_log-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody ConstructionLog constructionLog) {
        constructionLogService.updateById(constructionLog);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "construction_log-通过id删除")
    @ApiOperation(value = "construction_log-通过id删除", notes = "construction_log-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        constructionLogService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "construction_log-批量删除")
    @ApiOperation(value = "construction_log-批量删除", notes = "construction_log-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.constructionLogService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "construction_log-通过id查询")
    @ApiOperation(value = "construction_log-通过id查询", notes = "construction_log-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        ConstructionLog constructionLog = constructionLogService.getById(id);
        if (constructionLog == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(constructionLog);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param constructionLog
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ConstructionLog constructionLog) {
        return super.exportXls(request, constructionLog, ConstructionLog.class, "construction_log");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ConstructionLog.class);
    }

}
