package com.jinghe.breeze.modules.safety.controller;

import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.modules.safety.entity.SafeFileNoticeDetail;
import com.jinghe.breeze.modules.safety.service.ISafeFileNoticeDetailService;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;

import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import com.jinghe.breeze.modules.safety.entity.SafeFileNotice;
import com.jinghe.breeze.modules.safety.service.ISafeFileNoticeService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: 文件通知管理
 * @Author: jeecg-boot
 * @Date: 2025-06-26
 * @Version: V1.0
 */
@Api(tags = "文件通知管理")
@RestController
@RequestMapping("/safety/safeFileNotice")
@Slf4j
public class SafeFileNoticeController extends JeecgController<SafeFileNotice, ISafeFileNoticeService> {
    @Autowired
    private ISafeFileNoticeService safeFileNoticeService;
    @Autowired
    private ISafeFileNoticeDetailService safeFileNoticeDetailService;
    @Autowired
    private ISysUserService userService;


    /**
     * 分页列表查询
     *
     * @param safeFileNotice
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "文件通知管理-分页列表查询")
    @ApiOperation(value = "文件通知管理-分页列表查询", notes = "文件通知管理-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(SafeFileNotice safeFileNotice,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        QueryWrapper<SafeFileNotice> queryWrapper = QueryGenerator.initQueryWrapper(safeFileNotice, req.getParameterMap());
        queryWrapper.eq("create_by", sysUser.getUsername()).or().like("recipient", "%" + sysUser.getId() + "%");
        Page<SafeFileNotice> page = new Page<SafeFileNotice>(pageNo, pageSize);
        IPage<SafeFileNotice> pageList = safeFileNoticeService.page(page, queryWrapper);
        pageList.getRecords().forEach(safeFileNotice1 -> {
            List<SafeFileNoticeDetail> fileNoticeDetails = safeFileNoticeDetailService.list(new QueryWrapper<SafeFileNoticeDetail>().eq("base_id", safeFileNotice1.getId()));
            fileNoticeDetails.forEach(safeFileNoticeDetail -> {
                SysUser byId = userService.getById(safeFileNoticeDetail.getRecipient());
                safeFileNoticeDetail.setRecipient(byId.getRealname());
            });

            safeFileNotice1.setSafeFileNoticeDetailList(fileNoticeDetails);
        });
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param safeFileNotice
     * @return
     */
    @AutoLog(value = "文件通知管理-添加")
    @ApiOperation(value = "文件通知管理-添加", notes = "文件通知管理-添加")
    @RequiresPermissions("safeFileNotice:add")
    @PostMapping(value = "/add")
    public Result<?> add(@Validated @RequestBody SafeFileNotice safeFileNotice) {
        safeFileNotice.setReceivingStatus(Common.RECEIVING_STATUS.wd);
        safeFileNoticeService.save(safeFileNotice);
        String[] split = safeFileNotice.getRecipient().split(",");
        for (String s : split) {
            SafeFileNoticeDetail safeFileNoticeDetail = new SafeFileNoticeDetail();
            safeFileNoticeDetail.setRecipient(s);
            safeFileNoticeDetail.setReadStatus(Common.READ_STATUS.unread);
            safeFileNoticeDetail.setBaseId(safeFileNotice.getId());
            safeFileNoticeDetailService.save(safeFileNoticeDetail);
        }
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param safeFileNotice
     * @return
     */
    @AutoLog(value = "文件通知管理-编辑")
    @ApiOperation(value = "文件通知管理-编辑", notes = "文件通知管理-编辑")
    @RequiresPermissions("safeFileNotice:edit")
    @PutMapping(value = "/edit")
    public Result<?> edit(@Validated @RequestBody SafeFileNotice safeFileNotice) {
        safeFileNoticeService.updateById(safeFileNotice);
        return Result.OK("编辑成功!");
    }

    @AutoLog(value = "查看")
    @ApiOperation(value = "查看", notes = "查看")
    @PostMapping(value = "/view")
    public Result<?> view(@Validated @RequestBody SafeFileNotice safeFileNotice) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<SafeFileNoticeDetail> safeFileNoticeDetails = safeFileNoticeDetailService.list(new QueryWrapper<SafeFileNoticeDetail>()
                .eq("base_id", safeFileNotice.getId()));
        Map<String, List<SafeFileNoticeDetail>> collect = safeFileNoticeDetails.stream().collect(Collectors.groupingBy(SafeFileNoticeDetail::getReadStatus));
        List<SafeFileNoticeDetail> weidu = collect.get(Common.READ_STATUS.unread);
        if (Objects.isNull(weidu)) {
            return Result.OK("添加成功！");
        }
        if (weidu.size()-1==0) {
            safeFileNotice.setReceivingStatus(Common.RECEIVING_STATUS.yd);
        }else {
            safeFileNotice.setReceivingStatus(Common.RECEIVING_STATUS.bfyd);
        }
        boolean bool = false;
        for (SafeFileNoticeDetail safeFileNoticeDetail : safeFileNoticeDetails) {
            if (sysUser.getId().equals(safeFileNoticeDetail.getRecipient())) {
                safeFileNoticeDetail.setReadStatus(Common.READ_STATUS.read);
                safeFileNoticeDetail.setReadTime(new Date());
                safeFileNoticeDetailService.updateById(safeFileNoticeDetail);
                bool = true;
            }
        }
        if (bool) {
            safeFileNoticeService.updateById(safeFileNotice);
        }
        return Result.OK("添加成功！");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "文件通知管理-通过id删除")
    @ApiOperation(value = "文件通知管理-通过id删除", notes = "文件通知管理-通过id删除")
    @RequiresPermissions("safeFileNotice:delete")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        safeFileNoticeService.removeById(id);
        safeFileNoticeDetailService.remove(new QueryWrapper<SafeFileNoticeDetail>().eq("base_id", id));
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "文件通知管理-批量删除")
    @ApiOperation(value = "文件通知管理-批量删除", notes = "文件通知管理-批量删除")
    @RequiresPermissions("safeFileNotice:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.safeFileNoticeService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "文件通知管理-通过id查询")
    @ApiOperation(value = "文件通知管理-通过id查询", notes = "文件通知管理-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        SafeFileNotice safeFileNotice = safeFileNoticeService.getById(id);
        if (safeFileNotice == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(safeFileNotice);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param safeFileNotice
     */
    @RequiresPermissions("safeFileNotice:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SafeFileNotice safeFileNotice) {
        return super.exportXls(request, safeFileNotice, SafeFileNotice.class, "文件通知管理");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("safeFileNotice:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SafeFileNotice.class);
    }

}
