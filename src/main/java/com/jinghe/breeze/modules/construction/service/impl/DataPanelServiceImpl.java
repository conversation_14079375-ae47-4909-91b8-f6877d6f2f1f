package com.jinghe.breeze.modules.construction.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.common.constants.enums.ProjectPbsEnum;
import com.jinghe.breeze.modules.construction.entity.ConstructionLog;
import com.jinghe.breeze.modules.construction.entity.vo.ProcessVo;
import com.jinghe.breeze.modules.construction.mapper.ConstructionLogMapper;
import com.jinghe.breeze.modules.construction.service.IDataPanelService;
import com.jinghe.breeze.modules.project.entity.ProjectPbs;
import com.jinghe.breeze.modules.project.entity.ProjectProcess;
import com.jinghe.breeze.modules.project.mapper.ProjectPbsMapper;
import com.jinghe.breeze.modules.project.mapper.ProjectProcessMapper;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class DataPanelServiceImpl implements IDataPanelService {
    @Autowired
    private ProjectPbsMapper projectPbsMapper;
    @Autowired
    private ConstructionLogMapper constructionLogMapper;
    @Autowired
    private ProjectProcessMapper projectProcessMapper;

    @Override
    public Map<String, Object> condition(String projectId) {
        //整体思路 1查询出所有pbs 风机海缆 通过类型区分分组
        // 2 查询出所有类型的工序 工序按照类型在分组  查询出所有日志 根据pbsid分组然后判断pbs完成的工序长度与实际工序长度是否一致
        LambdaQueryWrapper<ProjectPbs> pbsWrapper = new LambdaQueryWrapper<>();
        pbsWrapper.eq(ProjectPbs::getDelFlag, Common.delete_flag.OK).eq(ProjectPbs::getFanSection, Common.commonality.ZERO);
        if (!StringUtils.isEmpty(projectId)) {
            pbsWrapper.eq(ProjectPbs::getProjectId, projectId);
        }
        List<ProjectPbs> projectPbs = projectPbsMapper.selectList(pbsWrapper);
        Set<String> processTypes = projectPbs.stream().map(x -> x.getApplicableProcess()).collect(Collectors.toSet());
        List<String> pbsIds = projectPbs.stream().map(x -> x.getId()).collect(Collectors.toList());
        LambdaQueryWrapper<ConstructionLog> logWrapper = new LambdaQueryWrapper<>();
        logWrapper.in(ConstructionLog::getPbsId, pbsIds).isNotNull(ConstructionLog::getEndTime);
        List<ConstructionLog> logList = constructionLogMapper.selectList(logWrapper);
        LambdaQueryWrapper<ProjectProcess> processWrapper = new LambdaQueryWrapper<>();
        processWrapper.eq(ProjectProcess::getDelFlag, Common.delete_flag.OK)
                .in(ProjectProcess::getType, processTypes);
        List<ProjectProcess> projectProcesses = projectProcessMapper.selectList(processWrapper);
        Map<String, Object> pbsMap = new HashMap<>();
//        Map<String, Object> hlMap = new HashMap<>();
        Map<String, List<ProjectPbs>> collect = projectPbs.stream().collect(Collectors.groupingBy(ProjectPbs::getType));
        Map<String, List<ProjectProcess>> processtype = projectProcesses.stream().collect(Collectors.groupingBy(ProjectProcess::getType));
        Map<String, List<ConstructionLog>> logs = logList.stream().collect(Collectors.groupingBy(ConstructionLog::getPbsId));
        for (String key : collect.keySet()) {
            if (key.equals(ProjectPbsEnum.SYZ.getCode())) {
                continue;
            }
            List<ProjectPbs> projectPbs1 = collect.get(key);
            if (key.equals(ProjectPbsEnum.FJ.getCode())) {
                pbsMap.put("fjSum", projectPbs1.size());
                int fjcomplete = 0;
                int fjnotStarted = 0;
                int fjunderway = 0;
                for (ProjectPbs pbs : projectPbs1) {
                    List<ConstructionLog> list = logs.get(pbs.getId());
                    List<ProjectProcess> projectProcesses1 = processtype.get(pbs.getApplicableProcess());
                    if (StringUtils.isEmpty(list)) { //未开始
                        fjnotStarted++;
                    } else if (list.size() < projectProcesses1.size()) {
                        fjunderway++;
                    } else {
                        fjcomplete++;
                    }
                }
                pbsMap.put("fjcomplete", fjcomplete);
                pbsMap.put("fjnotStarted", fjnotStarted);
                pbsMap.put("fjunderway", fjunderway);
            } else if (key.equals(ProjectPbsEnum.HL.getCode())) {
                pbsMap.put("hlSum", projectPbs1.size());
                int hlcomplete = 0;//已完成
                int hlnotStarted = 0;//未开始
                int hlunderway = 0; //进行中
                for (ProjectPbs pbs : projectPbs1) {
                    List<ConstructionLog> list = logs.get(pbs.getId());
                    List<ProjectProcess> projectProcesses1 = processtype.get(pbs.getApplicableProcess());
                    if (StringUtils.isEmpty(list)) { //未开始
                        hlnotStarted++;
                    } else if (list.size() < projectProcesses1.size()) {
                        hlunderway++;
                    } else {
                        hlcomplete++;
                    }
                }
                pbsMap.put("hlcomplete", hlcomplete);
                pbsMap.put("hlnotStarted", hlnotStarted);
                pbsMap.put("hlunderway", hlunderway);
            }
        }
        return pbsMap;
    }

    @Override
    public Result<?> schedule(String pbsId) {
        //先查询风机 在查询风机试用工序 ，找到同工序所有风机的日志
        ProjectPbs projectPbs = projectPbsMapper.selectById(pbsId);
        LambdaQueryWrapper<ProjectProcess> processWrapper = new LambdaQueryWrapper<>();
        processWrapper.eq(ProjectProcess::getDelFlag, Common.delete_flag.OK)
                .eq(ProjectProcess::getType, projectPbs.getApplicableProcess())
                .orderByAsc(ProjectProcess::getProcessCode);
        List<ProjectProcess> projectProcesses = projectProcessMapper.selectList(processWrapper);
        List<String> collect = projectProcesses.stream().map(x -> x.getId()).collect(Collectors.toList());
        LambdaQueryWrapper<ConstructionLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ConstructionLog::getProcessId, collect)
                .isNotNull(ConstructionLog::getUseTime).isNotNull(ConstructionLog::getEndTime);
        List<ConstructionLog> logList = constructionLogMapper.selectList(queryWrapper);
        Map<String, List<ConstructionLog>> logMap = logList.stream().collect(Collectors.groupingBy(ConstructionLog::getProcessId));
        LambdaQueryWrapper<ProjectPbs> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1.eq(ProjectPbs::getDelFlag, Common.delete_flag.OK)
                .eq(ProjectPbs::getApplicableProcess, projectPbs.getApplicableProcess());
        List<ProjectPbs> pbsList = projectPbsMapper.selectList(queryWrapper1);
//        List<ConstructionLog> constructionLogs = logList.stream().filter(x -> x.getPbsId().equals(pbsId)).collect(Collectors.toList());
        List<ProcessVo> voList = new LinkedList<>();
        double sumUseTime = 0;
        double schedule = 0;
        Map<String, Object> objectMap = new HashMap<>();
        for (ProjectProcess projectProcess : projectProcesses) {
            ProcessVo processVo = new ProcessVo();
            List<ConstructionLog> logs = logMap.get(projectProcess.getId());
            if (!StringUtils.isEmpty(logs) && logs.size() > 0) {
                Optional<ConstructionLog> max = logs.stream().max(Comparator.comparing(ConstructionLog::getUseTime));
                Optional<ConstructionLog> min = logs.stream().min(Comparator.comparing(ConstructionLog::getUseTime));
                //先找出最大最小值  然后再根据最大最小值取出风机list
                if (max.isPresent()) {
                    List<ConstructionLog> collect1 = logs.stream().filter(x -> x.getUseTime().doubleValue() == max.get().getUseTime().doubleValue()).collect(Collectors.toList());
                    String names = "";
                    for (ConstructionLog constructionLog : collect1) {
                        Optional<ProjectPbs> first = pbsList.stream().filter(x -> x.getId().equals(constructionLog.getPbsId())).findFirst();
                        if (first.isPresent()) {
                            names += first.get().getName() + ",";
                        }
                    }
                    if (!ObjectUtils.isEmpty(names)){
                        processVo.setMaxName(names.substring(0, names.length() - 1) + "(" + max.get().getUseTime() + "h)");
                    }
                }
                if (min.isPresent()) {
                    List<ConstructionLog> collect1 = logs.stream().filter(x -> x.getUseTime().doubleValue() == min.get().getUseTime().doubleValue()).collect(Collectors.toList());
                    String names = "";
                    for (ConstructionLog constructionLog : collect1) {
                        Optional<ProjectPbs> first = pbsList.stream().filter(x -> x.getId().equals(constructionLog.getPbsId())).findFirst();
                        if (first.isPresent()) {
                            names += first.get().getName() + ",";
                        }
                    }
                    if (!ObjectUtils.isEmpty(names)){
                        processVo.setMinName(names.substring(0, names.length() - 1) + "(" + min.get().getUseTime() + "h)");
                    }
                }
                Optional<ConstructionLog> first = logs.stream().filter(x -> ObjectUtil.equal(x.getPbsId(),pbsId)).findFirst();
                if (first.isPresent()) {
                    if (!StringUtils.isEmpty(projectProcess.getWeight())) {
                        schedule += projectProcess.getWeight();
                    }
                    processVo.setUseTime(first.get().getUseTime());
                    processVo.setEndTime(first.get().getEndTime());
                    processVo.setStartTime(first.get().getStartTime());
                    if (!StringUtils.isEmpty(first.get().getUseTime())) {
                        sumUseTime += first.get().getUseTime().doubleValue();
                    }
                }

            }
            if (projectPbs.getType().equals(ProjectPbsEnum.FJ.getCode())) {
                processVo.setPbsType(ProjectPbsEnum.FJ.getName());
            } else if (projectPbs.getType().equals(ProjectPbsEnum.HL.getCode())) {
                processVo.setPbsType(ProjectPbsEnum.HL.getName());
            } else {
                processVo.setPbsType("");
            }
            processVo.setName(projectProcess.getName());
            processVo.setTheoryHours(projectProcess.getTheoryHours());
            processVo.setWorkContent(projectProcess.getWorkContent());
            voList.add(processVo);
        }
        objectMap.put("processList", voList);
        objectMap.put("sumUseTime", sumUseTime);
        objectMap.put("schedule", schedule);
        return Result.OK(objectMap);
    }
}
