package com.jinghe.breeze.modules.monitor.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: monitor_person_record
 * @Author: jeecg-boot
 * @Date: 2024-08-07
 * @Version: V1.0
 */
@Data
@ApiModel(value = "设备对象")
public class MonitorRecordVo implements Serializable {
    //设备id
    private String id;

    //设备名称
    private String name;

    //安装区域
    private String installArea;

    //入场数量
    private Integer inCount;

    //出场数量
    private Integer outCount;

    //出勤数量
    private Integer count;
}
