package com.jinghe.breeze.modules.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.modules.construction.service.IConstructionLogService;
import com.jinghe.breeze.modules.project.entity.*;
import com.jinghe.breeze.modules.project.entity.vo.ConstructionPlanProcessVo;
import com.jinghe.breeze.modules.project.mapper.ConstructionPlanProcessMapper;
import com.jinghe.breeze.modules.project.service.*;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 关联工序
 * @Author: jeecg-boot
 * @Date: 2024-10-10
 * @Version: V1.0
 */
@Service
public class ConstructionPlanProcessServiceImpl extends ServiceImpl<ConstructionPlanProcessMapper, ConstructionPlanProcess> implements IConstructionPlanProcessService {
    @Autowired
    private MySysCategoryService categoryService;
    @Autowired
    private IProjectPbsService iProjectPbsService;
    @Autowired
    private IProjectProcessService iProjectProcessService;
    @Autowired
    private IProjectPlanService iProjectPlanService;
    @Autowired
    private IConstructionLogService iConstructionLogService;

    @Override
    public Result<?> leftTree() {
        List<MySysCategory> b06 = categoryService.gueryTreeList("B06", null);
        if (ObjectUtils.isEmpty(b06) && b06.get(0).getChildren().isEmpty()) {
            return Result.OK();
        }
        List<MySysCategory> children = b06.get(0).getChildren();
        for (MySysCategory child : children) {
            LambdaQueryWrapper<ProjectPbs> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProjectPbs::getType, child.getCode());
            queryWrapper.eq(ProjectPbs::getFanSection, Common.commonality.ZERO);
            queryWrapper.orderByAsc(ProjectPbs::getFanSection);
            queryWrapper.orderByAsc(ProjectPbs::getCode);
            List<ProjectPbs> list = iProjectPbsService.list(queryWrapper);
            List<MySysCategory> categoryList = new LinkedList<>();
            for (ProjectPbs projectPbs : list) {
                MySysCategory mySysCategory = new MySysCategory();
                mySysCategory.setCode(projectPbs.getCode());
                mySysCategory.setId(projectPbs.getId());
                mySysCategory.setName(projectPbs.getName());
                mySysCategory.setHasChild("0");
                mySysCategory.setPid(child.getId());
                mySysCategory.setChildren(new ArrayList<>());
                categoryList.add(mySysCategory);
            }
            child.setChildren(categoryList);
        }
        return Result.OK(children);
    }

    @Override
    public Result<?> getProcessByPlan(String id, String planCode, Integer version) {
        ProjectPbs projectPbs = iProjectPbsService.getById(id);
        LambdaQueryWrapper<ProjectProcess> processQueryWrapper = new LambdaQueryWrapper<>();
        processQueryWrapper.eq(ProjectProcess::getType, projectPbs.getApplicableProcess());
        processQueryWrapper.orderByAsc(ProjectProcess::getProcessCode);
        List<ProjectProcess> list = iProjectProcessService.list(processQueryWrapper);

        LambdaQueryWrapper<ConstructionPlanProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConstructionPlanProcess::getPbsId, id);
        List<ConstructionPlanProcess> constructionPlanProcesses = this.list(queryWrapper);
        List<String> stringList = constructionPlanProcesses.stream()
                .filter(x -> x.getPlanCode().equals(planCode))
                .map(x -> x.getProcessId())
                .collect(Collectors.toList());
        List<String> planCodes = constructionPlanProcesses.stream().map(x -> x.getPlanCode()).collect(Collectors.toList());

        if (!planCodes.isEmpty()) {
            LambdaQueryWrapper<ProjectPlan> queryWrapper1 = new LambdaQueryWrapper<>();
            queryWrapper1.in(ProjectPlan::getCode, planCodes).eq(ProjectPlan::getVersion, version);
            List<ProjectPlan> projectPlans = iProjectPlanService.list(queryWrapper1);
            Map<String, ProjectPlan> collect1 = projectPlans.stream().collect(Collectors.toMap(ProjectPlan::getCode, Function.identity()));
            list.forEach(x -> {
                constructionPlanProcesses.forEach(constructionPlanProcess -> {
                    if (constructionPlanProcess.getProcessId().equals(x.getId())) {
                        x.setPlanName(constructionPlanProcess.getPlanCode() + "-" + collect1.get(constructionPlanProcess.getPlanCode()).getName());
                        x.setPlanCode(constructionPlanProcess.getPlanCode());
                    }
                });
            });
        }
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("projectProcess", list);
        objectMap.put("checkOn", stringList);
        return Result.OK(objectMap);
    }

    @Override
    public Result<?> saveProcessPlan(ConstructionPlanProcessVo constructionPlanProcessVo) {
        if (constructionPlanProcessVo.getPbsIds().isEmpty()) {
            return Result.OK();
        }

        for (String s : constructionPlanProcessVo.getPbsIds().keySet()) {
            List<ConstructionPlanProcess> list = new ArrayList<>();
            List<String> list1 = constructionPlanProcessVo.getPbsIds().get(s);
            for (String processId : list1) {
                ConstructionPlanProcess constructionPlanProcess = new ConstructionPlanProcess();
                constructionPlanProcess.setPbsId(s);
                constructionPlanProcess.setPlanCode(constructionPlanProcessVo.getPlanCode());
                constructionPlanProcess.setProcessId(processId);
                list.add(constructionPlanProcess);
            }
            LambdaQueryWrapper<ConstructionPlanProcess> planProcessLambdaQueryWrapper = new LambdaQueryWrapper<>();
            planProcessLambdaQueryWrapper.eq(ConstructionPlanProcess::getPbsId, s)
                    .eq(ConstructionPlanProcess::getPlanCode, constructionPlanProcessVo.getPlanCode());
            this.remove(planProcessLambdaQueryWrapper);
            this.saveBatch(list);
            iConstructionLogService.planProcess(s, list1);
        }
        return Result.OK();
    }
}
