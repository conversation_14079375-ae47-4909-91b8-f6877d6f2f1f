package com.jinghe.breeze.modules.sea.controller;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.jinghe.breeze.common.constants.Common;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.jinghe.breeze.modules.sea.entity.SeaWeatherLive;
import com.jinghe.breeze.modules.sea.service.ISeaWeatherLiveService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: sea_weather_live
 * @Author: jeecg-boot
 * @Date: 2024-04-28
 * @Version: V1.0
 */
@Api(tags = "sea_weather_live")
@RestController
@RequestMapping("/sea/seaWeatherLive")
@Slf4j
public class SeaWeatherLiveController extends JeecgController<SeaWeatherLive, ISeaWeatherLiveService> {
    @Autowired
    private ISeaWeatherLiveService seaWeatherLiveService;

    /**
     * 分页列表查询
     *
     * @param seaWeatherLive
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "sea_weather_live-分页列表查询")
    @ApiOperation(value = "sea_weather_live-分页列表查询", notes = "sea_weather_live-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(SeaWeatherLive seaWeatherLive,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   @RequestParam(name = "startTime", required = false) String startTime,
                                   @RequestParam(name = "endTime", required = false) String endTime,
                                   HttpServletRequest req) {
        QueryWrapper<SeaWeatherLive> queryWrapper = QueryGenerator.initQueryWrapper(seaWeatherLive, req.getParameterMap());
//        QueryWrapper<SeaWeatherLive> queryWrapper = new QueryWrapper<>();
        SimpleDateFormat sdf = new SimpleDateFormat(Common.date.SECONDS);
        if (oConvertUtils.isNotEmpty(startTime)) {
            try {
                Date start = sdf.parse(startTime + " 00:00:00");
                Date end = sdf.parse(endTime + " 23:59:59");
                queryWrapper.between("create_time", start, end);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        queryWrapper.orderByDesc("create_time");
        Page<SeaWeatherLive> page = new Page<SeaWeatherLive>(pageNo, pageSize);
        IPage<SeaWeatherLive> pageList = seaWeatherLiveService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param seaWeatherLive
     * @return
     */
    @AutoLog(value = "sea_weather_live-添加")
    @ApiOperation(value = "sea_weather_live-添加", notes = "sea_weather_live-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody SeaWeatherLive seaWeatherLive) {
        seaWeatherLiveService.save(seaWeatherLive);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param seaWeatherLive
     * @return
     */
    @AutoLog(value = "sea_weather_live-编辑")
    @ApiOperation(value = "sea_weather_live-编辑", notes = "sea_weather_live-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody SeaWeatherLive seaWeatherLive) {
        seaWeatherLiveService.updateById(seaWeatherLive);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "sea_weather_live-通过id删除")
    @ApiOperation(value = "sea_weather_live-通过id删除", notes = "sea_weather_live-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        seaWeatherLiveService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "sea_weather_live-批量删除")
    @ApiOperation(value = "sea_weather_live-批量删除", notes = "sea_weather_live-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.seaWeatherLiveService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "sea_weather_live-通过id查询")
    @ApiOperation(value = "sea_weather_live-通过id查询", notes = "sea_weather_live-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        SeaWeatherLive seaWeatherLive = seaWeatherLiveService.getById(id);
        if (seaWeatherLive == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(seaWeatherLive);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param seaWeatherLive
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SeaWeatherLive seaWeatherLive) {
        return super.exportXls(request, seaWeatherLive, SeaWeatherLive.class, "sea_weather_live");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SeaWeatherLive.class);
    }

}
