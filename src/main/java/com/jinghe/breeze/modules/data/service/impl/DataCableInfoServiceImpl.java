package com.jinghe.breeze.modules.data.service.impl;

import com.jinghe.breeze.modules.data.entity.DataCableInfo;
import com.jinghe.breeze.modules.data.mapper.DataCableInfoMapper;
import com.jinghe.breeze.modules.data.service.IDataCableInfoService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: data_cable_info
 * @Author: jeecg-boot
 * @Date:   2024-05-22
 * @Version: V1.0
 */
@Service
public class DataCableInfoServiceImpl extends ServiceImpl<DataCableInfoMapper, DataCableInfo> implements IDataCableInfoService {

}
