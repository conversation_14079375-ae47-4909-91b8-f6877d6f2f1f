package com.jinghe.breeze.modules.quality.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.*;
/**
 * @Description: quality_inspection_records
 * @Author: jeecg-boot
 * @Date:   2025-06-20
 * @Version: V1.0
 */
@Data
@TableName("quality_inspection_records")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="quality_inspection_records对象", description="quality_inspection_records")
public class QualityInspectionRecords implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String id;
	/**创建人*/

    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改人*/

    @ApiModelProperty(value = "修改人")
    private String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
	/**delFlag*/
	@Excel(name = "delFlag", width = 15)

    @ApiModelProperty(value = "delFlag")
    private Integer delFlag;
	/**组织机构编码*/

    @ApiModelProperty(value = "组织机构编码")
    private String sysOrgCode;
	/**检查编号，自动生成*/
	@Excel(name = "检查编号，自动生成", width = 15)
    @NotNull(message = "检查编号，自动生成不能为空!")

    @ApiModelProperty(value = "检查编号，自动生成")
    private String code;
	/**检查工区，PBS树形列表*/
	@Excel(name = "检查工区，PBS树形列表", width = 15)
    @NotNull(message = "检查工区，PBS树形列表不能为空!")

    @ApiModelProperty(value = "检查工区，PBS树形列表")
    private String workArea;
	/**检查类型，引用数据字典*/
	@Excel(name = "检查类型，引用数据字典", width = 15, dicCode = "inspection_type")
	@Dict(dicCode = "inspection_type")
    @NotNull(message = "检查类型，引用数据字典不能为空!")

    @ApiModelProperty(value = "检查类型，引用数据字典")
    private String inspectionType;
	/**检查日期，格式为yyyy-mm-dd*/
	@Excel(name = "检查日期，格式为yyyy-mm-dd", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @NotNull(message = "检查日期，格式为yyyy-mm-dd不能为空!")

    @ApiModelProperty(value = "检查日期，格式为yyyy-mm-dd")
    private Date inspectionDate;
	/**检查结果，引用数据字典*/
	@Excel(name = "检查结果，引用数据字典", width = 15, dicCode = "inspection_result")
	@Dict(dicCode = "inspection_result")
    @ApiModelProperty(value = "检查结果，引用数据字典")
    private String inspectionResult;
	/**检查人员，引用基础数据*/
	@Excel(name = "检查人员，引用基础数据", width = 15)
    @NotNull(message = "检查人员，引用基础数据不能为空!")

    @ApiModelProperty(value = "检查人员，引用基础数据")
    private String inspector;
	/**检查上报人，默认为当前系统用户名称*/
	@Excel(name = "检查上报人，默认为当前系统用户名称", width = 15)
    @ApiModelProperty(value = "检查上报人，默认为当前系统用户名称")
    private String reporter;

	/**检查单位，根据用户自动带出*/
    @Dict(dictTable = "project_unit", dicText = "name", dicCode = "id")
	@Excel(name = "检查单位，根据用户自动带出", width = 15)
    @ApiModelProperty(value = "检查单位，根据用户自动带出")
    private String reportingUnit;
	/**检查内容，必填，最多1000字*/
	@Excel(name = "检查内容，必填，最多1000字", width = 15)
    @ApiModelProperty(value = "检查内容，必填，最多1000字")
    private String inspectionContent;
	/**检查依据，最多1000字*/
	@Excel(name = "检查依据，最多1000字", width = 15)

    @ApiModelProperty(value = "检查依据，最多1000字")
    private String inspectionBasis;

    @TableField(exist = false)
    private List<QualityIssueRectifications> details;

}
