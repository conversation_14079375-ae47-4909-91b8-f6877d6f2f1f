package com.jinghe.breeze.modules.device.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghe.breeze.modules.device.entity.DeviceGate;
import com.jinghe.breeze.modules.device.entity.DeviceInfo;
import com.jinghe.breeze.modules.device.vo.BaseDevicePage;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 设备信息
 * @Author: jeecg-boot
 * @Date: 2024-08-13
 * @Version: V1.0
 */
public interface IDeviceInfoService extends IService<DeviceInfo> {

    /**
     * 添加一对多
     */
    public void saveMain(DeviceInfo baseDevice, List<DeviceGate> baseDeviceGateList);

    /**
     * 修改一对多
     */
    public void updateMain(DeviceInfo baseDevice, List<DeviceGate> baseDeviceGateList);

    /**
     * 删除一对多
     */
    public void delMain(String id);

    /**
     * 批量删除一对多
     */
    public void delBatchMain(Collection<? extends Serializable> idList);

    List<BaseDevicePage> getListByIds(List<String> deviceIds);
}
