package com.jinghe.breeze.modules.project.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.query.QueryGenerator;
import com.jinghe.breeze.modules.project.entity.ProjectFence;
import com.jinghe.breeze.modules.project.service.IProjectFenceService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: project_fence
 * @Author: jeecg-boot
 * @Date: 2024-04-15
 * @Version: V1.0
 */
@Api(tags = "project_fence")
@RestController
@RequestMapping("/project/projectFence")
@Slf4j
public class ProjectFenceController extends JeecgController<ProjectFence, IProjectFenceService> {
    @Autowired
    private IProjectFenceService projectFenceService;

    /**
     * 分页列表查询
     *
     * @param projectFence
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "project_fence-分页列表查询")
    @ApiOperation(value = "project_fence-分页列表查询", notes = "project_fence-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(ProjectFence projectFence,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<ProjectFence> queryWrapper = QueryGenerator.initQueryWrapper(projectFence, req.getParameterMap());
        queryWrapper.orderByAsc("code");
        Page<ProjectFence> page = new Page<ProjectFence>(pageNo, pageSize);
        IPage<ProjectFence> pageList = projectFenceService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param projectFence
     * @return
     */
    @AutoLog(value = "project_fence-添加")
    @ApiOperation(value = "project_fence-添加", notes = "project_fence-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody ProjectFence projectFence) {
        try {
            projectFenceService.saveInfo(projectFence);
            return Result.OK("添加成功！");
        } catch (JeecgBootException e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 编辑
     *
     * @param projectFence
     * @return
     */
    @AutoLog(value = "project_fence-编辑")
    @ApiOperation(value = "project_fence-编辑", notes = "project_fence-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody ProjectFence projectFence) {
        try {
            projectFenceService.editInfo(projectFence);
            return Result.OK("编辑成功!");
        } catch (JeecgBootException e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "project_fence-通过id删除")
    @ApiOperation(value = "project_fence-通过id删除", notes = "project_fence-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        projectFenceService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "project_fence-批量删除")
    @ApiOperation(value = "project_fence-批量删除", notes = "project_fence-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.projectFenceService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "project_fence-通过id查询")
    @ApiOperation(value = "project_fence-通过id查询", notes = "project_fence-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        ProjectFence projectFence = projectFenceService.getById(id);
        if (projectFence == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(projectFence);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param projectFence
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ProjectFence projectFence) {
        return super.exportXls(request, projectFence, ProjectFence.class, "project_fence");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ProjectFence.class);
    }

}
