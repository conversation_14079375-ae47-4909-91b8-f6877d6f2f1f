package com.jinghe.breeze.modules.quality.controller;

import org.apache.shiro.authz.annotation.RequiresPermissions;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.jinghe.breeze.modules.quality.entity.QualityCatalogueTree;
import com.jinghe.breeze.modules.quality.service.IQualityCatalogueTreeService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: quality_catalogue_tree
 * @Author: jeecg-boot
 * @Date: 2025-06-20
 * @Version: V1.0
 */
@Api(tags = "quality_catalogue_tree")
@RestController
@RequestMapping("/quality/qualityCatalogueTree")
@Slf4j
public class QualityCatalogueTreeController extends JeecgController<QualityCatalogueTree, IQualityCatalogueTreeService> {
    @Autowired
    private IQualityCatalogueTreeService qualityCatalogueTreeService;

    /**
     * 分页列表查询
     *
     * @param qualityCatalogueTree
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "quality_catalogue_tree-分页列表查询")
    @ApiOperation(value = "quality_catalogue_tree-分页列表查询", notes = "quality_catalogue_tree-分页列表查询")
    @GetMapping(value = "/rootList")
    public Result<?> queryPageList(QualityCatalogueTree qualityCatalogueTree,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
		QueryWrapper<QualityCatalogueTree> queryWrapper =  QueryGenerator.initQueryWrapper(qualityCatalogueTree, req.getParameterMap());
//        QueryWrapper<QualityCatalogueTree> queryWrapper = new QueryWrapper<>(qualityCatalogueTree);
//        queryWrapper.eq("pid", "0");
        List<QualityCatalogueTree> list = qualityCatalogueTreeService.queryTreeListNoPage(queryWrapper);
//        IPage<QualityCatalogueTree> pageList = new Page<>(1, 10, list.size());
//        pageList.setRecords(list);
        return Result.OK(list);
    }

    /**
     * 获取子数据
     *
     * @param qualityCatalogueTree
     * @param req
     * @return
     */
    @AutoLog(value = "quality_catalogue_tree-获取子数据")
    @ApiOperation(value = "quality_catalogue_tree-获取子数据", notes = "quality_catalogue_tree-获取子数据")
    @GetMapping(value = "/childList")
    public Result<?> queryPageList(QualityCatalogueTree qualityCatalogueTree, HttpServletRequest req) {
        QueryWrapper<QualityCatalogueTree> queryWrapper = QueryGenerator.initQueryWrapper(qualityCatalogueTree, req.getParameterMap());
        List<QualityCatalogueTree> list = qualityCatalogueTreeService.list(queryWrapper);
        IPage<QualityCatalogueTree> pageList = new Page<>(1, 10, list.size());
        pageList.setRecords(list);
        return Result.OK(pageList);
    }

    /**
     * 批量查询子节点
     *
     * @param parentIds 父ID（多个采用半角逗号分割）
     * @param parentIds
     * @return 返回 IPage
     * @return
     */
    @AutoLog(value = "quality_catalogue_tree-批量获取子数据")
    @ApiOperation(value = "quality_catalogue_tree-批量获取子数据", notes = "quality_catalogue_tree-批量获取子数据")
    @GetMapping("/getChildListBatch")
    public Result getChildListBatch(@RequestParam("parentIds") String parentIds) {
        try {
            QueryWrapper<QualityCatalogueTree> queryWrapper = new QueryWrapper<>();
            List<String> parentIdList = Arrays.asList(parentIds.split(","));
            queryWrapper.in("pid", parentIdList);
            List<QualityCatalogueTree> list = qualityCatalogueTreeService.list(queryWrapper);
            IPage<QualityCatalogueTree> pageList = new Page<>(1, 10, list.size());
            pageList.setRecords(list);
            return Result.OK(pageList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("批量查询子节点失败：" + e.getMessage());
        }
    }

    /**
     * 添加
     *
     * @param qualityCatalogueTree
     * @return
     */
    @AutoLog(value = "quality_catalogue_tree-添加")
    @ApiOperation(value = "quality_catalogue_tree-添加", notes = "quality_catalogue_tree-添加")
//    @RequiresPermissions("qualityCatalogueTree:add")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody QualityCatalogueTree qualityCatalogueTree) {
        qualityCatalogueTreeService.addQualityCatalogueTree(qualityCatalogueTree);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param qualityCatalogueTree
     * @return
     */
    @AutoLog(value = "quality_catalogue_tree-编辑")
    @ApiOperation(value = "quality_catalogue_tree-编辑", notes = "quality_catalogue_tree-编辑")
//    @RequiresPermissions("qualityCatalogueTree:edit")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody QualityCatalogueTree qualityCatalogueTree) {
        qualityCatalogueTreeService.updateQualityCatalogueTree(qualityCatalogueTree);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "quality_catalogue_tree-通过id删除")
    @ApiOperation(value = "quality_catalogue_tree-通过id删除", notes = "quality_catalogue_tree-通过id删除")
//    @RequiresPermissions("qualityCatalogueTree:delete")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        qualityCatalogueTreeService.deleteQualityCatalogueTree(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "quality_catalogue_tree-批量删除")
    @ApiOperation(value = "quality_catalogue_tree-批量删除", notes = "quality_catalogue_tree-批量删除")
//    @RequiresPermissions("qualityCatalogueTree:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.qualityCatalogueTreeService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "quality_catalogue_tree-通过id查询")
    @ApiOperation(value = "quality_catalogue_tree-通过id查询", notes = "quality_catalogue_tree-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        QualityCatalogueTree qualityCatalogueTree = qualityCatalogueTreeService.getById(id);
        if (qualityCatalogueTree == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(qualityCatalogueTree);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param qualityCatalogueTree
     */
    @RequestMapping(value = "/exportXls")
    @RequiresPermissions("qualityCatalogueTree:export")
    public ModelAndView exportXls(HttpServletRequest request, QualityCatalogueTree qualityCatalogueTree) {
        return super.exportXls(request, qualityCatalogueTree, QualityCatalogueTree.class, "quality_catalogue_tree");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
//    @RequiresPermissions("qualityCatalogueTree:import")
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, QualityCatalogueTree.class);
    }

}
