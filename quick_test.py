#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 发送一次测试数据
"""

import requests
import json
import time
import random

def send_test_data():
    """发送一次测试数据"""
    
    # API地址 - 请根据实际情况修改
    api_url = "http://localhost:8080/jeecg-boot/deviceHelmet/location/receive"
    
    # 安全围栏内的坐标
    safety_fence_locations = [
        [116.400428, 39.90423],  # 安全围栏内中心偏左
        [116.404428, 39.90523],  # 安全围栏内中心偏右
    ]

    # 施工围栏内的坐标
    construction_fence_locations = [
        [116.410000, 39.90500],  # 施工围栏内中心偏左
        [116.415000, 39.90700],  # 施工围栏内中心偏右
    ]

    # 围栏外的坐标
    outside_locations = [
        [116.395000, 39.90423],  # 围栏外左侧
        [116.420000, 39.90523],  # 围栏外右侧
    ]
    
    current_time = int(time.time() * 1000)
    
    # 生成测试数据 - 2个在围栏内，2个在围栏外
    test_data = []
    
    # 围栏内的设备
    for i, (lng, lat) in enumerate(inside_locations):
        test_data.append({
            "puname": f"HELMET_IN_{i+1:03d}",
            "gpstime": current_time,
            "alarmtype": "0",
            "longitude": lng,
            "latitude": lat,
            "speed": round(random.uniform(0, 3), 2),
            "power": str(random.randint(50, 100)),
            "datatype": "GPS",
            "reserved": ""
        })
    
    # 围栏外的设备
    for i, (lng, lat) in enumerate(outside_locations):
        test_data.append({
            "puname": f"HELMET_OUT_{i+1:03d}",
            "gpstime": current_time,
            "alarmtype": "0",
            "longitude": lng,
            "latitude": lat,
            "speed": round(random.uniform(0, 3), 2),
            "power": str(random.randint(50, 100)),
            "datatype": "GPS",
            "reserved": ""
        })
    
    print("发送的测试数据:")
    print(json.dumps(test_data, indent=2, ensure_ascii=False))
    print("\n" + "="*50)
    
    # 发送数据
    try:
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
        response = requests.post(api_url, json=test_data, headers=headers, timeout=10)
        
        print(f"HTTP状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success', False):
                print("✅ 测试数据发送成功!")
            else:
                print(f"❌ 服务器返回错误: {result.get('message', '')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 发送失败: {e}")

if __name__ == "__main__":
    send_test_data()
