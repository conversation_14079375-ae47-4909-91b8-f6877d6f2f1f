package com.jinghe.breeze.modules.wind.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 避风单位
 * @Author: jeecg-boot
 * @Date:   2024-05-24
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="避风单位", description="避风单位")
public class WindReportDepartmentVo implements Serializable {
    private static final long serialVersionUID = 1L;

	/**单位名称*/
    @ApiModelProperty(value = "单位名称")
    private String name;

	/**是否报备*/
    @ApiModelProperty(value = "是否报备")
    private int state;

	/**报备船只*/
    @ApiModelProperty(value = "报备船只")
    private List<WindReportShipVo> ships;


}
