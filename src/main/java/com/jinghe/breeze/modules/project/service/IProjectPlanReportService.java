package com.jinghe.breeze.modules.project.service;

import com.jinghe.breeze.modules.project.entity.ProjectPlan;
import com.jinghe.breeze.modules.project.entity.ProjectPlanReport;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Description: project_plan_report
 * @Author: jeecg-boot
 * @Date:   2024-05-10
 * @Version: V1.0
 */
public interface IProjectPlanReportService extends IService<ProjectPlanReport> {

    Result<?> analysis();

    Result<?> getAll(Integer version, String name, Integer status);

    Result<?> schedule();

    Result<?> update(ProjectPlanReport projectPlanReport);

    Result<?> saveByCode(ProjectPlanReport projectPlanReport);

    @Transactional
    void updateByBase(ProjectPlan projectPlan);
}
