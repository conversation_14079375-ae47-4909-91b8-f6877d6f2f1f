package com.jinghe.breeze.modules.education.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.jinghe.breeze.modules.education.entity.EduExam;
import com.jinghe.breeze.modules.education.service.IEduExamService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: 学员考试实例表
 * @Author: jeecg-boot
 * @Date: 2025-06-26
 * @Version: V1.0
 */
@Api(tags = "学员考试实例表")
@RestController
@RequestMapping("/education/eduExam")
@Slf4j
public class EduExamController extends JeecgController<EduExam, IEduExamService> {
    @Autowired
    private IEduExamService eduExamService;

    /**
     * 分页列表查询
     *
     * @param eduExam
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "学员考试实例表-分页列表查询")
    @ApiOperation(value = "学员考试实例表-分页列表查询", notes = "学员考试实例表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(EduExam eduExam,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<EduExam> queryWrapper = QueryGenerator.initQueryWrapper(eduExam, req.getParameterMap());
        Page<EduExam> page = new Page<>(pageNo, pageSize);
        IPage<EduExam> pageList = eduExamService.page(page, queryWrapper);
        eduExamService.fillInfo(pageList.getRecords(), false);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param eduExam
     * @return
     */
    @AutoLog(value = "学员考试实例表-添加")
    @ApiOperation(value = "学员考试实例表-添加", notes = "学员考试实例表-添加")
    @RequiresPermissions("eduExam:add")
    @PostMapping(value = "/add")
    public Result<?> add(@Validated @RequestBody EduExam eduExam) {
        eduExamService.save(eduExam);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param eduExam
     * @return
     */
    @AutoLog(value = "学员考试实例表-编辑")
    @ApiOperation(value = "学员考试实例表-编辑", notes = "学员考试实例表-编辑")
    @RequiresPermissions("eduExam:edit")
    @PutMapping(value = "/edit")
    public Result<?> edit(@Validated @RequestBody EduExam eduExam) {
        eduExamService.updateById(eduExam);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "学员考试实例表-通过id删除")
    @ApiOperation(value = "学员考试实例表-通过id删除", notes = "学员考试实例表-通过id删除")
    @RequiresPermissions("eduExam:delete")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        eduExamService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "学员考试实例表-批量删除")
    @ApiOperation(value = "学员考试实例表-批量删除", notes = "学员考试实例表-批量删除")
    @RequiresPermissions("eduExam:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.eduExamService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "学员考试实例表-通过id查询")
    @ApiOperation(value = "学员考试实例表-通过id查询", notes = "学员考试实例表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        EduExam eduExam = eduExamService.getById(id);
        if (eduExam == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(eduExam);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param eduExam
     */
    @RequiresPermissions("eduExam:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, EduExam eduExam) {
        return super.exportXls(request, eduExam, EduExam.class, "学员考试实例表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("eduExam:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, EduExam.class);
    }

    @RequestMapping(value = "/startExam", method = RequestMethod.POST)
    public Result<?> startExam(@RequestBody EduExam eduExam) {
        try {
            eduExamService.startExam(eduExam);
            return Result.OK("开始答题");
        } catch (IllegalArgumentException e) {
            return Result.error(e.getMessage());
        }

    }

    @RequestMapping(value = "/submit", method = RequestMethod.POST)
    public Result<?> submit(@RequestBody EduExam eduExam) {
        try {
            eduExamService.submitExam(eduExam);
            return Result.OK("交卷成功!");
        } catch (IllegalArgumentException e) {
            return Result.error(e.getMessage());
        }
    }

    @RequestMapping(value = "getExamDetail", method = RequestMethod.POST)
    public Result<?> getExamDetail(@RequestBody EduExam eduExam) {
        try {
            EduExam entity = eduExamService.getExamDetail(eduExam);
            return Result.OK(entity);
        } catch (IllegalArgumentException e) {
            return Result.error(e.getMessage());
        }
    }
}
