package com.jinghe.breeze.modules.education.controller;

import org.apache.shiro.authz.annotation.RequiresPermissions;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.jinghe.breeze.modules.education.entity.EduQuestion;
import com.jinghe.breeze.modules.education.service.IEduQuestionService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: 题库
 * @Author: jeecg-boot
 * @Date: 2025-06-24
 * @Version: V1.0
 */
@Api(tags = "题库")
@RestController
@RequestMapping("/education/eduQuestion")
@Slf4j
public class EduQuestionController extends JeecgController<EduQuestion, IEduQuestionService> {
    @Autowired
    private IEduQuestionService eduQuestionService;

    /**
     * 分页列表查询
     *
     * @param eduQuestion
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "题库-分页列表查询")
    @ApiOperation(value = "题库-分页列表查询", notes = "题库-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(EduQuestion eduQuestion,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<EduQuestion> queryWrapper = QueryGenerator.initQueryWrapper(eduQuestion, req.getParameterMap());
        Page<EduQuestion> page = new Page<EduQuestion>(pageNo, pageSize);
        IPage<EduQuestion> pageList = eduQuestionService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param eduQuestion
     * @return
     */
    @AutoLog(value = "题库-添加")
    @ApiOperation(value = "题库-添加", notes = "题库-添加")
    @RequiresPermissions("eduQuestion:add")
    @PostMapping(value = "/add")
    public Result<?> add(@Validated @RequestBody EduQuestion eduQuestion) {
        boolean success = eduQuestionService.saveQuestionWithTags(eduQuestion);
        if (success) {
            return Result.OK("添加成功！");
        } else {
            return Result.error("添加失败！");
        }
    }

    /**
     * 编辑
     *
     * @param eduQuestion
     * @return
     */
    @AutoLog(value = "题库-编辑")
    @ApiOperation(value = "题库-编辑", notes = "题库-编辑")
    @RequiresPermissions("eduQuestion:edit")
    @PutMapping(value = "/edit")
    public Result<?> edit(@Validated @RequestBody EduQuestion eduQuestion) {
        boolean success = eduQuestionService.updateQuestionWithTags(eduQuestion);
        if (success) {
            return Result.OK("编辑成功!");
        } else {
            return Result.error("编辑失败!");
        }
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "题库-通过id删除")
    @ApiOperation(value = "题库-通过id删除", notes = "题库-通过id删除")
    @RequiresPermissions("eduQuestion:delete")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        boolean success = eduQuestionService.removeQuestionWithTags(id);
        if (success) {
            return Result.OK("删除成功!");
        } else {
            return Result.error("删除失败!");
        }
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "题库-批量删除")
    @ApiOperation(value = "题库-批量删除", notes = "题库-批量删除")
    @RequiresPermissions("eduQuestion:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        List<String> idList = Arrays.asList(ids.split(","));
        boolean success = eduQuestionService.removeQuestionsWithTags(idList);
        if (success) {
            return Result.OK("批量删除成功!");
        } else {
            return Result.error("批量删除失败!");
        }
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "题库-通过id查询")
    @ApiOperation(value = "题库-通过id查询", notes = "题库-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        EduQuestion eduQuestion = eduQuestionService.getById(id);
        if (eduQuestion == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(eduQuestion);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param eduQuestion
     */
    @RequiresPermissions("eduQuestion:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, EduQuestion eduQuestion) {
        return super.exportXls(request, eduQuestion, EduQuestion.class, "题库");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("eduQuestion:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, EduQuestion.class);
    }

}
