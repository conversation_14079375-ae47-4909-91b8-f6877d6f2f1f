package com.jinghe.breeze.modules.device.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghe.breeze.modules.device.entity.DeviceStar;
import com.jinghe.breeze.modules.device.mapper.DeviceStarMapper;
import com.jinghe.breeze.modules.device.service.IDeviceStarService;
import org.springframework.stereotype.Service;

/**
 * @Description: 关注的监控信息
 * @Author: jeecg-boot
 * @Date: 2024-08-14
 * @Version: V1.0
 */
@Service
public class DeviceStarServiceImpl extends ServiceImpl<DeviceStarMapper, DeviceStar> implements IDeviceStarService {

}
