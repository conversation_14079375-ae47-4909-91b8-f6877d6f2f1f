package com.jinghe.breeze.modules.education.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.*;
/**
 * @Description: 题库
 * @Author: jeecg-boot
 * @Date:   2025-06-24
 * @Version: V1.0
 */
@Data
@TableName("edu_question")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="edu_question对象", description="题库")
public class EduQuestion implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;

	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;

	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;

	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;

	/**删除标识*/
	@Excel(name = "删除标识", width = 15)
    @ApiModelProperty(value = "删除标识")
    private String delFlag;

	/**出题人*/
	@Excel(name = "出题人", width = 15, dictTable = "person_info", dicText = "name", dicCode = "id")
	@Dict(dictTable = "person_info", dicText = "name", dicCode = "id")
    @NotNull(message = "出题人不能为空!")
    @ApiModelProperty(value = "出题人")
    private String author;

	/**题目类型*/
	@Excel(name = "题目类型", width = 15, dicCode = "question_type")
	@Dict(dicCode = "question_type")
    @NotNull(message = "题目类型不能为空!")
    @ApiModelProperty(value = "题目类型")
    private String type;

	/**答案*/
	@Excel(name = "答案", width = 15)
    @NotNull(message = "答案不能为空!")
    @ApiModelProperty(value = "答案")
    private String answer;

	/**题目标签*/
	@Excel(name = "题目标签", width = 15, dicCode = "question_tag")
	@Dict(dicCode = "question_tag")
    @NotNull(message = "题目标签不能为空!")
    @ApiModelProperty(value = "题目标签")
    private String tags;

	/**题目内容*/
	@Excel(name = "题目内容", width = 15)
    @NotNull(message = "题目内容不能为空!")
    @ApiModelProperty(value = "题目内容")
    private String content;

	/**题目选项*/
	@Excel(name = "题目选项", width = 15)
    @NotNull(message = "题目选项不能为空!")
    @ApiModelProperty(value = "题目选项")
    private String options;

	/**答案详解*/
	@Excel(name = "答案详解", width = 15)
    @NotNull(message = "答案详解不能为空!")
    @ApiModelProperty(value = "答案详解")
    private String explanation;


    @ApiModelProperty(value="文件")
    private String file;

    @ApiModelProperty(value="答题次数")
    private Integer answerCount;



    @ApiModelProperty(value="错误次数")
    private Integer errorCount;

}
