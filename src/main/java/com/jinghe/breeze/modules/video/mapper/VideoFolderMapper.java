package com.jinghe.breeze.modules.video.mapper;

import org.apache.ibatis.annotations.Param;
import com.jinghe.breeze.modules.video.entity.VideoFolder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: video_folder
 * @Author: jeecg-boot
 * @Date:   2024-06-12
 * @Version: V1.0
 */
public interface VideoFolderMapper extends BaseMapper<VideoFolder> {

	/**
	 * 编辑节点状态
	 * @param id
	 * @param status
	 */
	void updateTreeNodeStatus(@Param("id") String id,@Param("status") String status);

}
