package com.jinghe.breeze.modules.monitor.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinghe.breeze.modules.monitor.entity.PersonMonitorRecord;
import com.jinghe.breeze.modules.monitor.vo.DataObjectVo;
import com.jinghe.breeze.modules.monitor.vo.MonitorRecordPageVo;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

/**
 * @Description: monitor_person_record
 * @Author: jeecg-boot
 * @Date: 2024-08-07
 * @Version: V1.0
 */
public interface PersonMonitorRecordMapper extends BaseMapper<PersonMonitorRecord> {
    List<DataObjectVo> statisticsByHalfMonth();

    List<DataObjectVo> statisticsByWeek(@Param("gateId") String gateId);

    IPage<MonitorRecordPageVo> getAttendanceRecords(Page<PersonMonitorRecord> page,@Param("gateId") String gateId);

}
