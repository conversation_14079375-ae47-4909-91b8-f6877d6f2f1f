package com.jinghe.breeze.modules.sailingcheck.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.common.constants.enums.PepleLoseReason;
import com.jinghe.breeze.modules.person.entity.PersonInfo;
import com.jinghe.breeze.modules.person.service.IPersonInfoService;
import com.jinghe.breeze.modules.sailingcheck.entity.ShipSailingRecord;
import com.jinghe.breeze.modules.sailingcheck.entity.UserBoardRecord;
import com.jinghe.breeze.modules.sailingcheck.entity.UserSailingRecord;
import com.jinghe.breeze.modules.sailingcheck.entity.vo.SailingUser;
import com.jinghe.breeze.modules.sailingcheck.entity.vo.SailingVo;
import com.jinghe.breeze.modules.sailingcheck.mapper.ShipSailingRecordMapper;
import com.jinghe.breeze.modules.sailingcheck.service.IShipSailingRecordService;
import com.jinghe.breeze.modules.sailingcheck.service.IUserBoardRecordService;
import com.jinghe.breeze.modules.sailingcheck.service.IUserSailingRecordService;
import com.jinghe.breeze.modules.ship.entity.ShipOutgoing;
import com.jinghe.breeze.modules.ship.service.IShipOutgoingService;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: ship_sailing_record
 * @Author: jeecg-boot
 * @Date: 2024-06-18
 * @Version: V1.0
 */
@Service
public class ShipSailingRecordServiceImpl extends ServiceImpl<ShipSailingRecordMapper, ShipSailingRecord> implements IShipSailingRecordService {

    @Resource
    private IUserSailingRecordService userSailingRecordService;

    @Resource
    private IUserBoardRecordService userBoardRecordService;

    @Resource
    private IPersonInfoService personService;

    @Resource
    private IShipOutgoingService shipOutService;

    @Override
    public Result<?> leave(SailingVo vo) {
        LambdaQueryWrapper<ShipSailingRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShipSailingRecord::getShipId, vo.getShipId());
        queryWrapper.eq(ShipSailingRecord::getDelFlag, Common.delete_flag.OK);
        queryWrapper.orderByDesc(ShipSailingRecord::getCreateTime);
        queryWrapper.last("limit 1");
        ShipSailingRecord record = this.getOne(queryWrapper);
        if (record != null && Objects.equals(record.getStatus(), Common.commonality.ONE)) {
            return Result.error("船只已出航，操作失败");
        } else {
            ShipSailingRecord shipSailingRecord = new ShipSailingRecord();
            BeanUtils.copyProperties(vo, shipSailingRecord);
            shipSailingRecord.setStatus(Common.commonality.ONE);
            this.save(shipSailingRecord);
            for (SailingUser user : vo.getUsers()) {
                UserSailingRecord userSailingRecord = new UserSailingRecord();
                BeanUtils.copyProperties(user, userSailingRecord);
                userSailingRecord.setRealName(user.getName());
                userSailingRecord.setBaseId(shipSailingRecord.getId());
                userSailingRecord.setStatus(Common.commonality.ONE);
                userSailingRecordService.save(userSailingRecord);
                UserBoardRecord userBoardRecord = new UserBoardRecord();
                BeanUtils.copyProperties(user, userBoardRecord);
                userBoardRecord.setRealName(user.getName());
                userBoardRecord.setBaseId(shipSailingRecord.getId());
                userBoardRecord.setShipId(shipSailingRecord.getShipId());
                userBoardRecord.setShipName(vo.getShipName());
                userBoardRecord.setMmsi(vo.getMmsi());
                userBoardRecord.setType(Common.commonality.ONE);
                userBoardRecordService.save(userBoardRecord);
            }
            return Result.OK();
        }
    }

    @Override
    public Result<?> back(SailingVo vo) {
//        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        LambdaQueryWrapper<ShipSailingRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShipSailingRecord::getShipId, vo.getShipId());
        queryWrapper.eq(ShipSailingRecord::getDelFlag, Common.delete_flag.OK);
        queryWrapper.orderByDesc(ShipSailingRecord::getCreateTime);
        queryWrapper.last("limit 1");
        ShipSailingRecord record = this.getOne(queryWrapper);
        if (record == null || !Objects.equals(record.getStatus(), Common.commonality.ONE)) {
            return Result.error("未找到对应船只，操作失败");
        } else {
            record.setStatus(Common.commonality.TWO);
            record.setBackTime(new Date());
            this.updateById(record);
            for (SailingUser user : vo.getUsers()) {
                LambdaQueryWrapper<UserSailingRecord> userWrapper = new LambdaQueryWrapper<>();
                userWrapper.eq(UserSailingRecord::getUserId, user.getUserId());
                userWrapper.eq(UserSailingRecord::getStatus, Common.commonality.ONE);
                userWrapper.eq(UserSailingRecord::getDelFlag, Common.delete_flag.OK);
                userWrapper.orderByDesc(UserSailingRecord::getCreateTime);
                userWrapper.last("limit 1");
                UserSailingRecord one = userSailingRecordService.getOne(userWrapper);
                if (one != null && one.getId() != null) {
                    one.setDisposeUserName(vo.getInspectorName());
                    if (StringUtils.isEmpty(user.getResult())) {
                        one.setStatus(Common.commonality.TWO);
                        one.setBackTime(new Date());
                        one.setBackBaseId(record.getId());
                        userSailingRecordService.updateById(one);
                        UserBoardRecord userBoardRecord = new UserBoardRecord();
                        BeanUtils.copyProperties(user, userBoardRecord);
                        userBoardRecord.setRealName(user.getName());
                        userBoardRecord.setBaseId(record.getId());
                        userBoardRecord.setShipId(record.getShipId());
                        userBoardRecord.setShipName(record.getShipName());
                        userBoardRecord.setMmsi(vo.getMmsi());
                        userBoardRecord.setType(Common.commonality.TWO);
                        userBoardRecordService.save(userBoardRecord);
                    } else {
                        one.setResult(user.getResult());
                        one.setRemark(user.getRemark());
                        // 1:已转乘其他船舶
                        // 2:已下船，未打卡
                        // 3:人员失踪
                        // 4:其他
                        //已下船，未打卡;人员失踪;其他;这三种情况的标注，需要将人员改为非出海状态
                        if ( PepleLoseReason.remark.Otherwise.equals(user.getResult())) {
                            one.setStatus(3);
                        }
                        if (PepleLoseReason.remark.ForgetCheck.equals(user.getResult())){
                            one.setStatus(Common.commonality.TWO);
                        }
                        userSailingRecordService.updateById(one);
                    }
                } else {
                    one = new UserSailingRecord();
                    BeanUtils.copyProperties(user, one);
                    one.setDisposeUserName(vo.getInspectorName());
                    if (StringUtils.isEmpty(user.getResult())) {
                        one.setStatus(Common.commonality.TWO);
                        one.setBackTime(new Date());
                        one.setBackBaseId(record.getId());
                        userSailingRecordService.save(one);
                        UserBoardRecord userBoardRecord = new UserBoardRecord();
                        BeanUtils.copyProperties(user, userBoardRecord);
                        userBoardRecord.setRealName(user.getName());
                        userBoardRecord.setBaseId(record.getId());
                        userBoardRecord.setShipId(record.getShipId());
                        userBoardRecord.setShipName(record.getShipName());
                        userBoardRecord.setMmsi(vo.getMmsi());
                        userBoardRecord.setType(Common.commonality.TWO);
                        userBoardRecordService.save(userBoardRecord);
                    } else {
                        one.setResult(user.getResult());
                        one.setRemark(user.getRemark());
                        userSailingRecordService.save(one);
                    }
                }
            }
            return Result.OK();
        }
    }

    @Override
    public List<ShipOutgoing> shipList(int option, String userId) {
        LambdaQueryWrapper<PersonInfo> personWrapper = new LambdaQueryWrapper<>();
        personWrapper.eq(PersonInfo::getUserId, userId);
//        personWrapper.eq(PersonInfo::getDelFlag, Common.delete_flag.OK);
        personWrapper.orderByDesc(PersonInfo::getCreateBy);
        personWrapper.last("limit 1");
        PersonInfo one = personService.getOne(personWrapper);
        if (one == null) {
            return null;
        }
        LambdaQueryWrapper<ShipOutgoing> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShipOutgoing::getUnit, one.getDepartment());
        queryWrapper.eq(ShipOutgoing::getDelFlag, Common.delete_flag.OK);
        queryWrapper.orderByDesc(ShipOutgoing::getCreateTime);
        List<ShipOutgoing> recordList = shipOutService.list(queryWrapper);//进出场记录
        List<ShipOutgoing> shipList = new ArrayList<>();//在场船舶
        Map<String, Integer> processed = new HashMap<>();//已处理集合
        for (ShipOutgoing record : recordList) {
            if (processed.get(record.getShipDataId()) == null) {
                if (record.getIsOut().equals("0")) {//最后的记录为进场
                    shipList.add(record);
                }
                processed.put(record.getShipDataId(), 1);
            } else {//如果已处理过该船舶的进出场记录，则跳过
                continue;
            }
        }
        if (shipList.isEmpty() || option == 9) {
            return shipList;
        }
        LambdaQueryWrapper<ShipSailingRecord> sailingWrapper = new LambdaQueryWrapper<>();
        sailingWrapper.eq(ShipSailingRecord::getStatus, Common.commonality.ONE);
        sailingWrapper.eq(ShipSailingRecord::getDelFlag, Common.delete_flag.OK);
        sailingWrapper.orderByDesc(ShipSailingRecord::getCreateBy);
        List<ShipSailingRecord> busyList = this.list(sailingWrapper);
        Map<String, String> collect = busyList.stream().collect(Collectors.toMap(ShipSailingRecord::getShipId, ShipSailingRecord::getId));

        List<ShipOutgoing> resultList = new ArrayList<>();
        for (ShipOutgoing record : shipList) {
            if (Common.commonality.ONE == option && collect.get(record.getShipDataId()) == null) {
                resultList.add(record);
            } else if (Common.commonality.TWO == option && collect.get(record.getShipDataId()) != null) {
                resultList.add(record);
            }
        }
        return resultList;
    }

    @Override
    public SailingVo shipUsers(String shipId) {
        LambdaQueryWrapper<ShipSailingRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShipSailingRecord::getShipId, shipId);
        queryWrapper.eq(ShipSailingRecord::getDelFlag, Common.delete_flag.OK);
        queryWrapper.orderByDesc(ShipSailingRecord::getCreateTime);
        queryWrapper.last("limit 1");
        ShipSailingRecord record = this.getOne(queryWrapper);
        if (record == null) {
            return null;
        } else {
            LambdaQueryWrapper<UserSailingRecord> userWrapper = new LambdaQueryWrapper<>();
            userWrapper.eq(UserSailingRecord::getBaseId, record.getId());
            userWrapper.eq(UserSailingRecord::getDelFlag, Common.delete_flag.OK);
            List<UserSailingRecord> list = userSailingRecordService.list(userWrapper);
            SailingVo vo = new SailingVo();
            vo.setInspector(record.getInspector());
            vo.setShipId(record.getShipId());
            vo.setShipName(record.getShipName());
            vo.setDepartmentId(record.getDepartmentId());
            vo.setDepartmentName(record.getDepartmentName());
            List<SailingUser> users = new ArrayList<>();
            for (UserSailingRecord item : list) {
                SailingUser user = new SailingUser();
                BeanUtils.copyProperties(item, user);
                user.setName(item.getRealName());
                user.setStatus(String.valueOf(item.getStatus()));
                if (item.getStatus() == Common.commonality.TWO) {
                    ShipSailingRecord backSailing = this.getById(item.getBackBaseId());
                    LambdaQueryWrapper<ShipOutgoing> shipWrapper = new LambdaQueryWrapper<>();
                    shipWrapper.eq(ShipOutgoing::getShipDataId, backSailing.getShipId());
                    shipWrapper.eq(ShipOutgoing::getDelFlag, Common.delete_flag.OK);
                    shipWrapper.orderByDesc(ShipOutgoing::getCreateTime);
                    shipWrapper.last("limit 1");
                    ShipOutgoing one = shipOutService.getOne(shipWrapper);
                    if (one != null) {
                        user.setBackShipName(one.getName());
                        user.setBackShipMMSI(one.getMmsi());
                    }
                }
                users.add(user);
            }
            vo.setUsers(users);
            return vo;
        }
    }


}
