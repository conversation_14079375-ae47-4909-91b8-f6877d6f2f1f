package com.jinghe.breeze.modules.person.service.impl;

import com.jinghe.breeze.common.enums.DelFlagEnum;
import com.jinghe.breeze.common.enums.SafeEducationStatusEnum;
import com.jinghe.breeze.modules.person.entity.DeviceHelmet;
import com.jinghe.breeze.modules.person.entity.PersonInfo;
import com.jinghe.breeze.modules.person.mapper.DeviceHelmetMapper;
import com.jinghe.breeze.modules.person.service.IDeviceHelmetService;
import com.jinghe.breeze.modules.person.service.IPersonInfoService;
import com.jinghe.breeze.modules.person.dto.LocationDataDTO;
import com.jinghe.breeze.modules.person.dto.EnrichedLocationDataDTO;
import com.jinghe.breeze.modules.project.entity.ProjectFence;
import com.jinghe.breeze.modules.project.service.IProjectFenceService;
import com.jinghe.breeze.modules.safety.entity.SafeViolationRecords;
import com.jinghe.breeze.modules.safety.service.ISafeViolationRecordsService;
import com.jinghe.breeze.common.utils.MapPoint;
import com.jinghe.breeze.common.utils.MapWktUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;
import java.util.ArrayList;
import java.util.Set;
import java.util.Map;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Description: 设备信息表
 * @Author: chenliang
 * @Date: 2022-08-09
 * @Version: V1.0
 */
@Slf4j
@Service
public class DeviceHelmetServiceImpl extends ServiceImpl<DeviceHelmetMapper, DeviceHelmet> implements IDeviceHelmetService {
    @Autowired
    private IProjectFenceService projectFenceService;
    @Autowired
    private IPersonInfoService personInfoService;
    @Autowired
    private ISafeViolationRecordsService safeViolationRecordsService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    private static final String REDIS_KEY_PREFIX = "deviceHelmet:location:";
    private static final long REDIS_EXPIRE_TIME = 600; // 10分钟

    @Override
    public void deviceBootable() {

    }

    @Override
    public void receiveLocationData(List<LocationDataDTO> locationDataList) {
        log.info("接收到位置数据，数量: {}", locationDataList.size());
        for (LocationDataDTO locationData : locationDataList) {
            try {
                String redisKey = REDIS_KEY_PREFIX + locationData.getPuname();
                // 存储到Redis并设置过期时间
                redisUtil.set(redisKey, locationData, REDIS_EXPIRE_TIME);
                log.debug("位置数据已存储到Redis，设备编号: {}, Key: {}", locationData.getPuname(), redisKey);
            } catch (Exception e) {
                log.error("存储位置数据到Redis失败，设备编号: {}, 错误: {}", locationData.getPuname(), e.getMessage(), e);
            }
        }
    }

    @Override
    public List<EnrichedLocationDataDTO> getAllPersonnelLocations() {
        List<EnrichedLocationDataDTO> result = new ArrayList<>();

        try {
            // 查找所有匹配的Redis键
            Set<String> keys = redisTemplate.keys(REDIS_KEY_PREFIX + "*");
            if (!keys.isEmpty()) {
                for (String key : keys) {
                    try {
                        Object value = redisUtil.get(key);
                        if (value instanceof LocationDataDTO) {
                            LocationDataDTO locationData = (LocationDataDTO) value;
                            EnrichedLocationDataDTO enrichedData = new EnrichedLocationDataDTO();
                            BeanUtils.copyProperties(locationData, enrichedData);
                            result.add(enrichedData);
                        }
                    } catch (Exception e) {
                        log.error("处理Redis数据失败，Key: {}, 错误: {}", key, e.getMessage(), e);
                    }
                }
            }
            log.info("查询到人员位置数据，数量: {}", result.size());
        } catch (Exception e) {
            log.error("查询人员位置数据失败: {}", e.getMessage(), e);
        }
        enrichLocationDataDTO(result);
        return result;
    }



    /**
     *
     */
    private void enrichLocationDataDTO(List<EnrichedLocationDataDTO> lst) {
        // 获取启用的电子围栏数据
        List<ProjectFence> fenceList = projectFenceService.list(new LambdaQueryWrapper<ProjectFence>()
                .eq(ProjectFence::getDelFlag, DelFlagEnum.NORMAL.getType())
                .last("limit 1"));

        String posText = null;
        if (!fenceList.isEmpty()) {
            ProjectFence fence = fenceList.get(0);
            posText = fence.getFenceRadius();
            log.info("获取到电子围栏数据: {}", posText);
        } else {
            log.warn("未找到电子围栏数据，将无法进行围栏检测");
        }


        if (lst.isEmpty()) {
            return;
        }
        List<String> deviceCodeList = lst.stream().map(EnrichedLocationDataDTO::getPuname).collect(Collectors.toList());
        if (deviceCodeList.isEmpty()) {
            return;
        }
        //通过设备编号获取所有绑定的人员信息,
        //通过人员信息获得所有最新的违规记录
        //补全lst中每条记录的personId,latestViolation,passCheck 没有的为null
        List<PersonInfo> personInfoList = personInfoService.list(new LambdaQueryWrapper<PersonInfo>()
                .in(PersonInfo::getDeviceCode, deviceCodeList)
                .eq(PersonInfo::getDelFlag, DelFlagEnum.NORMAL.getType()));
        List<String> personIdList = personInfoList.stream().map(PersonInfo::getId).collect(Collectors.toList());
        List<SafeViolationRecords> safeViolationRecordsList = new ArrayList<>();
        if (!personIdList.isEmpty()) {
            safeViolationRecordsList = safeViolationRecordsService.list(new LambdaQueryWrapper<SafeViolationRecords>()
                    .in(SafeViolationRecords::getPerson, personIdList)
                    .eq(SafeViolationRecords::getDelFlag, DelFlagEnum.NORMAL.getType())
                    .orderByDesc(SafeViolationRecords::getViolationTime)
            );
        }

        // 创建设备编号到人员信息的映射
        Map<String, PersonInfo> deviceCodeToPersonMap = personInfoList.stream()
                .collect(Collectors.toMap(PersonInfo::getDeviceCode, person -> person,
                        (existing, replacement) -> existing));
        // 创建人员ID到最新违规记录的映射
        Map<String, SafeViolationRecords> personToLatestViolationMap = new HashMap<>();
        for (SafeViolationRecords violation : safeViolationRecordsList) {
            String personId = violation.getPerson();
            if (!personToLatestViolationMap.containsKey(personId)) {
                personToLatestViolationMap.put(personId, violation);
            }
        }

        // 补全每条记录的信息
        for (EnrichedLocationDataDTO enrichedData : lst) {
            String deviceCode = enrichedData.getPuname();
            PersonInfo personInfo = deviceCodeToPersonMap.get(deviceCode);

            // 检测是否在电子围栏内
            boolean isInFence = false;
            if (posText != null && enrichedData.getLongitude() != null && enrichedData.getLatitude() != null) {
                try {
                    MapPoint currentPoint = new MapPoint(enrichedData.getLongitude(), enrichedData.getLatitude());
                    isInFence = MapWktUtil.isPointInPolygon(currentPoint, posText);
                    log.debug("设备 {} 位置检测结果: 经度={}, 纬度={}, 在围栏内={}",
                            deviceCode, enrichedData.getLongitude(), enrichedData.getLatitude(), isInFence);
                } catch (Exception e) {
                    log.error("检测围栏位置失败，设备编号: {}, 错误: {}", deviceCode, e.getMessage(), e);
                    isInFence = false;
                }
            } else {
                log.debug("设备 {} 无法进行围栏检测: posText={}, longitude={}, latitude={}",
                        deviceCode, posText != null ? "有围栏数据" : "无围栏数据",
                        enrichedData.getLongitude(), enrichedData.getLatitude());
            }
            enrichedData.setIsInFence(isInFence);

            if (personInfo != null) {
                // 设置人员信息
                enrichedData.setPersonId(personInfo.getId());
                // 设置是否登记（有人员信息即为已登记）
                enrichedData.setIsRegistered(true);
                // 设置最新违规记录
                SafeViolationRecords latestViolation = personToLatestViolationMap.get(personInfo.getId());
                enrichedData.setLatestViolation(latestViolation);
                String isAssessed = personInfo.getSafeEducation();
                if (isAssessed == null) {
                    enrichedData.setIsAssessed(null);
                } else {
                    enrichedData.setIsAssessed(SafeEducationStatusEnum.OK.type.equals(isAssessed));
                }
            } else {
                // 没有绑定人员信息
                enrichedData.setPersonId(null);
                enrichedData.setIsRegistered(false);
                enrichedData.setLatestViolation(null);
                enrichedData.setIsAssessed(null);
            }
        }
    }
}
