package com.jinghe.breeze.modules.education.controller;

import org.apache.shiro.authz.annotation.RequiresPermissions;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.jinghe.breeze.modules.education.entity.EduExamPaper;
import com.jinghe.breeze.modules.education.service.IEduExamPaperService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: 试卷管理
 * @Author: jeecg-boot
 * @Date: 2025-06-25
 * @Version: V1.0
 */
@Api(tags = "试卷管理")
@RestController
@RequestMapping("/education/eduExamPaper")
@Slf4j
public class EduExamPaperController extends JeecgController<EduExamPaper, IEduExamPaperService> {
    @Autowired
    private IEduExamPaperService eduExamPaperService;

    /**
     * 分页列表查询
     *
     * @param eduExamPaper
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "试卷管理-分页列表查询")
    @ApiOperation(value = "试卷管理-分页列表查询", notes = "试卷管理-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(EduExamPaper eduExamPaper,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<EduExamPaper> queryWrapper = QueryGenerator.initQueryWrapper(eduExamPaper, req.getParameterMap());
        Page<EduExamPaper> page = new Page<>(pageNo, pageSize);
        IPage<EduExamPaper> pageList = eduExamPaperService.page(page, queryWrapper);
        eduExamPaperService.fillPaperDetails(pageList.getRecords(), true);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param eduExamPaper
     * @return
     */
    @AutoLog(value = "试卷管理-添加")
    @ApiOperation(value = "试卷管理-添加", notes = "试卷管理-添加")
    @RequiresPermissions("eduExamPaper:add")
    @PostMapping(value = "/add")
    public Result<?> add(@Validated @RequestBody EduExamPaper eduExamPaper) {
        try {
            eduExamPaperService.create(eduExamPaper);
            return Result.OK("添加成功！");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 编辑
     *
     * @param eduExamPaper
     * @return
     */
    @AutoLog(value = "试卷管理-编辑")
    @ApiOperation(value = "试卷管理-编辑", notes = "试卷管理-编辑")
    @RequiresPermissions("eduExamPaper:edit")
    @PutMapping(value = "/edit")
    public Result<?> edit(@Validated @RequestBody EduExamPaper eduExamPaper) {
        try {
            return eduExamPaperService.updatePaper(eduExamPaper);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "试卷管理-通过id删除")
    @ApiOperation(value = "试卷管理-通过id删除", notes = "试卷管理-通过id删除")
    @RequiresPermissions("eduExamPaper:delete")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        try {
            return eduExamPaperService.deletePaper(id);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "试卷管理-批量删除")
    @ApiOperation(value = "试卷管理-批量删除", notes = "试卷管理-批量删除")
    @RequiresPermissions("eduExamPaper:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        try {
            List<String> idList = Arrays.asList(ids.split(","));
            return eduExamPaperService.deletePapers(idList);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "试卷管理-通过id查询")
    @ApiOperation(value = "试卷管理-通过id查询", notes = "试卷管理-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        EduExamPaper eduExamPaper = eduExamPaperService.getById(id);
        if (eduExamPaper == null) {
            return Result.error("未找到对应数据");
        }
        // 填充试卷详情数据
        eduExamPaperService.fillPaperDetails(Arrays.asList(eduExamPaper), true);
        return Result.OK(eduExamPaper);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param eduExamPaper
     */
    @RequiresPermissions("eduExamPaper:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, EduExamPaper eduExamPaper) {
        return super.exportXls(request, eduExamPaper, EduExamPaper.class, "试卷管理");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("eduExamPaper:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, EduExamPaper.class);
    }

}
