package com.jinghe.breeze.modules.data.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * @Description: data_cable_info
 * @Author: jeecg-boot
 * @Date: 2024-05-22
 * @Version: V1.0
 */
@Data
@TableName("data_cable_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "data_cable_info对象", description = "data_cable_info")
public class DataCableInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
//    @NotNull(message = "主键不能为空!")

    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    /**
     * 创建人
     */

    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")

    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
    /**
     * 更新人
     */

    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")

    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
    /**
     * 所属部门
     */

    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
    /**
     * 删除标识
     */
    @Excel(name = "删除标识", width = 15)
//    @NotNull(message = "删除标识不能为空!")

    @ApiModelProperty(value = "删除标识")
    private java.lang.Integer delFlag;
    /**
     * 海缆规格名
     */
    @Excel(name = "海缆规格名", width = 15)

    @ApiModelProperty(value = "海缆规格名")
    private java.lang.String cableType;
    /**
     * 备注
     */
    @Excel(name = "备注", width = 15)

    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
    /**
     * 颜色
     */
    @Excel(name = "颜色", width = 15)

    @ApiModelProperty(value = "颜色")
    private java.lang.String color;
}
