package com.jinghe.breeze.modules.ship.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: ship_alarm_list
 * @Author: jeecg-boot
 * @Date:   2024-04-23
 * @Version: V1.0
 */
@Data
@TableName("ship_alarm_detail")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="ship_alarm_detail对象", description="ship_alarm_detail")
public class ShipAlarmDetail implements Serializable {
    private static final long serialVersionUID = 1L;

	/**标识*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "标识")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd   HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
	/**删除标识*/
	@Excel(name = "删除标识", width = 15)
    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;
	/**组织机构编码*/
    @ApiModelProperty(value = "组织机构编码")
    private String sysOrgCode;
	/**项目id*/
	@Excel(name = "项目id", width = 15)
    @ApiModelProperty(value = "项目id")
    private String projectId;
	/**mmsi
*/
	@Excel(name = "mmsi", width = 15)
    @ApiModelProperty(value = "mmsi")
    private java.lang.String mmsi;
	/**船名*/
	@Excel(name = "船名", width = 15)
    @ApiModelProperty(value = "船名")
    private java.lang.String shipName;
	/**预警日期（记录到天）*/
	@Excel(name = "预警日期（记录到天）", width = 15)
    @ApiModelProperty(value = "预警日期（记录到天）")
    private String alarmDay;
	/**预警时间*/
	@Excel(name = "预警时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "预警时间")
    private Date alarmDate;
	/**纬度*/
	@Excel(name = "纬度", width = 15)
    @ApiModelProperty(value = "纬度")
    private BigDecimal lat;
	/**经度*/
	@Excel(name = "经度", width = 15)
    @ApiModelProperty(value = "经度")
    private BigDecimal lon;
	/**呼号*/
	@Excel(name = "呼号", width = 15)
    @ApiModelProperty(value = "呼号")
    private String callSign;
	/**船速*/
	@Excel(name = "船速", width = 15)
    @ApiModelProperty(value = "船速")
    private Integer sog;
	/**船迹象*/
	@Excel(name = "船迹象", width = 15)
    @ApiModelProperty(value = "船迹象")
    private Integer cog;
	/**船首向*/
	@Excel(name = "船首向", width = 15)
    @ApiModelProperty(value = "船首向")
    private Integer hdg;
	/**船舶类型*/
	@Excel(name = "船舶类型", width = 15)
    @ApiModelProperty(value = "船舶类型")
    private String shipType;
	/**imo*/
	@Excel(name = "imo", width = 15)
    @ApiModelProperty(value = "imo")
    private String imo;
	/**航行状态*/
	@Excel(name = "航行状态", width = 15)
    @ApiModelProperty(value = "航行状态")
    private String navistat;
	/**船向率*/
	@Excel(name = "船向率", width = 15)
    @ApiModelProperty(value = "船向率")
    private Integer rot;
	/**数据更新速率*/
	@Excel(name = "数据更新速率", width = 15)
    @ApiModelProperty(value = "数据更新速率")
    private Integer lastTime;

    /**船舶类型名称*/
    @Excel(name = "船舶类型名称", width = 15)
    @ApiModelProperty(value = "船舶类型名称")
    private String shipTypeName;

    /**是否进入围栏*/
    @Excel(name = "是否进入围栏", width = 15)
    @ApiModelProperty(value = "是否进入围栏")
    private Integer isIn;
}
