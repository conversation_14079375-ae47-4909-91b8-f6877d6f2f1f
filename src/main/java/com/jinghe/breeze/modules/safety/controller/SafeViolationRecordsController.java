package com.jinghe.breeze.modules.safety.controller;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.jinghe.breeze.modules.safety.entity.SafeViolationRecords;
import com.jinghe.breeze.modules.safety.service.ISafeViolationRecordsService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: safe_violation_records
 * @Author: jeecg-boot
 * @Date:   2025-06-22
 * @Version: V1.0
 */
@Api(tags="safe_violation_records")
@RestController
@RequestMapping("/safety/safeViolationRecords")
@Slf4j
public class SafeViolationRecordsController extends JeecgController<SafeViolationRecords, ISafeViolationRecordsService> {
	@Autowired
	private ISafeViolationRecordsService safeViolationRecordsService;
	
	/**
	 * 分页列表查询
	 *
	 * @param safeViolationRecords
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "safe_violation_records-分页列表查询")
	@ApiOperation(value="safe_violation_records-分页列表查询", notes="safe_violation_records-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(SafeViolationRecords safeViolationRecords,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<SafeViolationRecords> queryWrapper = QueryGenerator.initQueryWrapper(safeViolationRecords, req.getParameterMap());
		Page<SafeViolationRecords> page = new Page<SafeViolationRecords>(pageNo, pageSize);
		IPage<SafeViolationRecords> pageList = safeViolationRecordsService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param safeViolationRecords
	 * @return
	 */
	@AutoLog(value = "safe_violation_records-添加")
	@ApiOperation(value="safe_violation_records-添加", notes="safe_violation_records-添加")
//	@RequiresPermissions("safeViolationRecords:add")
	@PostMapping(value = "/add")
	public Result<?> add(@Validated @RequestBody SafeViolationRecords safeViolationRecords) {
		safeViolationRecordsService.save(safeViolationRecords);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param safeViolationRecords
	 * @return
	 */
	@AutoLog(value = "safe_violation_records-编辑")
	@ApiOperation(value="safe_violation_records-编辑", notes="safe_violation_records-编辑")
//	@RequiresPermissions("safeViolationRecords:edit")
	@PutMapping(value = "/edit")
	public Result<?> edit(@Validated @RequestBody SafeViolationRecords safeViolationRecords) {
		safeViolationRecordsService.updateById(safeViolationRecords);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "safe_violation_records-通过id删除")
	@ApiOperation(value="safe_violation_records-通过id删除", notes="safe_violation_records-通过id删除")
//	@RequiresPermissions("safeViolationRecords:delete")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		safeViolationRecordsService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "safe_violation_records-批量删除")
	@ApiOperation(value="safe_violation_records-批量删除", notes="safe_violation_records-批量删除")
//	@RequiresPermissions("safeViolationRecords:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.safeViolationRecordsService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "safe_violation_records-通过id查询")
	@ApiOperation(value="safe_violation_records-通过id查询", notes="safe_violation_records-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		SafeViolationRecords safeViolationRecords = safeViolationRecordsService.getById(id);
		if(safeViolationRecords==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(safeViolationRecords);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param safeViolationRecords
    */
    @RequiresPermissions("safeViolationRecords:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SafeViolationRecords safeViolationRecords) {
        return super.exportXls(request, safeViolationRecords, SafeViolationRecords.class, "safe_violation_records");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("safeViolationRecords:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SafeViolationRecords.class);
    }

}
