package com.jinghe.breeze.modules.safety.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.*;
/**
 * @Description: ai设备维护记录
 * @Author: jeecg-boot
 * @Date:   2025-06-22
 * @Version: V1.0
 */
@Data
@TableName("safe_maintenance_records")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="safe_maintenance_records对象", description="ai设备维护记录")
public class SafeMaintenanceRecords implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
//    @NotNull(message = "主键不能为空!")

    @ApiModelProperty(value = "主键")
    private String id;
	/**创建人*/

    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/

    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**所属部门*/

    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
	/**设备编码*/
	@Excel(name = "设备编码", width = 15)
    @NotNull(message = "设备编码不能为空!")

    @ApiModelProperty(value = "设备编码")
    private String code;
	/**设备名称*/
	@Excel(name = "设备名称", width = 15)
    @NotNull(message = "设备名称不能为空!")

    @ApiModelProperty(value = "设备名称")
    private String name;
	/**维护时间*/
	@Excel(name = "维护时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @NotNull(message = "维护时间不能为空!")

    @ApiModelProperty(value = "维护时间")
    private Date maintenanceTime;
	/**设备类型字典*/
	@Excel(name = "设备类型字典", width = 15, dicCode = "ai_device_type")
	@Dict(dicCode = "ai_device_type")
    @NotNull(message = "设备类型字典不能为空!")

    @ApiModelProperty(value = "设备类型字典")
    private String type;
}
