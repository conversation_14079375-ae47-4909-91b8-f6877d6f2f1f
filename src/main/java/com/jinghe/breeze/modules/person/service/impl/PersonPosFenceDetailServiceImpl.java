package com.jinghe.breeze.modules.person.service.impl;

import com.jinghe.breeze.modules.person.entity.PersonPosFenceDetail;
import com.jinghe.breeze.modules.person.mapper.PersonPosFenceDetailMapper;
import com.jinghe.breeze.modules.person.service.IPersonPosFenceDetailService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 人员定位电子围栏经纬度子表
 * @Author: chenliang
 * @Date:   2022-08-09
 * @Version: V1.0
 */
@Service
public class PersonPosFenceDetailServiceImpl extends ServiceImpl<PersonPosFenceDetailMapper, PersonPosFenceDetail> implements IPersonPosFenceDetailService {
	
	@Autowired
	private PersonPosFenceDetailMapper personPosFenceDetailMapper;
	
	@Override
	public List<PersonPosFenceDetail> selectByMainId(String mainId) {
		return personPosFenceDetailMapper.selectByMainId(mainId);
	}
}
