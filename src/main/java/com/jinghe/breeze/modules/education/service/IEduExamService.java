package com.jinghe.breeze.modules.education.service;

import com.jinghe.breeze.modules.education.entity.EduExam;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 学员考试实例表
 * @Author: jeecg-boot
 * @Date:   2025-06-26
 * @Version: V1.0
 */
public interface IEduExamService extends IService<EduExam> {

    boolean createExam(EduExam eduExam);

    boolean startExam(EduExam eduExam);

    void submitExam(EduExam eduExam);

    void fillInfo(List<EduExam> records, boolean includeAnswerAndAnalysis);

    EduExam getExamDetail(EduExam eduExam);
}
