package com.jinghe.breeze.modules.base.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;

/**
 * @Description: base_gate_equipment
 * @Author: jeecg-boot
 * @Date: 2024-08-06
 * @Version: V1.0
 */
@ApiModel(value = "base_gate对象", description = "base_gate")
@Data
@TableName("base_gate_equipment")
public class BaseGateEquipment implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 关联 Gate 表的 id
     */
    @ApiModelProperty(value = "关联 Gate 表的 id")
    private String gateId;
    /**
     * 设备序列号
     */
    @Excel(name = "设备序列号", width = 15)
    @ApiModelProperty(value = "设备序列号")
    private String equipmentSerial;
    /**
     * 设备类型
     */
    @Excel(name = "设备类型", width = 15)
    @ApiModelProperty(value = "设备类型")
    private String equipmentType;
}
