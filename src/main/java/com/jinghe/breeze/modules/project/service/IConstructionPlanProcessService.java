package com.jinghe.breeze.modules.project.service;

import com.jinghe.breeze.modules.project.entity.ConstructionPlanProcess;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghe.breeze.modules.project.entity.vo.ConstructionPlanProcessVo;
import org.jeecg.common.api.vo.Result;

/**
 * @Description: 关联工序
 * @Author: jeecg-boot
 * @Date:   2024-10-10
 * @Version: V1.0
 */
public interface IConstructionPlanProcessService extends IService<ConstructionPlanProcess> {

    Result<?> leftTree();

    Result<?> getProcessByPlan(String id,String planCode,Integer version);

    Result<?> saveProcessPlan(ConstructionPlanProcessVo constructionPlanProcessVo);
}
