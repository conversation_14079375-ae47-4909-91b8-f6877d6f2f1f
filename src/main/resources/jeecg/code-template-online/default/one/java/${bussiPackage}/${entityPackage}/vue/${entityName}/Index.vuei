<template>
<#assign list_need_category=false>
<#assign isTreeRelate = 0>
<#assign treeRelateDictField = "">
<#assign treeDictCode = "">
<#list originalColumns as po>
    <#if (po.fieldHref) != ''>
        <#assign fieldHrefJson = po.fieldHref?eval>
        <#assign isTreeRelate = fieldHrefJson.isTreeRelate>
        <#if isTreeRelate == 1>
            <#assign treeRelateDictField = po.fieldName>
            <#assign treeDictCode = po.dictField>
            <#break>
        </#if>
    </#if>
</#list>
  <div class="list-page ${Format.humpToShortbar(entityName)}-list">
    <table-layout
      :rightTitle="rightTitle"
      :search-props="searchProps"
      :table-props="tableProps"
      <#if isTreeRelate == 1>
      :tree-props="treeProps"
      @tree-init="searchQuery"
      @tree-select="searchQuery"
      </#if>
      @init-params="initParams"
      @search-submit="searchQuery"
      @table-change="onTableChange"
    >
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk"></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'

export default {
  name: '${entityName}List',
  mixins: [JeecgTreeListMixin],
  components: {
    Modal
  },
  data() {
    return {
      <#if isTreeRelate == 1>
      disableMixinCreated: true, // 初始化是否执行混入请求
      </#if>
      rightTitle: '${tableVo.ftlDescription}列表',
      <#if isTreeRelate == 1>
      // 树组件的props
      treeProps: {
        dictCode: '${treeDictCode}',
        isSelectParentNodes: false,
      },
      </#if>
      // 搜索组件的props
      searchProps: {
        formModel: {
        <#list columns as po>
          <#if po.isQuery=='Y'>
          '${po.fieldName}': null,
          </#if>
        </#list>
        },
        formItems: [
    <#list columns as po>
        <#assign query_field_dictCode="">
        <#if po.dictTable?default("")?trim?length gt 1>
        <#assign query_field_dictCode="${po.dictTable},${po.dictText},${po.dictField}">
        <#elseif po.dictField?default("")?trim?length gt 1>
        <#assign query_field_dictCode="${po.dictField}">
        </#if>
        <#if po.isQuery=='Y'>
          { key: '${po.fieldName}', label: '${po.filedComment}', type:'${po.classType}',<#if query_field_dictCode?default("") != ''> dictCode:'${query_field_dictCode?default("")}',</#if> },
        </#if>
    </#list>
        ]
      },
      // 表格组件的props
      tableProps: {
        loading: false,
        dataSource: [],
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            }
          },
<#list columns as po>
<#if po.classType=='cat_tree' && po.dictText?default("")?trim?length == 0>
    <#assign list_need_category=true>
</#if>
<#if po.isShowList =='Y' && po.fieldName !='id'>
        {
            title:'${po.filedComment}',
            align:"center",
    <#if po.sort=='Y'>
            sorter: true,
            </#if>
    <#if po.classType=='date'>
            dataIndex: '${po.fieldName}',
            customRender: (text) => {
            return !text?"":(text.length>10?text.substr(0,10):text)
        }
<#elseif po.fieldDbType=='Blob'>
            dataIndex: '${po.fieldName}String'
    <#elseif po.classType=='umeditor'>
            dataIndex: '${po.fieldName}',
            scopedSlots: {customRender: 'htmlSlot'}
    <#elseif po.classType=='pca'>
            dataIndex: '${po.fieldName}',
            scopedSlots: {customRender: 'pcaSlot'}
    <#elseif po.classType=='file'>
            dataIndex: '${po.fieldName}',
            scopedSlots: {customRender: 'fileSlot'}
    <#elseif po.classType=='image'>
            dataIndex: '${po.fieldName}',
            scopedSlots: {customRender: 'imgSlot'}
    <#elseif po.classType=='switch'>
            dataIndex: '${po.fieldName}',
            customRender: (text) => (text ? filterMultiDictText(this.dictOptions['${po.fieldName}'], text) : ''),
    <#elseif po.classType=='list' || po.classType=='list_multi' || po.classType=='sel_search' || po.classType=='radio' || po.classType=='checkbox' || po.classType=='sel_depart' || po.classType=='sel_user'>
            dataIndex: '${po.fieldName}_dictText'
    <#elseif po.classType=='cat_tree'>
    <#if list_need_category>
            dataIndex: '${po.fieldName}',
            customRender: (text) => (text ? filterMultiDictText(this.dictOptions['${po.fieldName}'], text) : '')
    <#else>
            dataIndex: '${po.fieldName}',
            customRender: (text, record) => (text ? record['${po.dictText}'] : '')
            </#if>
    <#else>
            dataIndex: '${po.fieldName}'
        </#if>
        },
        </#if>
        </#list>
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 160,
            scopedSlots: { customRender: 'action' }
          }
        ],
        <#if isTreeRelate == 1>
        treeField: '${treeRelateDictField}',
        </#if>
        actionButtons: [
            {
                text: '查看',
                handler: this.handleDetail,
            },
            {
                text: '编辑',
                handler: this.handleEdit,
            },
            {
                text: '删除',
                type: 'danger',
                handler: this.handleDelete,
            },
        ],
        headerButtons: [
            {
                text: '新增',
                icon: 'plus-circle',
                handler: this.handleAdd,
            },
        ]
      },

      url: {
        list: "/${entityPackage}/${entityName?uncap_first}/list",
        delete: "/${entityPackage}/${entityName?uncap_first}/delete",
        deleteBatch: "/${entityPackage}/${entityName?uncap_first}/deleteBatch",
        exportXlsUrl: "/${entityPackage}/${entityName?uncap_first}/exportXls",
        importExcelUrl: "${entityPackage}/${entityName?uncap_first}/importExcel",
      }
    }
  },
  created() {},
  computed: {},
  watch: {},
  methods: {
    initParams(mergeParams) {
      this.queryParam = mergeParams.formModel
      <#if isTreeRelate == 1>
      this.queryParam[this.tableProps.treeField] = mergeParams.params[this.tableProps.treeField]
      </#if>
    },
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less'
</style>