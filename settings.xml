<?xml version="1.0" encoding="UTF-8"?>
<!-- | This is the configuration file for Maven. It can be specified at two 
	levels: | | 1. User Level. This settings.xml file provides configuration 
	for a single user, | and is normally provided in ${user.home}/.m2/settings.xml. 
	| | NOTE: This location can be overridden with the CLI option: | | -s /path/to/user/settings.xml 
	| | 2. Global Level. This settings.xml file provides configuration for all 
	Maven | users on a machine (assuming they're all using the same Maven | installation). 
	It's normally provided in | ${maven.home}/conf/settings.xml. | | NOTE: This 
	location can be overridden with the CLI option: | | -gs /path/to/global/settings.xml 
	| | The sections in this sample file are intended to give you a running start 
	at | getting the most out of your Maven installation. Where appropriate, 
	the default | values (values used when the setting is not specified) are 
	provided. | | -->
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">


</settings>