package com.jinghe.breeze.modules.safety.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.*;

/**
 * @Description: 智能安全识别预警
 * @Author: jeecg-boot
 * @Date: 2025-06-22
 * @Version: V1.0
 */
@Data
@TableName("safe_security_alert")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "safe_security_alert对象", description = "智能安全识别预警")
public class SafeSecurityAlert implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
//    @NotNull(message = "主键不能为空!")

    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    /**
     * 创建人
     */

    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
    /**
     * 更新人
     */

    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
    /**
     * 所属部门
     */

    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
    /**
     * 视频/照片
     */
    @Excel(name = "视频/照片	", width = 15)

    @ApiModelProperty(value = "视频/照片	")
    private java.lang.String media;
    /**
     * 事件类型
     */
    @Excel(name = "事件类型	", width = 15, dicCode = "event_type")
    @Dict(dicCode = "event_type")

    @ApiModelProperty(value = "事件类型	")
    private java.lang.String eventType;
    /**
     * 设备类型
     */
    @Excel(name = "设备类型	", width = 15, dicCode = "device_type")
    @Dict(dicCode = "device_type")

    @ApiModelProperty(value = "设备类型	")
    private java.lang.String deviceType;
    /**
     * 危险等级
     */
    @Excel(name = "危险等级	", width = 15, dicCode = "risk_level")
    @Dict(dicCode = "risk_level")

    @ApiModelProperty(value = "危险等级	")
    private java.lang.String riskLevel;
    /**
     * 设备信息
     */
    @Excel(name = "设备信息	", width = 15, dicCode = "device_info")
    @Dict(dicCode = "device_info")

    @ApiModelProperty(value = "设备信息	")
    private java.lang.String deviceInfo;
    /**
     * 发生时间
     */
    @Excel(name = "发生时间	", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")

    @ApiModelProperty(value = "发生时间	")
    private java.util.Date eventTime;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;

    @TableField(exist = false)
    private String personName;
}
