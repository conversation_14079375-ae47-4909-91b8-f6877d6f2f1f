package com.jinghe.breeze.modules.settlement.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.modules.settlement.entity.Settlement;
import com.jinghe.breeze.modules.settlement.service.ISettlementService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.jinghe.breeze.modules.settlement.entity.ContractInfoDetail;
import com.jinghe.breeze.modules.settlement.service.IContractInfoDetailService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: contract_info_detail
 * @Author: jeecg-boot
 * @Date: 2024-05-31
 * @Version: V1.0
 */
@Api(tags = "contract_info_detail")
@RestController
@RequestMapping("/settlement/contractInfoDetail")
@Slf4j
public class ContractInfoDetailController extends JeecgController<ContractInfoDetail, IContractInfoDetailService> {
    @Autowired
    private IContractInfoDetailService contractInfoDetailService;

    @Autowired
    private ISettlementService settlementService;

    /**
     * 分页列表查询
     *
     * @param contractInfoDetail
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "contract_info_detail-分页列表查询")
    @ApiOperation(value = "contract_info_detail-分页列表查询", notes = "contract_info_detail-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(ContractInfoDetail contractInfoDetail,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<ContractInfoDetail> queryWrapper = QueryGenerator.initQueryWrapper(contractInfoDetail, req.getParameterMap());
        String code = contractInfoDetail.getCode();
        if (oConvertUtils.isNotEmpty(code)) {
            queryWrapper.likeRight("code", code);
        }
        Page<ContractInfoDetail> page = new Page<ContractInfoDetail>(pageNo, pageSize);
        IPage<ContractInfoDetail> pageList = contractInfoDetailService.page(page, queryWrapper);
        if (oConvertUtils.isNotEmpty(code)) {
            List<ContractInfoDetail> details = pageList.getRecords();

            pageList.setRecords(details);
        }
        return Result.OK(pageList);

    }


    /**
     * 添加
     *
     * @param contractInfoDetail
     * @return
     */
    @AutoLog(value = "contract_info_detail-添加")
    @ApiOperation(value = "contract_info_detail-添加", notes = "contract_info_detail-添加")
    @RequiresPermissions("contractInfoDetail:add")
    @PostMapping(value = "/add")
    public Result<?> add(@Validated @RequestBody ContractInfoDetail contractInfoDetail) {
        contractInfoDetailService.save(contractInfoDetail);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param contractInfoDetail
     * @return
     */
    @AutoLog(value = "contract_info_detail-编辑")
    @ApiOperation(value = "contract_info_detail-编辑", notes = "contract_info_detail-编辑")
    @RequiresPermissions("contractInfoDetail:edit")
    @PutMapping(value = "/edit")
    public Result<?> edit(@Validated @RequestBody ContractInfoDetail contractInfoDetail) {
        contractInfoDetailService.updateById(contractInfoDetail);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "contract_info_detail-通过id删除")
    @ApiOperation(value = "contract_info_detail-通过id删除", notes = "contract_info_detail-通过id删除")
    @RequiresPermissions("contractInfoDetail:delete")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        int count = contractInfoDetailService.count(new LambdaQueryWrapper<ContractInfoDetail>()
                .eq(ContractInfoDetail::getPid, id));
        if (count > 0) {
            return Result.error("该清单存在子项，请先删除子项!");
        }
        count = settlementService.count(new LambdaQueryWrapper<Settlement>().eq(Settlement::getContractInfoDetailId, id));
        if (count > 0) {
            return Result.error("该清单项存在结算记录,不允许删除!");
        }
        contractInfoDetailService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "contract_info_detail-批量删除")
    @ApiOperation(value = "contract_info_detail-批量删除", notes = "contract_info_detail-批量删除")
    @RequiresPermissions("contractInfoDetail:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.contractInfoDetailService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "contract_info_detail-通过id查询")
    @ApiOperation(value = "contract_info_detail-通过id查询", notes = "contract_info_detail-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        ContractInfoDetail contractInfoDetail = contractInfoDetailService.getById(id);
        if (contractInfoDetail == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(contractInfoDetail);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param contractInfoDetail
     */
    @RequiresPermissions("contractInfoDetail:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ContractInfoDetail contractInfoDetail) {
        return super.exportXls(request, contractInfoDetail, ContractInfoDetail.class, "contract_info_detail");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("contractInfoDetail:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ContractInfoDetail.class);
    }


    @GetMapping(value = "/getSettedDetailChildList")
    public Result<?> getSettedDetailChildList(@RequestParam(name = "id", required = true) String id,
                                              @RequestParam(name = "isRoot", required = true) Boolean isRoot) {
        List<ContractInfoDetail> details = contractInfoDetailService.getSettedDetailChildList(id, isRoot);
        return Result.OK(details);
    }


}
