package com.jinghe.breeze.modules.project.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinghe.breeze.modules.project.entity.ProjectWbs;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.jinghe.breeze.modules.project.service.IProjectWbsService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: project_wbs
 * @Author: jeecg-boot
 * @Date: 2024-04-11
 * @Version: V1.0
 */
@Api(tags = "project_wbs")
@RestController
@RequestMapping("/project/projectWbs")
@Slf4j
public class ProjectWbsController extends JeecgController<ProjectWbs, IProjectWbsService> {
    @Autowired
    private IProjectWbsService projectWbsService;

    /**
     * 分页列表查询
     *
     * @param projectWbs
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "project_wbs-分页列表查询")
    @ApiOperation(value = "project_wbs-分页列表查询", notes = "project_wbs-分页列表查询")
    @GetMapping(value = "/rootList")
    public Result<?> queryPageList(ProjectWbs projectWbs,
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            HttpServletRequest req) {
        QueryWrapper<ProjectWbs> queryWrapper = QueryGenerator.initQueryWrapper(projectWbs, req.getParameterMap());
        List<ProjectWbs> projecWbsList = projectWbsService.queryTreeListNoPage(queryWrapper);
        projecWbsList.sort(Comparator.comparing(ProjectWbs::getSortNo));
        projectWbsService.setCanMoveUpDown(projecWbsList);
        IPage<ProjectWbs> pageList = new Page<>(1, projecWbsList.size(), projecWbsList.size());
        pageList.setRecords(projecWbsList);
        return Result.OK(pageList);

    }

    /**
     * 获取子数据
     *
     * @param projectWbs
     * @param req
     * @return
     */
    @AutoLog(value = "project_wbs-获取子数据")
    @ApiOperation(value = "project_wbs-获取子数据", notes = "project_wbs-获取子数据")
    @GetMapping(value = "/childList")
    public Result<?> queryPageList(ProjectWbs projectWbs, HttpServletRequest req) {
        // 预期 包含pid参数
        QueryWrapper<ProjectWbs> queryWrapper = QueryGenerator.initQueryWrapper(projectWbs, req.getParameterMap());
        queryWrapper.orderByAsc("sort_no");
        List<ProjectWbs> list = projectWbsService.list(queryWrapper);
        projectWbsService.setCanMoveUpDown(list);
        IPage<ProjectWbs> pageList = new Page<>(1, list.size(), list.size());
        pageList.setRecords(list);
        return Result.OK(pageList);
    }

    /**
     * 批量查询子节点
     *
     * @param parentIds 父ID（多个采用半角逗号分割）
     * @param parentIds
     * @return 返回 IPage
     * @return
     */
    @AutoLog(value = "project_wbs-批量获取子数据")
    @ApiOperation(value = "project_wbs-批量获取子数据", notes = "project_wbs-批量获取子数据")
    @GetMapping("/getChildListBatch")
    public Result<?> getChildListBatch(@RequestParam("parentIds") String parentIds) { // 此接口不分页
        List<ProjectWbs> wbsList = new ArrayList<>();
        try {
            List<String> parentIdList = Arrays.asList(parentIds.split(","));
            parentIdList.forEach(parentId -> {
                LambdaQueryWrapper<ProjectWbs> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(ProjectWbs::getPid, parentId).orderByAsc(ProjectWbs::getSortNo);
                List<ProjectWbs> list = projectWbsService.list(queryWrapper);
                wbsList.addAll(list);
            });
            projectWbsService.setCanMoveUpDown(wbsList);
            IPage<ProjectWbs> pageList = new Page<>(1, wbsList.size(), wbsList.size());
            pageList.setRecords(wbsList);
            return Result.OK(pageList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("批量查询子节点失败：" + e.getMessage());
        }
    }

    /**
     * 上移
     *
     * @param projectWbs
     * @return
     */
    @AutoLog(value = "project_wbs-上移")
    @ApiOperation(value = "project_wbs-上移", notes = "project_wbs-上移")
    @PostMapping(value = "/moveUp")
    public Result<?> moveUp(@RequestBody ProjectWbs projectWbs) {
        projectWbsService.moveProjectWbs(projectWbs, true);
        return Result.OK("成功上移");
    }

    @AutoLog(value = "project_wbs-下移")
    @ApiOperation(value = "project_wbs-下移", notes = "project_wbs-下移")
    @PostMapping(value = "/moveDown")
    public Result<?> moveDown(@RequestBody ProjectWbs projectWbs) {
        projectWbsService.moveProjectWbs(projectWbs, false);
        return Result.OK("成功下移！");
    }

    /**
     * 添加
     *
     * @param projectWbs
     * @return
     */
    @AutoLog(value = "project_wbs-添加")
    @ApiOperation(value = "project_wbs-添加", notes = "project_wbs-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody ProjectWbs projectWbs) {
        projectWbsService.addProjectWbs(projectWbs);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param projectWbs
     * @return
     */
    @AutoLog(value = "project_wbs-编辑")
    @ApiOperation(value = "project_wbs-编辑", notes = "project_wbs-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody ProjectWbs projectWbs) {
        projectWbsService.editProjectWbs(projectWbs);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */

    @Transactional(rollbackFor = Exception.class)
    @AutoLog(value = "project_wbs-通过id删除")
    @ApiOperation(value = "project_wbs-通过id删除", notes = "project_wbs-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        // 最新需求,有子节点的不允许删除,有关联的不允许删除
        ProjectWbs projectWbs = projectWbsService.getById(id);
        if (projectWbs == null) {
            return Result.error("未找到对应记录");
        }
        if (projectWbsService.count(new LambdaQueryWrapper<ProjectWbs>().eq(ProjectWbs::getPid, id)) > 0) {
            return Result.error("存在下级节点，不允许删除!");
        }
        if (projectWbs.getIsPlan().equals(1)) {
            return Result.error("已关联,不允许删除!");
        }
        projectWbsService.deleteProjectWbs(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */

    @AutoLog(value = "project_wbs-批量删除")
    @ApiOperation(value = "project_wbs-批量删除", notes = "project_wbs-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.projectWbsService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "project_wbs-通过id查询")
    @ApiOperation(value = "project_wbs-通过id查询", notes = "project_wbs-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        ProjectWbs projectWbs = projectWbsService.getById(id);
        if (projectWbs == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(projectWbs);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param projectWbs
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ProjectWbs projectWbs) {
        return super.exportXls(request, projectWbs, ProjectWbs.class, "project_wbs");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ProjectWbs.class);
    }

}
