package com.jinghe.breeze.modules.project.entity;

import java.io.Serializable;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: project_plan
 * @Author: jeecg-boot
 * @Date: 2024-05-09
 * @Version: V1.0
 */
@Data
@TableName("construction_plan")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "project_plan对象", description = "project_plan")
public class ProjectPlan implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 标识
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "标识")
    private java.lang.String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
    /**
     * 删除标识
     */
    @Excel(name = "删除标识", width = 15)
    @ApiModelProperty(value = "删除标识")
    private java.lang.Integer delFlag;
    /**
     * 组织机构编码
     */
    @ApiModelProperty(value = "组织机构编码")
    private java.lang.String sysOrgCode;
    /**
     * 项目id
     */
    @Excel(name = "项目id", width = 15)
    @ApiModelProperty(value = "项目id")
    private java.lang.String projectId;
    /**
     * 编码
     */
    @Excel(name = "编码", width = 15)
    @ApiModelProperty(value = "编码")
    private java.lang.String code;
    /**
     * 名称
     */
    @Excel(name = "名称", width = 15)
    @ApiModelProperty(value = "名称")
    private java.lang.String name;
    /**
     * 计划开始日期
     */
    @Excel(name = "计划开始日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "计划开始日期")
    private java.util.Date startDate;
    /**
     * 计划结束日期
     */
    @Excel(name = "计划结束日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "计划结束日期")
    private java.util.Date endDate;
    /**
     * 任务类型
     */
    @Excel(name = "任务类型", width = 15)
    @ApiModelProperty(value = "任务类型")
    private java.lang.String taskType;
    /**
     * 计划工期
     */
    @Excel(name = "计划工期", width = 15)
    @ApiModelProperty(value = "计划工期")
    private java.lang.Integer planDay;
    /**
     * 计划完成数量
     */
    @Excel(name = "计划完成数量", width = 15)
    @ApiModelProperty(value = "计划完成数量")
    private java.lang.Integer planNum;
    /**
     * 实际开始日期
     */
    @Excel(name = "实际开始日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "实际开始日期")
    private java.util.Date actualStartDate;
    /**
     * 实际结束日期
     */
    @Excel(name = "实际结束日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "实际结束日期")
    private java.util.Date actualEndDate;
    /**
     * 实际持续天数
     */
    @Excel(name = "实际持续天数", width = 15)
    @ApiModelProperty(value = "实际持续天数")
    private java.lang.Integer actualDay;
    /**
     * 实际完成数量
     */
    @Excel(name = "实际完成数量", width = 15)
    @ApiModelProperty(value = "实际完成数量")
    private java.lang.Integer actualNum;
    /**
     * 备注
     */
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
    /**
     * 状态
     */
    @Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态")
    private java.lang.Integer status;
    /**
     * 父级节点
     */
    @Excel(name = "父级节点", width = 15)
    @ApiModelProperty(value = "父级节点")
    private java.lang.String pid;
    /**
     * 是否有子节点
     */
    @Excel(name = "是否有子节点", width = 15)
    @ApiModelProperty(value = "是否有子节点")
    private java.lang.String hasChild;
    /**
     * 后续wbs编码
     */
    @Excel(name = "后续wbs编码", width = 15)
    @ApiModelProperty(value = "后续wbs编码")
    private java.lang.String followTaskCodes;

    @Excel(name = "后续wbs对应ids", width = 15)
    @ApiModelProperty(value = "后续wbs对应ids")
    private java.lang.String followTaskIds;


    @Excel(name = "后续wbs编码", width = 15)
    @ApiModelProperty(value = "后续wbs编码")
    private java.lang.String followTaskRelationship;

    @Excel(name = "延迟天数", width = 15)
    @ApiModelProperty(value = "延迟天数")
    private String delayDays;

    /**
     * 进度填报日志
     */
    @Excel(name = "进度填报日志", width = 15)
    @ApiModelProperty(value = "进度填报日志")
    private java.lang.String actualRemark;
    /**
     * 是否里程碑
     */
    @Excel(name = "是否里程碑", width = 15)
    @ApiModelProperty(value = "是否里程碑")
    private java.lang.Integer isKeytask;
    /**
     * 大纲层级
     */
    @Excel(name = "大纲层级", width = 15)
    @ApiModelProperty(value = "大纲层级")
    private java.lang.Integer layer;

    /**
     * 版本号
     */
    @Excel(name = "版本号", width = 15)
    @ApiModelProperty(value = "版本号")
    private java.lang.Integer version;

    @TableField(exist = false)
    private ProjectPlanReport projectPlanReport;

    @TableField(exist = false)
    private List<ConstructionPlanProcess> planProcessList;
}
