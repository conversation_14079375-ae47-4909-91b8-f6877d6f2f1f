package com.jinghe.breeze.modules.sailingcheck.service.impl;

import com.jinghe.breeze.modules.sailingcheck.entity.UserSailingRecord;
import com.jinghe.breeze.modules.sailingcheck.mapper.UserSailingRecordMapper;
import com.jinghe.breeze.modules.sailingcheck.service.IUserSailingRecordService;
import org.jeecg.common.api.vo.Result;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: user_sailing_record
 * @Author: jeecg-boot
 * @Date: 2024-06-18
 * @Version: V1.0
 */

@Service
public class UserSailingRecordServiceImpl extends ServiceImpl<UserSailingRecordMapper, UserSailingRecord> implements IUserSailingRecordService {



    @Override
    public Result<?> unitInfo(String id, int type) {
        if (type == 0) {

        } else {

        }

        return null;
    }

}
