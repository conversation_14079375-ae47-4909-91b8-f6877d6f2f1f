package com.jinghe.breeze.modules.project.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import com.jinghe.breeze.modules.project.entity.ProjectPlanReport;
import com.jinghe.breeze.modules.project.service.IProjectPlanReportService;

import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: project_plan_report
 * @Author: jeecg-boot
 * @Date: 2024-05-10
 * @Version: V1.0
 */
@Api(tags = "project_plan_report")
@RestController
@RequestMapping("/project/projectPlanReport")
@Slf4j
public class ProjectPlanReportController extends JeecgController<ProjectPlanReport, IProjectPlanReportService> {
    @Autowired
    private IProjectPlanReportService projectPlanReportService;

    /**
     * 分页列表查询
     * @return
     */
    @AutoLog(value = "project_plan_report-分页列表查询")
    @ApiOperation(value = "project_plan_report-分页列表查询", notes = "project_plan_report-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(
            @RequestParam(name = "version", required = false) Integer version,
            @RequestParam(name = "name", required = false) String name,
            @RequestParam(name = "status", required = false) Integer status) {
        return projectPlanReportService.getAll(version, name, status);
    }

    /**
     * 添加
     *
     * @param projectPlanReport
     * @return
     */
    @AutoLog(value = "project_plan_report-添加")
    @ApiOperation(value = "project_plan_report-添加", notes = "project_plan_report-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody ProjectPlanReport projectPlanReport) {
        return projectPlanReportService.saveByCode(projectPlanReport);
    }

    /**
     * 编辑
     *
     * @param projectPlanReport
     * @return
     */
    @AutoLog(value = "project_plan_report-编辑")
    @ApiOperation(value = "project_plan_report-编辑", notes = "project_plan_report-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody ProjectPlanReport projectPlanReport) {

        return projectPlanReportService.update(projectPlanReport);
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "project_plan_report-通过id删除")
    @ApiOperation(value = "project_plan_report-通过id删除", notes = "project_plan_report-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        projectPlanReportService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "project_plan_report-批量删除")
    @ApiOperation(value = "project_plan_report-批量删除", notes = "project_plan_report-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.projectPlanReportService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "project_plan_report-通过id查询")
    @ApiOperation(value = "project_plan_report-通过id查询", notes = "project_plan_report-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        ProjectPlanReport projectPlanReport = projectPlanReportService.getById(id);
        if (projectPlanReport == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(projectPlanReport);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param projectPlanReport
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ProjectPlanReport projectPlanReport) {
        return super.exportXls(request, projectPlanReport, ProjectPlanReport.class, "project_plan_report");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ProjectPlanReport.class);
    }


    /**
     * 进度分析
     *
     * @param
     * @return
     */
    @AutoLog(value = "进度分析")
    @ApiOperation(value = "进度分析", notes = "进度分析")
    @GetMapping(value = "/analysis")
    public Result<?> analysis() {
        return projectPlanReportService.analysis();
    }

    /**
     * 施工进度
     *
     * @param
     * @return
     */
    @AutoLog(value = "施工进度")
    @ApiOperation(value = "施工进度", notes = "进度分析")
    @GetMapping(value = "/schedule")
    public Result<?> schedule() {
        return projectPlanReportService.schedule();
    }

}
