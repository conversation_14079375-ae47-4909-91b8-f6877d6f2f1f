package com.jinghe.breeze.modules.person.mapper;

import java.util.List;
import com.jinghe.breeze.modules.person.entity.DictPersonItem;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 数据字典表-子表
 * @Author:
 * @Date:   2022-08-31
 * @Version: V1.0
 */
public interface DictPersonItemMapper extends BaseMapper<DictPersonItem> {

	public boolean deleteByMainId(@Param("mainId") String mainId);
    
	public List<DictPersonItem> selectByMainId(@Param("mainId") String mainId);
}
