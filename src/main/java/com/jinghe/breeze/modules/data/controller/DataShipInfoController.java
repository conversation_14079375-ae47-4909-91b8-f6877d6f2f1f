package com.jinghe.breeze.modules.data.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.jinghe.breeze.modules.data.entity.DataShipInfo;
import com.jinghe.breeze.modules.data.service.IDataShipInfoService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: 船舶信息
 * @Author: jeecg-boot
 * @Date: 2024-04-17
 * @Version: V1.0
 */
@Api(tags = "船舶信息")
@RestController
@RequestMapping("/data/dataShipInfo")
@Slf4j
public class DataShipInfoController extends JeecgController<DataShipInfo, IDataShipInfoService> {
    @Autowired
    private IDataShipInfoService dataShipInfoService;

    /**
     * 分页列表查询
     *
     * @param dataShipInfo
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "船舶信息-分页列表查询")
    @ApiOperation(value = "船舶信息-分页列表查询", notes = "船舶信息-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(DataShipInfo dataShipInfo,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<DataShipInfo> queryWrapper = QueryGenerator.initQueryWrapper(dataShipInfo, req.getParameterMap());
        Page<DataShipInfo> page = new Page<DataShipInfo>(pageNo, pageSize);
        IPage<DataShipInfo> pageList = dataShipInfoService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    @ApiOperation(value = "船舶信息-所有", notes = "船舶信息-所有")
    @GetMapping(value = "/getAll")
    public Result<?> getAll() {
        return Result.OK(dataShipInfoService.list());
    }

    /**
     * 添加
     *
     * @param dataShipInfo
     * @return
     */
    @AutoLog(value = "船舶信息-添加")
    @ApiOperation(value = "船舶信息-添加", notes = "船舶信息-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody DataShipInfo dataShipInfo) {
        dataShipInfoService.save(dataShipInfo);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param dataShipInfo
     * @return
     */
    @AutoLog(value = "船舶信息-编辑")
    @ApiOperation(value = "船舶信息-编辑", notes = "船舶信息-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody DataShipInfo dataShipInfo) {
        dataShipInfoService.updateById(dataShipInfo);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "船舶信息-通过id删除")
    @ApiOperation(value = "船舶信息-通过id删除", notes = "船舶信息-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        dataShipInfoService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "船舶信息-批量删除")
    @ApiOperation(value = "船舶信息-批量删除", notes = "船舶信息-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.dataShipInfoService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "船舶信息-通过id查询")
    @ApiOperation(value = "船舶信息-通过id查询", notes = "船舶信息-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        DataShipInfo dataShipInfo = dataShipInfoService.getById(id);
        if (dataShipInfo == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(dataShipInfo);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param dataShipInfo
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DataShipInfo dataShipInfo) {
        return super.exportXls(request, dataShipInfo, DataShipInfo.class, "船舶信息");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DataShipInfo.class);
    }

}
