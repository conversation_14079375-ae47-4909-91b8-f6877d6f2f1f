package com.jinghe.breeze.modules.person.service.impl;

import com.jinghe.breeze.modules.person.entity.DictPerson;
import com.jinghe.breeze.modules.person.entity.DictPersonItem;
import com.jinghe.breeze.modules.person.mapper.DictPersonItemMapper;
import com.jinghe.breeze.modules.person.mapper.DictPersonMapper;
import com.jinghe.breeze.modules.person.service.IDictPersonService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;
import java.util.Collection;

/**
 * @Description: 数据字典-工种表
 * @Author: chenliang
 * @Date:   2022-08-31
 * @Version: V1.0
 */
@Service
public class DictPersonServiceImpl extends ServiceImpl<DictPersonMapper, DictPerson> implements IDictPersonService {

	@Autowired
	private DictPersonMapper dictPersonMapper;
	@Autowired
	private DictPersonItemMapper dictPersonItemMapper;
	
	@Override
	@Transactional
	public void saveMain(DictPerson dictPerson, List<DictPersonItem> dictPersonItemList) {
		dictPersonMapper.insert(dictPerson);
		if(dictPersonItemList!=null && dictPersonItemList.size()>0) {
			for(DictPersonItem entity:dictPersonItemList) {
				//外键设置
				entity.setBaseId(dictPerson.getId());
				dictPersonItemMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional
	public void updateMain(DictPerson dictPerson,List<DictPersonItem> dictPersonItemList) {
		dictPersonMapper.updateById(dictPerson);
		
		//1.先删除子表数据
		dictPersonItemMapper.deleteByMainId(dictPerson.getId());
		
		//2.子表数据重新插入
		if(dictPersonItemList!=null && dictPersonItemList.size()>0) {
			for(DictPersonItem entity:dictPersonItemList) {
				//外键设置
				entity.setBaseId(dictPerson.getId());
				dictPersonItemMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional
	public void delMain(String id) {
		dictPersonItemMapper.deleteByMainId(id);
		dictPersonMapper.deleteById(id);
	}

	@Override
	@Transactional
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			dictPersonItemMapper.deleteByMainId(id.toString());
			dictPersonMapper.deleteById(id);
		}
	}
}
