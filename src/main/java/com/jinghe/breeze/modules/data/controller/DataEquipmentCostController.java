package com.jinghe.breeze.modules.data.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.jinghe.breeze.modules.data.entity.DataEquipmentCost;
import com.jinghe.breeze.modules.data.service.IDataEquipmentCostService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;


import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: data_equipment_cost
 * @Author: jeecg-boot
 * @Date:   2024-06-05
 * @Version: V1.0
 */
@Api(tags="data_equipment_cost")
@RestController
@RequestMapping("/data/dataEquipmentCost")
@Slf4j
public class DataEquipmentCostController extends JeecgController<DataEquipmentCost, IDataEquipmentCostService> {
	@Autowired
	private IDataEquipmentCostService dataEquipmentCostService;
	
	/**
	 * 分页列表查询
	 *
	 * @param dataEquipmentCost
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "data_equipment_cost-分页列表查询")
	@ApiOperation(value="data_equipment_cost-分页列表查询", notes="data_equipment_cost-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(DataEquipmentCost dataEquipmentCost,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<DataEquipmentCost> queryWrapper = QueryGenerator.initQueryWrapper(dataEquipmentCost, req.getParameterMap());
		Page<DataEquipmentCost> page = new Page<>(pageNo, pageSize);
		IPage<DataEquipmentCost> pageList = dataEquipmentCostService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param dataEquipmentCost
	 * @return
	 */
	@AutoLog(value = "data_equipment_cost-添加")
	@ApiOperation(value="data_equipment_cost-添加", notes="data_equipment_cost-添加")
	@RequiresPermissions("dataEquipmentCost:add")
	@PostMapping(value = "/add")
	public Result<?> add(@Validated @RequestBody DataEquipmentCost dataEquipmentCost) {
		dataEquipmentCostService.save(dataEquipmentCost);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param dataEquipmentCost
	 * @return
	 */
	@AutoLog(value = "data_equipment_cost-编辑")
	@ApiOperation(value="data_equipment_cost-编辑", notes="data_equipment_cost-编辑")
	@RequiresPermissions("dataEquipmentCost:edit")
	@PutMapping(value = "/edit")
	public Result<?> edit(@Validated @RequestBody DataEquipmentCost dataEquipmentCost) {
		dataEquipmentCostService.updateById(dataEquipmentCost);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "data_equipment_cost-通过id删除")
	@ApiOperation(value="data_equipment_cost-通过id删除", notes="data_equipment_cost-通过id删除")
	@RequiresPermissions("dataEquipmentCost:delete")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		dataEquipmentCostService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "data_equipment_cost-批量删除")
	@ApiOperation(value="data_equipment_cost-批量删除", notes="data_equipment_cost-批量删除")
	@RequiresPermissions("dataEquipmentCost:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.dataEquipmentCostService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "data_equipment_cost-通过id查询")
	@ApiOperation(value="data_equipment_cost-通过id查询", notes="data_equipment_cost-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		DataEquipmentCost dataEquipmentCost = dataEquipmentCostService.getById(id);
		if(dataEquipmentCost==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(dataEquipmentCost);
	}
}
