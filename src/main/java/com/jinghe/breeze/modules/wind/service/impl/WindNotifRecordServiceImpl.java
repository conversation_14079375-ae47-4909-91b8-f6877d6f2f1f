package com.jinghe.breeze.modules.wind.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.common.constants.enums.WindNotifyRecordEnum;
import com.jinghe.breeze.modules.person.service.impl.PersonInfoServiceImpl;
import com.jinghe.breeze.modules.wind.entity.WindNotifRecord;
import com.jinghe.breeze.modules.wind.entity.WindNotification;
import com.jinghe.breeze.modules.wind.entity.WindReport;
import com.jinghe.breeze.modules.wind.mapper.WindNotifRecordMapper;
import com.jinghe.breeze.modules.wind.service.IWindNotifRecordService;
import com.jinghe.breeze.modules.wind.service.IWindNotificationService;
import com.jinghe.breeze.modules.wind.service.IWindReportDetailService;
import com.jinghe.breeze.modules.wind.service.IWindReportService;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description: wind_notif_record
 * @Author: jeecg-boot
 * @Date: 2024-05-23
 * @Version: V1.0
 */
@Service
public class WindNotifRecordServiceImpl extends ServiceImpl<WindNotifRecordMapper, WindNotifRecord> implements IWindNotifRecordService {
    @Autowired
    private PersonInfoServiceImpl personInfoService;
    @Autowired
    private IWindNotificationService windNotificationService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private IWindReportService windReportService;
    @Autowired
    private IWindReportDetailService windReportDetailService;

    /**
     * 根据当前登录账号获取取自身对应的未填报过的,且未截止的避风通知
     *
     * @return
     */
    @Override
    public List<WindNotifRecord> getListByLoginUser() {
        //不要已结束和以到期的
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userId = sysUser.getId();
        LambdaQueryWrapper<WindNotifRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WindNotifRecord::getUserId, userId)
                .eq(WindNotifRecord::getDelFlag, Common.delete_flag.OK)
                .eq(WindNotifRecord::getState, WindNotifyRecordEnum.NOTDONE)
                .orderByDesc(WindNotifRecord::getCreateTime);
        List<WindNotifRecord> recordList = baseMapper.selectList(queryWrapper);
        if (recordList.isEmpty()) {
            return recordList;
        }
        List<String> notifyIdList = recordList.stream().map(WindNotifRecord::getNotificationId).distinct().collect(Collectors.toList());
        List<WindNotification> windNotificationList = windNotificationService.list(new LambdaQueryWrapper<WindNotification>()
                .in(WindNotification::getId, notifyIdList) //目标id
//                .eq(WindNotification::getState, 1) //已发布
//                .gt(WindNotification::getPlanEndDate, new Date())
        );//未结束
//        List<WindNotification> windNotificationList = windNotificationService.listByIds(notifyIdList);
        recordList.forEach(notify -> {
            windNotificationList.stream().filter(notification -> Objects.equals(notify.getNotificationId(), notification.getId()))
                    .findFirst().ifPresent(notify::setWindNotification);
        });
        return recordList;
    }

    /**
     * 判断一个WindNotifRecord对象是否可新增填报
     *
     * @param notifyRecord
     * @return
     */
    private Boolean checkCanAdd(WindNotifRecord notifyRecord) {
        WindNotification notify = notifyRecord.getWindNotification();
        if (notify == null) {
            return false; //已经删除了
        }
        if (Objects.equals(notify.getState(), 2)) {
            return false; //已经手动结束了
        }
        int reportCount = windReportService.count(new LambdaQueryWrapper<WindReport>().eq(WindReport::getNotifyId, notify.getId()));
        if (reportCount > 0) { //已经填报过了
            return false;
        }
        Date deadline = notify.getDeadline(); //检查截止日期
        if (deadline == null) {
            return true;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(deadline);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        deadline = calendar.getTime();
        return deadline.compareTo(new Date()) >= 0;
    }
}
