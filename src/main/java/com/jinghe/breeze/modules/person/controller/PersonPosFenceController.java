package com.jinghe.breeze.modules.person.controller;
import org.jeecg.common.util.RedisUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.jinghe.breeze.modules.person.entity.PersonPosFence;
import com.jinghe.breeze.modules.person.service.IPersonPosFenceService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 人员定位电子围栏信息表
 * @Author: chenliang
 * @Date:   2022-08-23
 * @Version: V1.0
 */
@Api(tags="人员定位电子围栏信息表")
@RestController
@RequestMapping("/person/personPosFence")
@Slf4j
public class PersonPosFenceController extends JeecgController<PersonPosFence, IPersonPosFenceService> {
	@Autowired
	private IPersonPosFenceService personPosFenceService;

	@Autowired
	private RedisUtil redisUtil;
	/**
	 * 分页列表查询
	 *
	 * @param personPosFence
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "人员定位电子围栏信息表-分页列表查询")
	@ApiOperation(value="人员定位电子围栏信息表-分页列表查询", notes="人员定位电子围栏信息表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(PersonPosFence personPosFence,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<PersonPosFence> queryWrapper = QueryGenerator.initQueryWrapper(personPosFence, req.getParameterMap());
		Page<PersonPosFence> page = new Page<PersonPosFence>(pageNo, pageSize);
		IPage<PersonPosFence> pageList = personPosFenceService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param
	 * @return
	 */
	@AutoLog(value = "人员定位电子围栏信息表-添加")
	@ApiOperation(value="人员定位电子围栏信息表-添加", notes="人员定位电子围栏信息表-添加")
	@RequestMapping(value = "/add")
	public Result<?> add(String fenceName, String drawType, String isUsed ,  String  nautica) {
		PersonPosFence personPosFence = new PersonPosFence();
		personPosFence.setFenceName(fenceName);
		personPosFence.setDrawType(drawType);
		personPosFence.setIsUsed(isUsed);
		personPosFence.setNautica(nautica);
		personPosFenceService.save(personPosFence);
		return Result.OK(personPosFence);
	}

	 /**
	  * 编辑
	  * @param id
	  * @param fenceName
	  * @param drawType
	  * @param isUsed
	  * @param nautica
	  * @return
	  */
	@AutoLog(value = "人员定位电子围栏信息表-编辑")
	@ApiOperation(value="人员定位电子围栏信息表-编辑", notes="人员定位电子围栏信息表-编辑")
	@RequestMapping(value = "/edit")
	public Result<?> edit(String id,String fenceName, String drawType, String isUsed ,  String  nautica) {
		PersonPosFence personPosFence = new PersonPosFence();
		personPosFence.setId(id);
		personPosFence.setFenceName(fenceName);
		personPosFence.setDrawType(drawType);
		personPosFence.setIsUsed(isUsed);
		personPosFence.setNautica(nautica);
		personPosFenceService.updateById(personPosFence);
		return Result.OK(personPosFence);
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "人员定位电子围栏信息表-通过id删除")
	@ApiOperation(value="人员定位电子围栏信息表-通过id删除", notes="人员定位电子围栏信息表-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		personPosFenceService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "人员定位电子围栏信息表-批量删除")
	@ApiOperation(value="人员定位电子围栏信息表-批量删除", notes="人员定位电子围栏信息表-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.personPosFenceService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "人员定位电子围栏信息表-通过id查询")
	@ApiOperation(value="人员定位电子围栏信息表-通过id查询", notes="人员定位电子围栏信息表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		PersonPosFence personPosFence = personPosFenceService.getById(id);
		if(personPosFence==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(personPosFence);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param personPosFence
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PersonPosFence personPosFence) {
        return super.exportXls(request, personPosFence, PersonPosFence.class, "人员定位电子围栏信息表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PersonPosFence.class);
    }

}
