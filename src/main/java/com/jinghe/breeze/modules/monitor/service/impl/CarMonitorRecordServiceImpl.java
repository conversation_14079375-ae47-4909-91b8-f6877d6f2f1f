package com.jinghe.breeze.modules.monitor.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghe.breeze.common.enums.ActionEnum;
import com.jinghe.breeze.common.utils.DateUtils;
import com.jinghe.breeze.modules.monitor.entity.CarMonitorRecord;
import com.jinghe.breeze.modules.monitor.mapper.CarMonitorRecordMapper;
import com.jinghe.breeze.modules.monitor.service.ICarMonitorRecordService;
import com.jinghe.breeze.modules.monitor.vo.DataObjectVo;
import com.jinghe.breeze.modules.monitor.vo.MonitorRecordPageVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: monitor_car_record
 * @Author: jeecg-boot
 * @Date: 2024-08-07
 * @Version: V1.0
 */
@Service
public class CarMonitorRecordServiceImpl extends ServiceImpl<CarMonitorRecordMapper, CarMonitorRecord> implements ICarMonitorRecordService {

    @Autowired
    private CarMonitorRecordMapper carMonitorRecordMapper;

    @Override
    public List<DataObjectVo> statisticsByHalfMonth() {
        List<DataObjectVo> list = carMonitorRecordMapper.statisticsByHalfMonth();
        //将action分别添加
        List<DataObjectVo> objects = new ArrayList<>();
        List<DataObjectVo> list1 = list.stream().filter(item -> ActionEnum.IN.getType().equals(item.getAction())).collect(Collectors.toList());
        try {
            List<DataObjectVo> dataObjectVos = DateUtils.completeAndSortData(15, list1);
            dataObjectVos.stream().forEach(item->item.setAction(ActionEnum.IN.getType()));
            objects.addAll(dataObjectVos);
        } catch (Exception e) {
            log.error("数据异常",e);
        }

        List<DataObjectVo> list2 = list.stream().filter(item -> ActionEnum.OUT.getType().equals(item.getAction())).collect(Collectors.toList());
        try {
            List<DataObjectVo> dataObjectVos = DateUtils.completeAndSortData(15, list2);
            dataObjectVos.stream().forEach(item->item.setAction(ActionEnum.OUT.getType()));
            objects.addAll(dataObjectVos);
        } catch (Exception e) {
            log.error("数据异常",e);
        }

        return objects;
    }

    @Override
    public List<DataObjectVo> statisticsByWeek(String gateId) {
        List<DataObjectVo> list = carMonitorRecordMapper.statisticsByWeek(gateId);
        //需要补齐无数据的日期
        List<DataObjectVo> objects = null;
        try {
            objects = DateUtils.completeAndSortData(7, list);
        } catch (Exception e) {
            log.error("数据异常",e);
        }
        return objects;
    }

    @Override
    public IPage<MonitorRecordPageVo> getAttendanceRecords(Page<CarMonitorRecord> page,String gateId) {
        IPage<MonitorRecordPageVo> recordPageVoIPage = carMonitorRecordMapper.getAttendanceRecords(page,gateId);
        List<MonitorRecordPageVo> list = recordPageVoIPage.getRecords();
        list.stream().forEach(item -> {
            if (ObjectUtil.isNotEmpty(item.getInTime()) && ObjectUtil.isNotEmpty(item.getOutTime())) {
                //计算值
                String duration = DateUtils.ShowTimeInterval(item.getInTime(), item.getOutTime());
                if (StrUtil.isNotEmpty(duration)) {
                    item.setDuration(duration);
                }
            }
        });
        return recordPageVoIPage;
    }

    @Override
    public String carTotalCount() {
        List<DataObjectVo> list = carMonitorRecordMapper.carTotalCount();
        if (CollectionUtil.isNotEmpty(list)){
            //获取今日车辆
            DataObjectVo dataObjectVo = list.get(0);
            //判断是不是今天的
            boolean isToday = DateUtil.isSameDay(DateUtil.parse(dataObjectVo.getDay()), DateUtil.date());
            if (isToday){
                return dataObjectVo.getCount();
            }
        }
        return "0";
    }
}
