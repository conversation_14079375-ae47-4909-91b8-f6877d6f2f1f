package com.jinghe.breeze.modules.person.dto;

import com.jinghe.breeze.modules.person.entity.PersonInfo;
import com.jinghe.breeze.modules.safety.entity.SafeViolationRecords;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jeecg.common.aspect.annotation.Dict;

/**
 * @Description: 增强的位置数据传输对象
 * @Author: system
 * @Date: 2025-01-02
 * @Version: V1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EnrichedLocationDataDTO", description = "增强的位置数据传输对象")
public class EnrichedLocationDataDTO extends LocationDataDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "是否通过考核")
    private Boolean isAssessed;

    @ApiModelProperty(value = "是否登记")
    private Boolean isRegistered;

    @ApiModelProperty(value = "人员id")
    @Dict(dictTable = "person_info", dicText = "name", dicCode = "id")
    private String personId;

    @ApiModelProperty(value = "最新违规记录")
    private SafeViolationRecords latestViolation;
}
