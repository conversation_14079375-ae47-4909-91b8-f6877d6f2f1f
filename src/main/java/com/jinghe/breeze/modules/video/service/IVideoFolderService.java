package com.jinghe.breeze.modules.video.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jinghe.breeze.modules.video.entity.VideoFolder;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.exception.JeecgBootException;

import java.util.List;

/**
 * @Description: video_folder
 * @Author: jeecg-boot
 * @Date: 2024-06-12
 * @Version: V1.0
 */
public interface IVideoFolderService extends IService<VideoFolder> {

    /**
     * 根节点父ID的值
     */
    public static final String ROOT_PID_VALUE = "0";

    /**
     * 树节点有子节点状态值
     */
    public static final String HASCHILD = "1";

    /**
     * 树节点无子节点状态值
     */
    public static final String NOCHILD = "0";

    /**
     * 新增节点
     */
    void addVideoFolder(VideoFolder videoFolder);

    /**
     * 删除节点
     */
    void deleteVideoFolder(String id) throws JeecgBootException;


    List<VideoFolder> getTwoLayerNodeList();

    IPage<VideoFolder> getChildList(VideoFolder videoFolder);

    IPage<VideoFolder> getChildListBatch(String parentIds);

    void moveFolder(VideoFolder videoFolder, boolean isUp);

    boolean checkDuplicateSameLayer(String id,String name);
    void editFolder(VideoFolder videoFolder);
}
