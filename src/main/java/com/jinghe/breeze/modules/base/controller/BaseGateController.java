package com.jinghe.breeze.modules.base.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinghe.breeze.modules.base.entity.BaseGate;
import com.jinghe.breeze.modules.base.entity.BaseGateEquipment;
import com.jinghe.breeze.modules.base.service.IBaseGateEquipmentService;
import com.jinghe.breeze.modules.base.service.IBaseGateService;
import com.jinghe.breeze.modules.base.vo.BaseGatePage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 闸机设备
 * @Author: jeecg-boot
 * @Date: 2024-08-06
 * @Version: V1.0
 */
@Api(tags = "闸机设备")
@RestController
@RequestMapping("/base/gate")
@Slf4j
public class BaseGateController {
    @Autowired
    private IBaseGateService baseGateService;
    @Autowired
    private IBaseGateEquipmentService baseGateEquipmentService;

    /**
     * 分页列表查询
     *
     * @param baseGate
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "闸机设备-分页列表查询")
    @ApiOperation(value = "闸机设备-分页列表查询", notes = "闸机设备-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(BaseGate baseGate,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<BaseGate> queryWrapper = QueryGenerator.initQueryWrapper(baseGate, req.getParameterMap());
        Page<BaseGate> page = new Page<BaseGate>(pageNo, pageSize);
        IPage<BaseGate> pageList = baseGateService.page(page, queryWrapper);
        IPage<BaseGatePage> pageResult = new Page<>();
        //设置明细数据
        List<BaseGate> records = pageList.getRecords();
        List<BaseGatePage> result = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(records)){
            for (BaseGate record : records) {
                BaseGatePage obj = new BaseGatePage();
                BeanUtil.copyProperties(record,obj);
                List<BaseGateEquipment> baseGateEquipments = baseGateEquipmentService.selectByMainId(record.getId());
                obj.setBaseGateEquipmentList(baseGateEquipments);
                result.add(obj);
            }
        }
        pageResult.setRecords(result);
        pageResult.setTotal(pageList.getTotal());
        return Result.OK(pageResult);
    }

    /**
     * 添加
     *
     * @param baseGatePage
     * @return
     */
    @AutoLog(value = "闸机设备-添加")
    @ApiOperation(value = "闸机设备-添加", notes = "闸机设备-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody BaseGatePage baseGatePage) {
        BaseGate baseGate = new BaseGate();
        BeanUtils.copyProperties(baseGatePage, baseGate);
        baseGateService.saveMain(baseGate, baseGatePage.getBaseGateEquipmentList());
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param baseGatePage
     * @return
     */
    @AutoLog(value = "闸机设备-编辑")
    @ApiOperation(value = "闸机设备-编辑", notes = "闸机设备-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody BaseGatePage baseGatePage) {
        BaseGate baseGate = new BaseGate();
        BeanUtils.copyProperties(baseGatePage, baseGate);
        BaseGate baseGateEntity = baseGateService.getById(baseGate.getId());
        if (baseGateEntity == null) {
            return Result.error("未找到对应数据");
        }
        baseGateService.updateMain(baseGate, baseGatePage.getBaseGateEquipmentList());
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "闸机设备-通过id删除")
    @ApiOperation(value = "闸机设备-通过id删除", notes = "闸机设备-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        baseGateService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "闸机设备-批量删除")
    @ApiOperation(value = "闸机设备-批量删除", notes = "闸机设备-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.baseGateService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "闸机设备-通过id查询")
    @ApiOperation(value = "闸机设备-通过id查询", notes = "闸机设备-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        BaseGate baseGate = baseGateService.getById(id);
        if (baseGate == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(baseGate);

    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "闸机设备_通过主表ID查询")
    @ApiOperation(value = "闸机设备_主表ID查询", notes = "闸机设备-通主表ID查询")
    @GetMapping(value = "/queryBaseGateEquipmentByMainId")
    public Result<?> queryBaseGateEquipmentListByMainId(@RequestParam(name = "id", required = true) String id) {
        List<BaseGateEquipment> baseGateEquipmentList = baseGateEquipmentService.selectByMainId(id);
        return Result.OK(baseGateEquipmentList);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param baseGate
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, BaseGate baseGate) {
        // Step.1 组装查询条件查询数据
        QueryWrapper<BaseGate> queryWrapper = QueryGenerator.initQueryWrapper(baseGate, request.getParameterMap());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        //Step.2 获取导出数据
        List<BaseGate> queryList = baseGateService.list(queryWrapper);
        // 过滤选中数据
        String selections = request.getParameter("selections");
        List<BaseGate> baseGateList = new ArrayList<BaseGate>();
        if (oConvertUtils.isEmpty(selections)) {
            baseGateList = queryList;
        } else {
            List<String> selectionList = Arrays.asList(selections.split(","));
            baseGateList = queryList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
        }

        // Step.3 组装pageList
        List<BaseGatePage> pageList = new ArrayList<BaseGatePage>();
        for (BaseGate main : baseGateList) {
            BaseGatePage vo = new BaseGatePage();
            BeanUtils.copyProperties(main, vo);
            List<BaseGateEquipment> baseGateEquipmentList = baseGateEquipmentService.selectByMainId(main.getId());
            vo.setBaseGateEquipmentList(baseGateEquipmentList);
            pageList.add(vo);
        }

        // Step.4 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "闸机设备列表");
        mv.addObject(NormalExcelConstants.CLASS, BaseGatePage.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("闸机设备数据", "导出人:" + sysUser.getRealname(), "闸机设备"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<BaseGatePage> list = ExcelImportUtil.importExcel(file.getInputStream(), BaseGatePage.class, params);
                for (BaseGatePage page : list) {
                    BaseGate po = new BaseGate();
                    BeanUtils.copyProperties(page, po);
                    baseGateService.saveMain(po, page.getBaseGateEquipmentList());
                }
                return Result.OK("文件导入成功！数据行数:" + list.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.OK("文件导入失败！");
    }

    @AutoLog(value = "闸机设备-修改标记")
    @ApiOperation(value = "闸机设备-修改标记", notes = "闸机设备-修改标记")
    @PutMapping(value = "/cleanMark")
    public Result<?> cleanMark(@RequestBody BaseGatePage baseGatePage) {
        BaseGate baseGate = new BaseGate();
        BeanUtils.copyProperties(baseGatePage, baseGate);
        BaseGate baseGateEntity = baseGateService.getById(baseGate.getId());
        if (baseGateEntity == null) {
            return Result.error("未找到对应数据");
        }
        LambdaUpdateWrapper<BaseGate> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(BaseGate::getPositionX,baseGate.getPositionX());
        lambdaUpdateWrapper.set(BaseGate::getPositionY,baseGate.getPositionY());
        lambdaUpdateWrapper.eq(BaseGate::getId,baseGate.getId());
        baseGateService.update(lambdaUpdateWrapper);
        return Result.OK("操作成功");
    }

}
