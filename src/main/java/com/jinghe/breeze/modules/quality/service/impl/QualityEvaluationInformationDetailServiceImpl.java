package com.jinghe.breeze.modules.quality.service.impl;

import com.jinghe.breeze.modules.quality.entity.QualityEvaluationInformationDetail;
import com.jinghe.breeze.modules.quality.mapper.QualityEvaluationInformationDetailMapper;
import com.jinghe.breeze.modules.quality.service.IQualityEvaluationInformationDetailService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 验评资料详情
 * @Author: jeecg-boot
 * @Date:   2025-06-27
 * @Version: V1.0
 */
@Service
public class QualityEvaluationInformationDetailServiceImpl extends ServiceImpl<QualityEvaluationInformationDetailMapper, QualityEvaluationInformationDetail> implements IQualityEvaluationInformationDetailService {
	
	@Autowired
	private QualityEvaluationInformationDetailMapper qualityEvaluationInformationDetailMapper;
	
	@Override
	public List<QualityEvaluationInformationDetail> selectByMainId(String mainId) {
		return qualityEvaluationInformationDetailMapper.selectByMainId(mainId);
	}
}
