package com.jinghe.breeze.modules.video.controller;

import java.util.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import jodd.util.StringUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import com.jinghe.breeze.modules.video.entity.VideoFolder;
import com.jinghe.breeze.modules.video.service.IVideoFolderService;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: video_folder
 * @Author: jeecg-boot
 * @Date: 2024-06-12
 * @Version: V1.0
 */
@Api(tags = "video_folder")
@RestController
@RequestMapping("/video/videoFolder")
@Slf4j
public class VideoFolderController extends JeecgController<VideoFolder, IVideoFolderService> {
    @Autowired
    private IVideoFolderService videoFolderService;

    /**
     * 分页列表查询*
     *
     * @return
     */
    @AutoLog(value = "video_folder-分页列表查询")
    @ApiOperation(value = "video_folder-分页列表查询", notes = "video_folder-分页列表查询")
    @GetMapping(value = "/rootList")
    public Result<?> rootList() {
        List<VideoFolder> folderList = videoFolderService.getTwoLayerNodeList();
        IPage<VideoFolder> pageList = new Page<>(1, folderList.size(), folderList.size());
        pageList.setRecords(folderList);
        return Result.OK(pageList);
    }

    /**
     * 获取子数据
     *
     * @param videoFolder
     * @return
     */
    @AutoLog(value = "video_folder-获取子数据")
    @ApiOperation(value = "video_folder-获取子数据", notes = "video_folder-获取子数据")
    @GetMapping(value = "/childList")
    public Result<?> queryPageList(VideoFolder videoFolder) {
        try {
            IPage<VideoFolder> list = videoFolderService.getChildList(videoFolder);
            return Result.OK(list);
        } catch (JeecgBootException e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 批量查询子节点
     *
     * @param parentIds 父ID（多个采用半角逗号分割）
     * @return 返回 IPage
     * @return
     */
    @AutoLog(value = "video_folder-批量获取子数据")
    @ApiOperation(value = "video_folder-批量获取子数据", notes = "video_folder-批量获取子数据")
    @GetMapping("/getChildListBatch")
    public Result getChildListBatch(@RequestParam("parentIds") String parentIds) {
        try {
            return Result.OK(videoFolderService.getChildListBatch(parentIds));
        } catch (JeecgBootException e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 添加
     *
     * @param videoFolder
     * @return
     */
    @AutoLog(value = "video_folder-添加")
    @ApiOperation(value = "video_folder-添加", notes = "video_folder-添加")
    @RequiresPermissions("videoFolder:add")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody VideoFolder videoFolder) {
        try {
            videoFolderService.addVideoFolder(videoFolder);
            return Result.OK("添加成功！");
        } catch (JeecgBootException e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "video_folder-通过id删除")
    @ApiOperation(value = "video_folder-通过id删除", notes = "video_folder-通过id删除")
    @RequiresPermissions("videoFolder:delete")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        try {
            videoFolderService.deleteVideoFolder(id);
            return Result.OK("删除成功!");
        } catch (JeecgBootException e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "video_folder-通过id查询")
    @ApiOperation(value = "video_folder-通过id查询", notes = "video_folder-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        VideoFolder videoFolder = videoFolderService.getById(id);
        if (videoFolder == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(videoFolder);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param videoFolder
     */
    @RequestMapping(value = "/exportXls")
    @RequiresPermissions("videoFolder:export")
    public ModelAndView exportXls(HttpServletRequest request, VideoFolder videoFolder) {
        return super.exportXls(request, videoFolder, VideoFolder.class, "video_folder");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    @RequiresPermissions("videoFolder:import")
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, VideoFolder.class);
    }

    /**
     * 上移
     *
     * @param videoFolder
     * @return
     */
    @Transactional
    @AutoLog(value = "video-folder-上移")
    @ApiOperation(value = "video-folder-上移", notes = "video-folder-上移")
    @PostMapping(value = "/moveUp")
    public Result<?> moveUp(@RequestBody VideoFolder videoFolder) {
        try {
            videoFolderService.moveFolder(videoFolder, true);
            return Result.OK("上移成功！");
        } catch (JeecgBootException e) {
            return Result.error(e.getMessage());
        }
    }

    @AutoLog(value = "video-folder-下移")
    @Transactional
    @ApiOperation(value = "video-folder-下移", notes = "video-folder-下移")
    @PostMapping(value = "/moveDown")
    public Result<?> moveDown(@RequestBody VideoFolder videoFolder) {
        try {
            videoFolderService.moveFolder(videoFolder, false);
            return Result.OK("下移成功！");
        } catch (JeecgBootException e) {
            return Result.error(e.getMessage());
        }
    }

    @AutoLog(value = "video-folder-edit")
    @ApiOperation(value = "video-folder-edit", notes = "video-folder-edit")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody VideoFolder videoFolder) {
        if (StringUtil.isEmpty(videoFolder.getId())) {
            return Result.error("id不能为空!");
        }
        boolean result = videoFolderService.updateById(videoFolder);
        if (!result) {
            return Result.error("修改失败!");
        }
        return Result.OK("修改成功!");
    }

    @ApiOperation(value = "video-folder-校验同级别下唯一性", notes = "video-folder-校验同级别下唯一性")
    @GetMapping(value = "/checkDuplicateSameLayer")
    public Result<?> checkDuplicateSameLayer(@RequestParam("id") String id,
                                             @RequestParam("name") String name) {
        try {
            boolean result = videoFolderService.checkDuplicateSameLayer(id, name);
            return result ? Result.OK("校验通过!") : Result.error("同层级下已存在同名文件夹!");
        } catch (JeecgBootException e) {
            return Result.error(e.getMessage());
        }
    }
}
