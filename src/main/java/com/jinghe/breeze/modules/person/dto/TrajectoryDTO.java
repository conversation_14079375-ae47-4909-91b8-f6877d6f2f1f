package com.jinghe.breeze.modules.person.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 轨迹数据传输对象
 * @Author: system
 * @Date: 2025-01-02
 * @Version: V1.0
 */
@Data
@ApiModel(value = "TrajectoryDTO", description = "轨迹数据传输对象")
public class TrajectoryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "设备编号")
    private String puname;

    @ApiModelProperty(value = "GPS时间戳")
    private Long gpstime;

    @ApiModelProperty(value = "报警类型")
    private String alarmtype;

    @ApiModelProperty(value = "经度")
    private Double longitude;

    @ApiModelProperty(value = "纬度")
    private Double latitude;
}
