package com.jinghe.breeze.modules.safety.service;

import com.jinghe.breeze.modules.quality.entity.QualityInspectionRecords;
import com.jinghe.breeze.modules.safety.entity.SafeInspectionRecords;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 安全检查
 * @Author: jeecg-boot
 * @Date:   2025-06-24
 * @Version: V1.0
 */
public interface ISafeInspectionRecordsService extends IService<SafeInspectionRecords> {

    void fullDetails(List<SafeInspectionRecords> list);

    void createOrUpdate(SafeInspectionRecords records);


}
