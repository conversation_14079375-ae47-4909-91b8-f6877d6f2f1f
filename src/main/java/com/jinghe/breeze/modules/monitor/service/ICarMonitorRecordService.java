package com.jinghe.breeze.modules.monitor.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghe.breeze.modules.monitor.entity.CarMonitorRecord;
import com.jinghe.breeze.modules.monitor.vo.DataObjectVo;
import com.jinghe.breeze.modules.monitor.vo.MonitorRecordPageVo;

import java.util.List;

/**
 * @Description: monitor_car_record
 * @Author: jeecg-boot
 * @Date: 2024-08-07
 * @Version: V1.0
 */
public interface ICarMonitorRecordService extends IService<CarMonitorRecord> {

    List<DataObjectVo> statisticsByHalfMonth();

    List<DataObjectVo> statisticsByWeek(String gateId);

    IPage<MonitorRecordPageVo> getAttendanceRecords(Page<CarMonitorRecord> page,String gateId);

    String carTotalCount();
}
