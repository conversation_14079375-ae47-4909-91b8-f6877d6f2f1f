package com.jinghe.breeze.modules.construction.entity;

import java.io.Serializable;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 观豚日志
 * @Author: jeecg-boot
 * @Date: 2024-05-11
 * @Version: V1.0
 */
@Data
@TableName("monitor_dolphin_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "monitor_dolphin_log对象", description = "观豚日志")
public class MonitorDolphinLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
    /**
     * 观测日期
     */
    @Excel(name = "观测日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "观测日期")
    private java.util.Date watchDate;
    /**
     * 所属船舶
     */
    @Excel(name = "所属船舶", width = 15)
    @Dict(dicCode = "id", dicText = "name", dictTable = "ship_info")
    @ApiModelProperty(value = "所属船舶")
    private java.lang.String shipId;
    /**
     * 观测人员
     */
    @Excel(name = "观测人员	", width = 15)
    @ApiModelProperty(value = "观测人员")
    private java.lang.String username;
    /**
     * 是否观测到白海豚
     */
    @Dict(dicCode = "yn")
    @Excel(name = "是否观测到白海豚", width = 15)
    @ApiModelProperty(value = "是否观测到白海豚")
    private java.lang.Integer exsitDolphin;

    @TableField(exist = false)  // 如果不存储在数据库中
    private List<MonitorDolphinLogDetail> details; // 子节点>


}
