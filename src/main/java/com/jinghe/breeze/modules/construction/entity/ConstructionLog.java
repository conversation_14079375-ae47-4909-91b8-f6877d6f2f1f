package com.jinghe.breeze.modules.construction.entity;

import java.io.Serializable;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: construction_log
 * @Author: jeecg-boot
 * @Date: 2024-05-09
 * @Version: V1.0
 */
@Data
@TableName("construction_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "construction_log对象", description = "construction_log")
public class ConstructionLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 标识
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "标识")
    private java.lang.String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
    /**
     * 删除标识
     */
    @Excel(name = "删除标识", width = 15)
    @ApiModelProperty(value = "删除标识")
    private java.lang.Integer delFlag;
    /**
     * 组织机构编码
     */
    @ApiModelProperty(value = "组织机构编码")
    private java.lang.String sysOrgCode;
    /**
     * 项目id
     */
    @Excel(name = "项目id", width = 15)
    @ApiModelProperty(value = "项目id")
    private java.lang.String projectId;
    /**
     * pbs Id
     */
    @Excel(name = "pbs Id", width = 15)
    @ApiModelProperty(value = "pbs Id")
    private java.lang.String pbsId;
    /**
     * 工序id
     */
    @Excel(name = "工序id", width = 15)
    @ApiModelProperty(value = "工序id")
    private java.lang.String processId;
    /**
     * 工序id
     */
    @Excel(name = "工序编码", width = 15)
    @ApiModelProperty(value = "工序编码")
    private java.lang.String processCode;
    /**
     * 创建人姓名
     */
    @Excel(name = "创建人姓名", width = 15)
    @ApiModelProperty(value = "创建人姓名")
    private java.lang.String createName;
    /**
     * 单位id
     */
    @Excel(name = "单位id", width = 15)
    @ApiModelProperty(value = "单位id")
    private java.lang.String unitId;
    /**
     * 单位名称
     */
    @Excel(name = "单位名称", width = 15)
    @TableField(exist = false)
    @ApiModelProperty(value = "单位名称")
    private java.lang.String unitName;


    /**
     * 开始时间
     */
    @Excel(name = "开始时间", width = 20, format = "yyyy-MM-dd HH:mm")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间")
    @TableField(updateStrategy = FieldStrategy.IGNORED, insertStrategy = FieldStrategy.IGNORED) //忽略空值检查
    private java.util.Date startTime;
    /**
     * 结束时间
     */
    @Excel(name = "结束时间", width = 20, format = "yyyy-MM-dd HH:mm")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(updateStrategy = FieldStrategy.IGNORED, insertStrategy = FieldStrategy.IGNORED) //忽略空值检查
    @ApiModelProperty(value = "结束时间")
    private java.util.Date endTime;
    /**
     * 耗时
     */
    @Excel(name = "耗时", width = 15)
    @ApiModelProperty(value = "耗时")
    private BigDecimal useTime;
    /**
     * 施工状态
     */
    @Excel(name = "施工状态", width = 15)
    @ApiModelProperty(value = "施工状态")
    private java.lang.Integer constructionState;
    /**
     * 施工内容
     */
    @Excel(name = "施工内容", width = 15)
    @ApiModelProperty(value = "施工内容")
    private java.lang.String content;
    /**
     * 附件
     */
    @Excel(name = "附件", width = 15)
    @ApiModelProperty(value = "附件")
    private java.lang.String file;

    @TableField(exist = false)
    private String processName;

    @TableField(exist = false)
    private String modelSign;
}
