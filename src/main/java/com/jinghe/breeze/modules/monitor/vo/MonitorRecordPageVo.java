package com.jinghe.breeze.modules.monitor.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: monitor_person_record
 * @Author: jeecg-boot
 * @Date: 2024-08-07
 * @Version: V1.0
 */
@Data
@ApiModel(value = "对象")
public class MonitorRecordPageVo implements Serializable {

    private static final long serialVersionUID = -7547543711458399773L;

    private String name;
    private String username;
    private String licensePlate;
    private String path;
    private Date inTime;
    private Date outTime;
    private String duration;
}
