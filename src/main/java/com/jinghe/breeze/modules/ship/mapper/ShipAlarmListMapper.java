package com.jinghe.breeze.modules.ship.mapper;

import com.jinghe.breeze.modules.ship.entity.ShipAlarmDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * @Description: ship_alarm_list
 * @Author: jeecg-boot
 * @Date:   2024-04-23
 * @Version: V1.0
 */
public interface ShipAlarmListMapper extends BaseMapper<ShipAlarmDetail> {

    ShipAlarmDetail selectByCreateDate(@Param("createTime") Date createTime, @Param("mmsi") String mmsi);
}
