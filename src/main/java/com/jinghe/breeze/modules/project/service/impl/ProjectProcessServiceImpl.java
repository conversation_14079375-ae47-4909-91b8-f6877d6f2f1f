package com.jinghe.breeze.modules.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.modules.project.entity.ProjectProcess;
import com.jinghe.breeze.modules.project.mapper.ProjectProcessMapper;
import com.jinghe.breeze.modules.project.service.IProjectProcessService;
import org.jeecg.common.api.vo.Result;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: project_process
 * @Author: jeecg-boot
 * @Date:   2024-04-17
 * @Version: V1.0
 */
@Service
public class ProjectProcessServiceImpl extends ServiceImpl<ProjectProcessMapper, ProjectProcess> implements IProjectProcessService {

    @Override
    public Result<?> listAll() {
        LambdaQueryWrapper<ProjectProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectProcess::getDelFlag, Common.commonality.ZERO);
        queryWrapper.orderByAsc(ProjectProcess::getProcessCode);
        List<ProjectProcess> projectProcesses = baseMapper.selectList(queryWrapper);
        Map<String, List<ProjectProcess>> map = projectProcesses.stream().collect(Collectors.groupingBy(score -> score.getType()));
//        for (String s : map.keySet()) {
//            List<ProjectProcess> projectProcesses1 = map.get(s);
//            List<ProjectProcess> collect = projectProcesses1.stream().sorted(Comparator.comparing(ProjectProcess::getProcessCode)).collect(Collectors.toList());
//            map.put(s,collect);
//        }
        return Result.OK(map);
    }
}
