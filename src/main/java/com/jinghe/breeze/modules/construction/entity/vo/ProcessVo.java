package com.jinghe.breeze.modules.construction.entity.vo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProcessVo {
    @ApiModelProperty(value = "开始时间")
    private java.util.Date startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private java.util.Date endTime;
    /**
     * 耗时
     */
    @ApiModelProperty(value = "耗时")
    private BigDecimal useTime;
    /**
     * pbs类型
     */
    @ApiModelProperty(value = "类型")
    private java.lang.String pbsType;

    @ApiModelProperty(value = "理论耗时")
    private BigDecimal theoryHours;

    @ApiModelProperty(value = "工序名称")
    private java.lang.String name;

    @ApiModelProperty(value = "工作内容")
    private java.lang.String workContent;

    @ApiModelProperty(value = "最大风机")
    private java.lang.String maxName;


    @ApiModelProperty(value = "最小风机")
    private java.lang.String minName;

    @ApiModelProperty(value = "总耗时")
    private java.lang.Double sumUseTime;

    @ApiModelProperty(value = "进度")
    private java.lang.Double schedule;
}
