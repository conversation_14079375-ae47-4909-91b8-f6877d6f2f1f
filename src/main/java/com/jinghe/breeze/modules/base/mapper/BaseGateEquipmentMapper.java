package com.jinghe.breeze.modules.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jinghe.breeze.modules.base.entity.BaseGateEquipment;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: base_gate_equipment
 * @Author: jeecg-boot
 * @Date: 2024-08-06
 * @Version: V1.0
 */
public interface BaseGateEquipmentMapper extends BaseMapper<BaseGateEquipment> {

    public boolean deleteByMainId(@Param("mainId") String mainId);

    public List<BaseGateEquipment> selectByMainId(@Param("mainId") String mainId);
}
