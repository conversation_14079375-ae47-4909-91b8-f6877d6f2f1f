package com.jinghe.breeze.modules.safety.service;

import com.jinghe.breeze.modules.safety.entity.SafeSecurityAlert;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghe.breeze.modules.safety.vo.SafeSecurityStaticsVo;
import org.jeecg.common.api.vo.Result;

/**
 * @Description: 智能安全识别预警
 * @Author: jeecg-boot
 * @Date:   2025-06-22
 * @Version: V1.0
 */
public interface ISafeSecurityAlertService extends IService<SafeSecurityAlert> {

    Result<SafeSecurityStaticsVo> statics();
}
