package com.jinghe.breeze.modules.ship.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.modules.data.entity.DataShipInfo;
import com.jinghe.breeze.modules.data.service.IDataShipInfoService;
import com.jinghe.breeze.modules.ship.entity.ShipInfo;
import com.jinghe.breeze.modules.ship.entity.ShipOutgoing;
import com.jinghe.breeze.modules.ship.mapper.ShipInfoMapper;
import com.jinghe.breeze.modules.ship.service.IShipInfoService;
import com.jinghe.breeze.modules.ship.service.IShipOutgoingService;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: ship_info
 * @Author: jeecg-boot
 * @Date: 2024-04-19
 * @Version: V1.0
 */
@Service
public class ShipInfoServiceImpl extends ServiceImpl<ShipInfoMapper, ShipInfo> implements IShipInfoService {
    @Autowired
    private IDataShipInfoService iDataShipInfoService;

    @Autowired
    private IShipOutgoingService shipOutgoingService;

    @Override
    public void saveInfo(ShipInfo shipInfo) {
        updateDataShip(shipInfo.getShipDataId(), Common.commonality.ONE);
        super.save(shipInfo);
    }

    @Override
    public void delete(String id) {
        LambdaQueryWrapper<ShipInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShipInfo::getId, id);
        ShipInfo shipInfo = super.getOne(queryWrapper);
        updateDataShip(shipInfo.getShipDataId(), Common.commonality.ZERO);
        super.removeById(id);
    }

    private void updateDataShip(String id, int isRelevance) {
        DataShipInfo dataShipInfo = new DataShipInfo();
        dataShipInfo.setId(id);
        dataShipInfo.setIsRelevance(isRelevance);
        iDataShipInfoService.updateById(dataShipInfo);
    }

    public IPage<ShipInfo> getList(ShipInfo shipInfo, Integer pageNo, Integer pageSize) {
        QueryWrapper<ShipInfo> queryWrapper = QueryGenerator.initQueryWrapper(shipInfo, null);
        queryWrapper.orderByDesc("create_time");
        Page<ShipInfo> page = new Page<ShipInfo>(pageNo, pageSize);
        IPage<ShipInfo> pageList = super.page(page, queryWrapper);
        List<ShipInfo> infos = pageList.getRecords();
        List<String> shipIds = infos.stream().map(ShipInfo::getId).collect(Collectors.toList());
        List<ShipOutgoing> shipOutgoings = shipOutgoingService.list(new LambdaQueryWrapper<ShipOutgoing>()
                .in(ShipOutgoing::getShipDataId, shipIds)
                .orderByDesc(ShipOutgoing::getCreateTime));
        Map<String, ShipOutgoing> shipOutgoingMap = shipOutgoings.stream()
                .collect(Collectors.toMap(ShipOutgoing::getShipDataId, record -> record, (existing, replacement) -> existing));
        for (ShipInfo shipInfo1 : infos) {
            if (shipOutgoingMap.containsKey(shipInfo1.getId())) {
                shipInfo1.setStatus(shipOutgoingMap.get(shipInfo1.getId()).getIsOut());
            }
        }
        return pageList;
    }
}
