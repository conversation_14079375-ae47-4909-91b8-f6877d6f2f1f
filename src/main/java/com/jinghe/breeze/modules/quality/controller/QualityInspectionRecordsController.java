package com.jinghe.breeze.modules.quality.controller;

import org.apache.shiro.authz.annotation.RequiresPermissions;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.jinghe.breeze.modules.quality.entity.QualityInspectionRecords;
import com.jinghe.breeze.modules.quality.service.IQualityInspectionRecordsService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: quality_inspection_records
 * @Author: jeecg-boot
 * @Date: 2025-06-20
 * @Version: V1.0
 */
@Api(tags = "quality_inspection_records")
@RestController
@RequestMapping("/quality/qualityInspectionRecords")
@Slf4j
public class QualityInspectionRecordsController extends JeecgController<QualityInspectionRecords, IQualityInspectionRecordsService> {
    @Autowired
    private IQualityInspectionRecordsService qualityInspectionRecordsService;

    /**
     * 分页列表查询
     *
     * @param qualityInspectionRecords
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "quality_inspection_records-分页列表查询")
    @ApiOperation(value = "quality_inspection_records-分页列表查询", notes = "quality_inspection_records-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(QualityInspectionRecords qualityInspectionRecords,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<QualityInspectionRecords> queryWrapper = QueryGenerator.initQueryWrapper(qualityInspectionRecords, req.getParameterMap());
        Page<QualityInspectionRecords> page = new Page<QualityInspectionRecords>(pageNo, pageSize);
        IPage<QualityInspectionRecords> pageList = qualityInspectionRecordsService.page(page, queryWrapper);
        qualityInspectionRecordsService.fullDetails(pageList.getRecords());
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param qualityInspectionRecords
     * @return
     */
    @AutoLog(value = "quality_inspection_records-添加")
    @ApiOperation(value = "quality_inspection_records-添加", notes = "quality_inspection_records-添加")
//	@RequiresPermissions("qualityInspectionRecords:add")
    @PostMapping(value = "/add")
    public Result<?> add(@Validated @RequestBody QualityInspectionRecords qualityInspectionRecords) {
        qualityInspectionRecordsService.createOrUpdate(qualityInspectionRecords);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param qualityInspectionRecords
     * @return
     */
    @AutoLog(value = "quality_inspection_records-编辑")
    @ApiOperation(value = "quality_inspection_records-编辑", notes = "quality_inspection_records-编辑")
//	@RequiresPermissions("qualityInspectionRecords:edit")
    @PutMapping(value = "/edit")
    public Result<?> edit(@Validated @RequestBody QualityInspectionRecords qualityInspectionRecords) {
        qualityInspectionRecordsService.createOrUpdate(qualityInspectionRecords);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "quality_inspection_records-通过id删除")
    @ApiOperation(value = "quality_inspection_records-通过id删除", notes = "quality_inspection_records-通过id删除")
//	@RequiresPermissions("qualityInspectionRecords:delete")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        qualityInspectionRecordsService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "quality_inspection_records-批量删除")
    @ApiOperation(value = "quality_inspection_records-批量删除", notes = "quality_inspection_records-批量删除")
//	@RequiresPermissions("qualityInspectionRecords:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.qualityInspectionRecordsService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "quality_inspection_records-通过id查询")
    @ApiOperation(value = "quality_inspection_records-通过id查询", notes = "quality_inspection_records-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        QualityInspectionRecords qualityInspectionRecords = qualityInspectionRecordsService.getById(id);
        if (qualityInspectionRecords == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(qualityInspectionRecords);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param qualityInspectionRecords
     */
    @RequiresPermissions("qualityInspectionRecords:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, QualityInspectionRecords qualityInspectionRecords) {
        return super.exportXls(request, qualityInspectionRecords, QualityInspectionRecords.class, "quality_inspection_records");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("qualityInspectionRecords:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, QualityInspectionRecords.class);
    }

}
