package com.jinghe.breeze.modules.wind.controller;

import java.util.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.modules.wind.service.IWindNotifRecordService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.query.QueryGenerator;
import com.jinghe.breeze.modules.wind.entity.WindNotification;
import com.jinghe.breeze.modules.wind.service.IWindNotificationService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: wind_notification
 * @Author: jeecg-boot
 * @Date: 2024-05-22
 * @Version: V1.0
 */
@Api(tags = "wind_notification")
@RestController
@RequestMapping("/wind/windNotification")
@Slf4j
public class WindNotificationController extends JeecgController<WindNotification, IWindNotificationService> {
    @Autowired
    private IWindNotificationService windNotificationService;
    @Autowired
    private IWindNotifRecordService windRecordService;


    /**
     * 分页列表查询
     *
     * @param windNotification
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "wind_notification-分页列表查询")
    @ApiOperation(value = "wind_notification-分页列表查询", notes = "wind_notification-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(WindNotification windNotification,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<WindNotification> queryWrapper = QueryGenerator.initQueryWrapper(windNotification, req.getParameterMap());
        queryWrapper.orderByDesc("deadline");
        Page<WindNotification> page = new Page<WindNotification>(pageNo, pageSize);
        IPage<WindNotification> pageList = windNotificationService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param windNotification
     * @return
     */
    @AutoLog(value = "wind_notification-添加")
    @ApiOperation(value = "wind_notification-添加", notes = "wind_notification-添加")
    @RequiresPermissions("windNotification:add")
    @Transactional(rollbackFor = Exception.class)
    @PostMapping(value = "/add")
    public Result<?> add(@Validated @RequestBody WindNotification windNotification) {
        try {
            windNotificationService.addWindNotification(windNotification);
            return Result.OK("添加成功!");
        } catch (JeecgBootException e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 编辑
     *
     * @param windNotification
     * @return
     */
    @AutoLog(value = "wind_notification-编辑")
    @ApiOperation(value = "wind_notification-编辑", notes = "wind_notification-编辑")
    @RequiresPermissions("windNotification:edit")
    @PutMapping(value = "/edit")
    public Result<?> edit(@Validated @RequestBody WindNotification windNotification) {
        return windNotificationService.editWindNotification(windNotification);
    }

    /**
     * 编辑
     *
     * @param windNotification
     * @return
     */
    @AutoLog(value = "wind_notification-发布")
    @ApiOperation(value = "wind_notification-发布", notes = "wind_notification-发布")
    @PutMapping(value = "/release")
    public Result<?> release(@Validated @RequestBody WindNotification windNotification) {
        return windNotificationService.releaseWindNotification(windNotification);
    }

    /**
     * 编辑
     *
     * @param windNotification
     * @return
     */
    @AutoLog(value = "wind_notification-结束避风")
    @ApiOperation(value = "wind_notification-结束避风", notes = "wind_notification-结束避风")
    @PutMapping(value = "/setEnd")
    public Result<?> setEnd(@Validated @RequestBody WindNotification windNotification) {
        return windNotificationService.endWindNotification(windNotification);
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "wind_notification-通过id删除")
    @ApiOperation(value = "wind_notification-通过id删除", notes = "wind_notification-通过id删除")
    @RequiresPermissions("windNotification:delete")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        //先判断是否避风结束
        LambdaQueryWrapper<WindNotification> wrapper = new LambdaQueryWrapper<>();
        WindNotification windNotification = windNotificationService.getOne(wrapper.eq(WindNotification::getId, id), false);
        if (windNotification == null) {
            return Result.error(404, "未找到对应数据");
        }
        if (windNotification.getActualEndDate() != null) {
            return Result.error(404, "避风已结束,无法删除!");
        }
        windNotificationService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "wind_notification-批量删除")
    @ApiOperation(value = "wind_notification-批量删除", notes = "wind_notification-批量删除")
    @RequiresPermissions("windNotification:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.windNotificationService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "wind_notification-通过id查询")
    @ApiOperation(value = "wind_notification-通过id查询", notes = "wind_notification-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        WindNotification windNotification = windNotificationService.getById(id);
        if (windNotification == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(windNotification);
    }

    /**
     * 避风监控
     *
     * @return
     */
    @AutoLog(value = "wind_notification-避风监控")
    @ApiOperation(value = "wind_notification-避风监控", notes = "wind_notification-避风监控")
    @GetMapping(value = "/monitor")
    public Result<?> monitor() {
        return windNotificationService.getDetail();
    }

    /**
     * 导出excel
     *
     * @param request
     * @param windNotification
     */
    @RequiresPermissions("windNotification:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WindNotification windNotification) {
        return super.exportXls(request, windNotification, WindNotification.class, "wind_notification");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("windNotification:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WindNotification.class);
    }


    @ApiOperation(value = "wind_notification-获取最近一个月的通知", notes = "wind_notification-获取最近一个月的通知")
    @GetMapping(value = "/getRecentNotify")
    public Result<?> getRecentNotify() {
        Map<String, List<WindNotification>> map = windNotificationService.getRecentNotify();
        return Result.OK(map);
    }

}
