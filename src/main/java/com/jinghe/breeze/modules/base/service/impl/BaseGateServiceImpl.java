package com.jinghe.breeze.modules.base.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghe.breeze.modules.base.entity.BaseGate;
import com.jinghe.breeze.modules.base.entity.BaseGateEquipment;
import com.jinghe.breeze.modules.base.mapper.BaseGateEquipmentMapper;
import com.jinghe.breeze.modules.base.mapper.BaseGateMapper;
import com.jinghe.breeze.modules.base.service.IBaseGateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: base_gate
 * @Author: jeecg-boot
 * @Date: 2024-08-06
 * @Version: V1.0
 */
@Service
public class BaseGateServiceImpl extends ServiceImpl<BaseGateMapper, BaseGate> implements IBaseGateService {

    @Autowired
    private BaseGateMapper baseGateMapper;
    @Autowired
    private BaseGateEquipmentMapper baseGateEquipmentMapper;

    @Override
    @Transactional
    public void saveMain(BaseGate baseGate, List<BaseGateEquipment> baseGateEquipmentList) {
        baseGateMapper.insert(baseGate);
        if (baseGateEquipmentList != null && baseGateEquipmentList.size() > 0) {
            for (BaseGateEquipment entity : baseGateEquipmentList) {
                //外键设置
                entity.setGateId(baseGate.getId());
                baseGateEquipmentMapper.insert(entity);
            }
        }
    }

    @Override
    @Transactional
    public void updateMain(BaseGate baseGate, List<BaseGateEquipment> baseGateEquipmentList) {
        baseGateMapper.updateById(baseGate);

        //1.先删除子表数据
        baseGateEquipmentMapper.deleteByMainId(baseGate.getId());

        //2.子表数据重新插入
        if (baseGateEquipmentList != null && baseGateEquipmentList.size() > 0) {
            for (BaseGateEquipment entity : baseGateEquipmentList) {
                //外键设置
                entity.setGateId(baseGate.getId());
                baseGateEquipmentMapper.insert(entity);
            }
        }
    }

    @Override
    @Transactional
    public void delMain(String id) {
        baseGateEquipmentMapper.deleteByMainId(id);
        baseGateMapper.deleteById(id);
    }

    @Override
    @Transactional
    public void delBatchMain(Collection<? extends Serializable> idList) {
        for (Serializable id : idList) {
            baseGateEquipmentMapper.deleteByMainId(id.toString());
            baseGateMapper.deleteById(id);
        }
    }

}
