package com.jinghe.breeze.modules.settlement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.modules.settlement.entity.ContractInfo;
import com.jinghe.breeze.modules.settlement.entity.ContractInfoDetail;
import com.jinghe.breeze.modules.settlement.entity.Settlement;
import com.jinghe.breeze.modules.settlement.mapper.SettlementMapper;
import com.jinghe.breeze.modules.settlement.service.IContractInfoDetailService;
import com.jinghe.breeze.modules.settlement.service.IContractInfoService;
import com.jinghe.breeze.modules.settlement.service.ISettlementService;
import jodd.util.StringUtil;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: settlement
 * @Author: jeecg-boot
 * @Date: 2024-06-03
 * @Version: V1.0
 */
@Service
public class SettlementServiceImpl extends ServiceImpl<SettlementMapper, Settlement> implements ISettlementService {
    @Autowired
    private IContractInfoDetailService contractInfoDetailService;

    @Autowired
    private ISettlementService settlementService;

    @Autowired
    private IContractInfoService contractInfoService;

    /**
     * 创建树形结构,layer低于maxLayer的不再构建
     *
     * @param detailList
     * @param maxLayer
     * @return
     */

    /**
     * 只返回2层信息.
     *
     * @param contractId
     * @return
     */
    public List<ContractInfoDetail> getTreeData(String contractId) {
        List<ContractInfoDetail> details = contractInfoDetailService.list(new LambdaQueryWrapper<ContractInfoDetail>()
                .eq(ContractInfoDetail::getContractId, contractId));

        List<ContractInfoDetail> retData = new ArrayList<>();
        for (ContractInfoDetail detail : details) {
            if (detail.getLayer() == 0) {
                retData.add(detail);
                for (ContractInfoDetail subDetail : details) {
                    if (subDetail.getLayer() == 1 && subDetail.getCode().startsWith(detail.getCode())) {
                        if (detail.getChildren() == null) {
                            detail.setChildren(new ArrayList<>());
                        }
                        detail.getChildren().add(subDetail);
                    }
                }
            }
        }
        return retData;
    }

    /**
     * 向Settlement对象列表中添加合同信息。
     *
     * @param settleList 需要添加合同信息的Settlement对象列表
     */
    @Override
    public void addContractInfomation(List<Settlement> settleList) {
        List<String> batchList = settleList.stream().map(Settlement::getBatch).distinct().collect(Collectors.toList());
        List<String> contractIdList = settleList.stream().map(Settlement::getContractId).distinct()
                .collect(Collectors.toList());
        if (batchList.isEmpty() || contractIdList.isEmpty()) {
            return;
        }
        final List<ContractInfo> contractInfos = contractInfoService
                .list(new LambdaQueryWrapper<ContractInfo>().in(ContractInfo::getId, contractIdList));
        final List<Settlement> settlements = baseMapper.selectList(new LambdaQueryWrapper<Settlement>()
                .in(Settlement::getBatch, batchList));
        settleList.forEach(
                settle -> {
                    String contractId = settle.getContractId();
                    String batch = settle.getBatch();
                    ContractInfo contractInfo = contractInfos.stream().filter(p -> p.getId().equals(contractId))
                            .findFirst().orElse(null);
                    BigDecimal settledMoney = settlements.stream().filter(item -> item.getBatch().equals(batch))
                            .map(Settlement::getCurrentSettlement).reduce(BigDecimal.ZERO, BigDecimal::add);
                    settle.setBatchTotal(settledMoney);
                    settle.setContractInfo(contractInfo);
                });
    }

    /**
     * 根据自身的code,过滤出自己的子级的结算记录中的code列表
     * 返回code列表
     *
     * @param settleList
     * @param code
     */
    private List<String> filterSettlementListByCode(List<Settlement> settleList, String code) {
        int layer = code.split("\\.").length;
        List<String> codes = settleList.stream()
                .map(Settlement::getCode)
                .filter(itemCode -> itemCode.startsWith(code) && itemCode.split("\\.").length >= layer + 1)
                .map(itemCode -> {
                    List<String> arr = Arrays.asList(itemCode.split("\\."));
                    // 用.连接前layer+1个元素
                    return String.join(".", arr.subList(0, layer + 1));
                }).distinct()
                .collect(Collectors.toList());
        return codes;
    }

    public List<ContractInfoDetail> getChild(String code, String id) {
        boolean isRoot = contractInfoService.getById(id) != null; // 是否是合同
        Map<String, Settlement> map = new HashMap<>();
        List<String> codes = new ArrayList<>();// 存放下一级的code的列表
        String tmpContractId = "";
        LambdaQueryWrapper<Settlement> settleWrapper = new LambdaQueryWrapper<Settlement>();
        if (isRoot) {
            settleWrapper.eq(Settlement::getContractId, id);
            tmpContractId = id;
        } else {
            tmpContractId = contractInfoDetailService.getById(id).getContractId();
            settleWrapper.eq(Settlement::getContractId, tmpContractId);
        }
        final String contractId = tmpContractId;
        if (!StringUtil.isEmpty(code)) {
            settleWrapper.like(Settlement::getCode, code);
        } // 先按条件获取所有的结算记录 再将根据结算记录的code获取到详单的根code
        List<Settlement> settlementList = settlementService.list(settleWrapper);
        if (isRoot) {
            // 查询合同id
            settlementList.forEach(item -> {
                String parentCode = item.getCode().split("\\.")[0];
                if (!map.containsKey(parentCode)) {
                    map.put(parentCode, item);
                }
            });
            codes = new ArrayList<>(map.keySet());
            // 对每个parentCode 查询 根据详单code 和 合同id 详单
        } else {
            String detailCode = contractInfoDetailService.getById(id).getCode();
            codes = filterSettlementListByCode(settlementList, detailCode);
        }
        List<ContractInfoDetail> contractInfoDetails = codes.stream().map(code1 -> {
            return contractInfoDetailService.getOne(new LambdaQueryWrapper<ContractInfoDetail>()
                    .eq(ContractInfoDetail::getCode, code1)
                    .eq(ContractInfoDetail::getContractId, contractId), false);
        }).filter(Objects::nonNull)
                .collect(Collectors.toList());
        contractInfoDetailService.setSettledMoney(contractInfoDetails);
        return contractInfoDetails;
    }

    /**
     * 获取指定batch相关的的所有详单的信息
     *
     * @param contractId 不为null
     * @param batch      不为null
     * @return
     */
    public List<ContractInfoDetail> getBatchSettlementInfo(String contractId, String batch) {
        List<Settlement> settlementList = baseMapper.selectList(new LambdaQueryWrapper<Settlement>()
                .eq(Settlement::getContractId, contractId).eq(Settlement::getBatch, batch));
        List<String> ids = settlementList.stream().map(Settlement::getContractInfoDetailId)
                .collect(Collectors.toList());
        if (ids.size() == 0) {
            return new ArrayList<>();
        }
        List<ContractInfoDetail> detailList = contractInfoDetailService
                .list(new LambdaQueryWrapper<ContractInfoDetail>()
                        .in(ContractInfoDetail::getId, ids));
        detailList.forEach(detail -> {
            Settlement settlement = settlementList.stream()
                    .filter(item -> item.getContractInfoDetailId().equals(detail.getId())).findFirst().get();
            detail.setSettlementId(settlement.getId());
            detail.setCurrentSettlement(settlement.getCurrentSettlement());
        });
        return detailList;
    }

    /**
     * 编辑或者新增结算
     *
     * @param settlementList
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveInfo(List<Settlement> settlementList, String batch, String contractId) {
        if (settlementList.isEmpty()) {
            return;
        }
        String file = settlementList.get(0).getFile();
        if (file == null) {
            file = "";
        }
        if (StringUtil.isEmpty(contractId)) {
            throw new JeecgBootException("合同id不能为空");
        }
        if (StringUtil.isEmpty(batch)) {// 新增模式
            String newBatch = UUID.randomUUID().toString();
            for (Settlement item : settlementList) {
                item.setFile(file);
                item.setBatch(newBatch);
            }
            if (settlementList.size() > 0) {
                super.saveBatch(settlementList);
            }
            return;
        }
        List<Settlement> oldList = baseMapper.selectList(new LambdaQueryWrapper<Settlement>()
                .eq(Settlement::getContractId, contractId)
                .eq(Settlement::getBatch, batch));
        for (Settlement item : settlementList) {
            item.setFile(file);
        }
        super.updateBatchById(oldList);
        // 对比settlementList 和 oldList 找出需要删除,修改 新增的记录
        List<Settlement> delList = new ArrayList<>();
        List<Settlement> addList = new ArrayList<>();
        List<Settlement> updateList = new ArrayList<>();
        List<String> detailIdList = new ArrayList<>();
        settlementList.forEach(item -> {
            detailIdList.add(item.getContractInfoDetailId());
            if (oldList.stream().anyMatch(old -> old.getId().equals(item.getId()))) {
                updateList.add(item);
            } else {
                addList.add(item);
            }
        });
        oldList.forEach(item -> {
            if (!settlementList.stream().anyMatch(old -> old.getId().equals(item.getId()))) {
                delList.add(item);
                detailIdList.add(item.getContractInfoDetailId());
            }
        });
        if (delList.size() > 0) {
            settlementService.removeByIds(delList.stream().map(Settlement::getId).collect(Collectors.toList()));
        }
        if (addList.size() > 0) {
            settlementService.saveBatch(addList);
        }
        if (updateList.size() > 0) {
            settlementService.updateBatchById(updateList);
        }
        if (detailIdList.size() > 0) {
            List<ContractInfoDetail> details = contractInfoDetailService.listByIds(detailIdList);
            contractInfoDetailService.setSettledInfo(details);// 更新
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void addSettlement(List<Settlement> settlements) {
        String batch = UUID.randomUUID().toString();// 生成UUID
        List<ContractInfoDetail> details = new ArrayList<>();
        for (Settlement settlement : settlements) {
            String detailId = settlement.getContractInfoDetailId();
            ContractInfoDetail detail = contractInfoDetailService.getById(detailId);
            if (Objects.equals(detail.getHasChild(), 1)) {
                throw new JeecgBootException("不能提交有子项的清单!");
            }
            details.add(detail);
            settlement.setBatch(batch);
        }
        settlementService.saveBatch(settlements);
        contractInfoDetailService.setSettledInfo(details); // 更新该detail结算的总和
    }


}
