package com.jinghe.breeze.modules.ship.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghe.breeze.modules.ship.entity.ShipAlarm;
import org.jeecg.common.api.vo.Result;

/**
 * @Description: ship_alarm
 * @Author: jeecg-boot
 * @Date:   2024-04-23
 * @Version: V1.0
 */
public interface IShipAlarmService extends IService<ShipAlarm> {

    Result<?> queryStatistics(ShipAlarm shipAlarm);

    Result<?> queryAlarm();
}
