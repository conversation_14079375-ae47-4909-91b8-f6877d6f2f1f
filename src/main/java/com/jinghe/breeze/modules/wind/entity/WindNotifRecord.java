package com.jinghe.breeze.modules.wind.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.*;

/**
 * @Description: wind_notif_record
 * @Author: jeecg-boot
 * @Date: 2024-05-23
 * @Version: V1.0
 */
@Data
@TableName("wind_notif_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "wind_notif_record对象", description = "wind_notif_record")
public class WindNotifRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)

    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    /**
     * 创建人
     */

    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
    /**
     * 更新人
     */

    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
    /**
     * 所属部门
     */

    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
    /**
     * 删除标识
     */
    @Excel(name = "删除标识", width = 15)

    @ApiModelProperty(value = "删除标识")
    private java.lang.Integer delFlag;
    /**
     * notificationId
     */
    @Excel(name = "notificationId", width = 15)
    @NotNull(message = "notificationId不能为空!")

    @ApiModelProperty(value = "notificationId")
    private java.lang.String notificationId;
    /**
     * userId
     */
    @Excel(name = "userId", width = 15)
    @NotNull(message = "userId不能为空!")

    @ApiModelProperty(value = "userId")
    private java.lang.String userId;

    @ApiModelProperty(value = "state")
    private Integer state;

    @TableField(exist = false)
    private WindNotification windNotification;

    @TableField(exist = false)
    private WindReport windReport;
}
