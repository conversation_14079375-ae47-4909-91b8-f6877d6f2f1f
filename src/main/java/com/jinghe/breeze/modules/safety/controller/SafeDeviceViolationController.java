package com.jinghe.breeze.modules.safety.controller;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.jinghe.breeze.modules.safety.entity.SafeDeviceViolation;
import com.jinghe.breeze.modules.safety.service.ISafeDeviceViolationService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 人员行为安全预警
 * @Author: jeecg-boot
 * @Date:   2025-06-23
 * @Version: V1.0
 */
@Api(tags="人员行为安全预警")
@RestController
@RequestMapping("/safety/safeDeviceViolation")
@Slf4j
public class SafeDeviceViolationController extends JeecgController<SafeDeviceViolation, ISafeDeviceViolationService> {
	@Autowired
	private ISafeDeviceViolationService safeDeviceViolationService;
	
	/**
	 * 分页列表查询
	 *
	 * @param safeDeviceViolation
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "人员行为安全预警-分页列表查询")
	@ApiOperation(value="人员行为安全预警-分页列表查询", notes="人员行为安全预警-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(SafeDeviceViolation safeDeviceViolation,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<SafeDeviceViolation> queryWrapper = QueryGenerator.initQueryWrapper(safeDeviceViolation, req.getParameterMap());
		if (!ObjectUtils.isEmpty(safeDeviceViolation.getStartTime())){
			queryWrapper.between("violation_time",safeDeviceViolation.getStartTime(),safeDeviceViolation.getEndTime());
		}

		Page<SafeDeviceViolation> page = new Page<SafeDeviceViolation>(pageNo, pageSize);
		IPage<SafeDeviceViolation> pageList = safeDeviceViolationService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param safeDeviceViolation
	 * @return
	 */
	@AutoLog(value = "人员行为安全预警-添加")
	@ApiOperation(value="人员行为安全预警-添加", notes="人员行为安全预警-添加")
	@RequiresPermissions("safeDeviceViolation:add")
	@PostMapping(value = "/add")
	public Result<?> add(@Validated @RequestBody SafeDeviceViolation safeDeviceViolation) {
		safeDeviceViolationService.save(safeDeviceViolation);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param safeDeviceViolation
	 * @return
	 */
	@AutoLog(value = "人员行为安全预警-编辑")
	@ApiOperation(value="人员行为安全预警-编辑", notes="人员行为安全预警-编辑")
	@RequiresPermissions("safeDeviceViolation:edit")
	@PutMapping(value = "/edit")
	public Result<?> edit(@Validated @RequestBody SafeDeviceViolation safeDeviceViolation) {
		safeDeviceViolationService.updateById(safeDeviceViolation);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "人员行为安全预警-通过id删除")
	@ApiOperation(value="人员行为安全预警-通过id删除", notes="人员行为安全预警-通过id删除")
	@RequiresPermissions("safeDeviceViolation:delete")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		safeDeviceViolationService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "人员行为安全预警-批量删除")
	@ApiOperation(value="人员行为安全预警-批量删除", notes="人员行为安全预警-批量删除")
	@RequiresPermissions("safeDeviceViolation:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.safeDeviceViolationService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "人员行为安全预警-通过id查询")
	@ApiOperation(value="人员行为安全预警-通过id查询", notes="人员行为安全预警-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		SafeDeviceViolation safeDeviceViolation = safeDeviceViolationService.getById(id);
		if(safeDeviceViolation==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(safeDeviceViolation);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param safeDeviceViolation
    */
    @RequiresPermissions("safeDeviceViolation:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SafeDeviceViolation safeDeviceViolation) {
        return super.exportXls(request, safeDeviceViolation, SafeDeviceViolation.class, "人员行为安全预警");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("safeDeviceViolation:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SafeDeviceViolation.class);
    }

}
