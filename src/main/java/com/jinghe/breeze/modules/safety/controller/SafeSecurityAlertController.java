package com.jinghe.breeze.modules.safety.controller;
import com.jinghe.breeze.modules.safety.vo.SafeSecurityStaticsVo;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.jinghe.breeze.modules.safety.entity.SafeSecurityAlert;
import com.jinghe.breeze.modules.safety.service.ISafeSecurityAlertService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 智能安全识别预警
 * @Author: jeecg-boot
 * @Date:   2025-06-22
 * @Version: V1.0
 */
@Api(tags="智能安全识别预警")
@RestController
@RequestMapping("/safety/safeSecurityAlert")
@Slf4j
public class SafeSecurityAlertController extends JeecgController<SafeSecurityAlert, ISafeSecurityAlertService> {
	@Autowired
	private ISafeSecurityAlertService safeSecurityAlertService;
	
	/**
	 * 分页列表查询
	 *
	 * @param safeSecurityAlert
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "智能安全识别预警-分页列表查询")
	@ApiOperation(value="智能安全识别预警-分页列表查询", notes="智能安全识别预警-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(SafeSecurityAlert safeSecurityAlert,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<SafeSecurityAlert> queryWrapper = QueryGenerator.initQueryWrapper(safeSecurityAlert, req.getParameterMap());
		Page<SafeSecurityAlert> page = new Page<SafeSecurityAlert>(pageNo, pageSize);
		IPage<SafeSecurityAlert> pageList = safeSecurityAlertService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	 @AutoLog(value = "智能安全识别预警-统计")
	 @ApiOperation(value="智能安全识别预警-统计", notes="智能安全识别预警-统计")
	 @GetMapping(value = "/statics")
	 public Result<SafeSecurityStaticsVo> statics() {
		 return safeSecurityAlertService.statics();
	 }

	 /**
      *   添加
      *
      * @param safeSecurityAlert
      * @return
      */
	@AutoLog(value = "智能安全识别预警-添加")
	@ApiOperation(value="智能安全识别预警-添加", notes="智能安全识别预警-添加")
	@RequiresPermissions("safeSecurityAlert:add")
	@PostMapping(value = "/add")
	public Result<?> add(@Validated @RequestBody SafeSecurityAlert safeSecurityAlert) {
		safeSecurityAlertService.save(safeSecurityAlert);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param safeSecurityAlert
	 * @return
	 */
	@AutoLog(value = "智能安全识别预警-编辑")
	@ApiOperation(value="智能安全识别预警-编辑", notes="智能安全识别预警-编辑")
	@RequiresPermissions("safeSecurityAlert:edit")
	@PutMapping(value = "/edit")
	public Result<?> edit(@Validated @RequestBody SafeSecurityAlert safeSecurityAlert) {
		safeSecurityAlertService.updateById(safeSecurityAlert);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "智能安全识别预警-通过id删除")
	@ApiOperation(value="智能安全识别预警-通过id删除", notes="智能安全识别预警-通过id删除")
	@RequiresPermissions("safeSecurityAlert:delete")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		safeSecurityAlertService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "智能安全识别预警-批量删除")
	@ApiOperation(value="智能安全识别预警-批量删除", notes="智能安全识别预警-批量删除")
	@RequiresPermissions("safeSecurityAlert:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.safeSecurityAlertService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "智能安全识别预警-通过id查询")
	@ApiOperation(value="智能安全识别预警-通过id查询", notes="智能安全识别预警-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		SafeSecurityAlert safeSecurityAlert = safeSecurityAlertService.getById(id);
		if(safeSecurityAlert==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(safeSecurityAlert);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param safeSecurityAlert
    */
    @RequiresPermissions("safeSecurityAlert:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SafeSecurityAlert safeSecurityAlert) {
        return super.exportXls(request, safeSecurityAlert, SafeSecurityAlert.class, "智能安全识别预警");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("safeSecurityAlert:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SafeSecurityAlert.class);
    }

}
