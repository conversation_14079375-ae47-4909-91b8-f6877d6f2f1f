package com.jinghe.breeze.modules.dashboard.service;

import com.jinghe.breeze.modules.project.entity.ProjectCamera;
import com.jinghe.breeze.modules.sea.entity.ExtremeWeather;
import com.jinghe.breeze.modules.wind.entity.WindNotification;
import org.jeecg.common.api.vo.Result;

import java.util.List;
import java.util.Map;

public interface IDashboardService {
    WindNotification getWindNotification();

    Result<?> getShipAlarm();

    Result<?> getExtremeWeather();

    Result<?> getRealTimeWeather();

    Map<String, String> getProjectInfo();

    Map<String, String> getDictMap(String dictCode, String dicriptName);

    Map<String, Object> getConstructInfo();

    Map<String, Object> condition(String projectId);

    List<ProjectCamera> getCameraInfo();

    Map getWeather7days();

    Map<String, Object> getPersonInfo();

    Map<String, Object> getShipInfo();

    List<Map<String, Object>> getCurrentShipSailingInfo();

    List<String> bigScreenPictures();

    Map<String, List<Integer>> bigScreenWeekWork();

    Map<String, Integer> bigScreenDolphinInfo();

    Map<String, Integer> bigScreenWeatherInfo();

    List<ExtremeWeather> bigScreenWeatherAlarm();

    Map<String, Object> bigScreenSchedule();

    Map<String, Object> getHomeInfo();

    Result<?> schedule(String id);
}
