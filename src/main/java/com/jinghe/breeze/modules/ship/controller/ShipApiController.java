package com.jinghe.breeze.modules.ship.controller;

import com.jinghe.breeze.modules.ship.service.IShipApiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.ibatis.annotations.Param;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Api(tags = "船讯网")
@RestController
@RequestMapping("/ship/api")
public class ShipApiController {
    @Autowired
    private IShipApiService iShipApiService;

//    @Autowired
//    private ShipService shipService;

    @AutoLog(value = "区域船舶查询")
    @ApiOperation(value = "区域船舶", notes = "ship_info-分页列表查询")
    @GetMapping(value = "/GetAreaShip")
    public Result<?> GetAreaShip(@Param("xy") String xy) {
        return iShipApiService.GetAreaShip(xy);
    }

//    @PostMapping("/getShipsByMmsi")
//    public Result<?> getShip(@RequestBody Ship ship) {
//        String mmsi = ship.getMmsi();
//        List<Ship> ships = shipService.findByMmsi(mmsi);
//        return Result.OK(ships);
//    }
//
//    @PostMapping("/searchShips")
//    public Result<?> searchShips(@RequestBody ShipSearchRequest shipSearchRequest) {
//        List<Ship> ships = shipService.searchShips(shipSearchRequest.getMmsi(), shipSearchRequest.getStartTimestamp(), shipSearchRequest.getEndTimestamp());
//        return Result.OK(ships);
//    }

    private static class ShipSearchRequest {
        private String mmsi;
        private int startTimestamp;
        private int endTimestamp;

        // Getters and setters for mmsi, startTimestamp, and endTimestamp

        public String getMmsi() {
            return mmsi;
        }

        public void setMmsi(String mmsi) {
            this.mmsi = mmsi;
        }

        public int getStartTimestamp() {
            return startTimestamp;
        }

        public void setStartTimestamp(int startTimestamp) {
            this.startTimestamp = startTimestamp;
        }

        public int getEndTimestamp() {
            return endTimestamp;
        }

        public void setEndTimestamp(int endTimestamp) {
            this.endTimestamp = endTimestamp;
        }
    }
}
