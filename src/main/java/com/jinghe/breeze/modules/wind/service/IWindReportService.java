package com.jinghe.breeze.modules.wind.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinghe.breeze.modules.wind.entity.WindNotification;
import com.jinghe.breeze.modules.wind.entity.WindReport;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * @Description: wind_report
 * @Author: jeecg-boot
 * @Date: 2024-05-24
 * @Version: V1.0
 */
public interface IWindReportService extends IService<WindReport> {

    void removeReportAndDetails(String id);

    Page<WindReport> getReportList(WindReport windReport, String name, Integer pageNo, Integer pageSize);

    Map<String, List<WindNotification>> getRecentReport();

    void report(WindReport windReport, String userId);

    void editReport(WindReport windReport);
}
