package com.jinghe.breeze.modules.project.controller;
import com.jinghe.breeze.modules.project.entity.vo.ConstructionPlanProcessVo;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.jinghe.breeze.modules.project.entity.ConstructionPlanProcess;
import com.jinghe.breeze.modules.project.service.IConstructionPlanProcessService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 关联工序
 * @Author: jeecg-boot
 * @Date:   2024-10-10
 * @Version: V1.0
 */
@Api(tags="关联工序")
@RestController
@RequestMapping("/construction/constructionPlanProcess")
@Slf4j
public class ConstructionPlanProcessController extends JeecgController<ConstructionPlanProcess, IConstructionPlanProcessService> {
	@Autowired
	private IConstructionPlanProcessService constructionPlanProcessService;
	
	/**
	 * 分页列表查询
	 *
	 * @param constructionPlanProcess
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "关联工序-分页列表查询")
	@ApiOperation(value="关联工序-分页列表查询", notes="关联工序-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(ConstructionPlanProcess constructionPlanProcess,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<ConstructionPlanProcess> queryWrapper = QueryGenerator.initQueryWrapper(constructionPlanProcess, req.getParameterMap());
		Page<ConstructionPlanProcess> page = new Page<ConstructionPlanProcess>(pageNo, pageSize);
		IPage<ConstructionPlanProcess> pageList = constructionPlanProcessService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param constructionPlanProcess
	 * @return
	 */
	@AutoLog(value = "关联工序-添加")
	@ApiOperation(value="关联工序-添加", notes="关联工序-添加")
	@RequiresPermissions("constructionPlanProcess:add")
	@PostMapping(value = "/add")
	public Result<?> add(@Validated @RequestBody ConstructionPlanProcess constructionPlanProcess) {
		constructionPlanProcessService.save(constructionPlanProcess);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param constructionPlanProcess
	 * @return
	 */
	@AutoLog(value = "关联工序-编辑")
	@ApiOperation(value="关联工序-编辑", notes="关联工序-编辑")
	@RequiresPermissions("constructionPlanProcess:edit")
	@PutMapping(value = "/edit")
	public Result<?> edit(@Validated @RequestBody ConstructionPlanProcess constructionPlanProcess) {
		constructionPlanProcessService.updateById(constructionPlanProcess);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "关联工序-通过id删除")
	@ApiOperation(value="关联工序-通过id删除", notes="关联工序-通过id删除")
	@RequiresPermissions("constructionPlanProcess:delete")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		constructionPlanProcessService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "关联工序-批量删除")
	@ApiOperation(value="关联工序-批量删除", notes="关联工序-批量删除")
	@RequiresPermissions("constructionPlanProcess:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.constructionPlanProcessService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "关联工序-通过id查询")
	@ApiOperation(value="关联工序-通过id查询", notes="关联工序-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		ConstructionPlanProcess constructionPlanProcess = constructionPlanProcessService.getById(id);
		if(constructionPlanProcess==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(constructionPlanProcess);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param constructionPlanProcess
    */
    @RequiresPermissions("constructionPlanProcess:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ConstructionPlanProcess constructionPlanProcess) {
        return super.exportXls(request, constructionPlanProcess, ConstructionPlanProcess.class, "关联工序");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("constructionPlanProcess:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ConstructionPlanProcess.class);
    }

	 @AutoLog(value = "关联工序-左侧树")
	 @ApiOperation(value="关联工序-左侧树", notes="关联工序-左侧树")
//	 @RequiresPermissions("constructionPlanProcess:add")
	 @GetMapping(value = "/leftTree")
	 public Result<?> leftTree() {
		 return constructionPlanProcessService.leftTree();
	 }

	 @AutoLog(value = "关联工序-右侧数据")
	 @ApiOperation(value="关联工序-右侧数据", notes="关联工序-右侧数据")
//	 @RequiresPermissions("constructionPlanProcess:add")
	 @GetMapping(value = "/getProcessByPlan")
	 public Result<?> getProcessByPlan(@RequestParam(name="id",required=true) String id,
									   @RequestParam(name="planCode",required=false) String planCode,
									   @RequestParam(name="version",required=false) Integer version) {
		 return constructionPlanProcessService.getProcessByPlan(id,planCode,version);
	 }

	 @AutoLog(value = "关联工序-保存")
	 @ApiOperation(value="关联工序-保存", notes="关联工序-保存")
//	 @RequiresPermissions("constructionPlanProcess:add")
	 @PostMapping(value = "/saveProcessPlan")
	 public Result<?> saveProcessPlan(@RequestBody ConstructionPlanProcessVo constructionPlanProcessVo) {
		 return constructionPlanProcessService.saveProcessPlan(constructionPlanProcessVo);
	 }

}
