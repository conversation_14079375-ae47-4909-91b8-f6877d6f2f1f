package com.jinghe.breeze.modules.wind.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinghe.breeze.common.constants.enums.WindNotifyRecordEnum;
import com.jinghe.breeze.modules.person.entity.PersonInfo;
import com.jinghe.breeze.modules.person.service.IPersonInfoService;
import com.jinghe.breeze.modules.project.entity.ProjectUnit;
import com.jinghe.breeze.modules.project.service.IProjectUnitService;
import com.jinghe.breeze.modules.wind.entity.WindNotifRecord;
import com.jinghe.breeze.modules.wind.entity.WindNotification;
import com.jinghe.breeze.modules.wind.entity.WindReport;
import com.jinghe.breeze.modules.wind.entity.WindReportDetail;
import com.jinghe.breeze.modules.wind.mapper.WindReportMapper;
import com.jinghe.breeze.modules.wind.service.IWindNotifRecordService;
import com.jinghe.breeze.modules.wind.service.IWindNotificationService;
import com.jinghe.breeze.modules.wind.service.IWindReportDetailService;
import com.jinghe.breeze.modules.wind.service.IWindReportService;
import jodd.util.StringUtil;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: wind_report
 * @Author: jeecg-boot
 * @Date: 2024-05-24
 * @Version: V1.0
 */
@Service
public class WindReportServiceImpl extends ServiceImpl<WindReportMapper, WindReport> implements IWindReportService {
    @Autowired
    private IWindReportDetailService windReportDetailService;
    @Autowired
    private IWindNotificationService windNotificationService;
    @Autowired
    IWindNotifRecordService windRecordService;

    @Autowired
    private IPersonInfoService personInfoService;

    @Autowired
    private IProjectUnitService projectUnitService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editReport(WindReport windReport) {
        String id = windReport.getId();
        if (StringUtil.isEmpty(id)) {
            throw new JeecgBootException("参数错误!");
        }
        WindReport entity = super.getById(id);
        if (entity == null) {
            throw new JeecgBootException("未找到对应数据");
        }
        WindNotification notification = windNotificationService.getById(windReport.getNotifyId());
        if (notification == null) {
            throw new JeecgBootException("未找到对应通知");
        }
        if (Objects.equals(notification.getState(), 2)) {
            throw new JeecgBootException("避风已结束,无法修改报备");
        }
        if (notification.getDeadline().compareTo(new Date(System.currentTimeMillis())) < 0) {
            throw new JeecgBootException("该通知已超过截止日期,无法修改报备");
        }
        windReport.setWindNotification(notification);
        super.updateById(windReport);
        List<WindReportDetail> detailList = windReport.getDetails();
        List<WindReportDetail> toSaveList = new ArrayList<>();
        List<WindReportDetail> toUpdateList = new ArrayList<>();
        for (WindReportDetail detail : detailList) {
            detail.setReportId(id);
            if (detail.getId() == null) {
                toSaveList.add(detail);
            } else {
                toUpdateList.add(detail);
            }
        }
        if (toSaveList.size() > 0) {
            windReportDetailService.saveBatch(toSaveList);
        }
        if (toUpdateList.size() > 0) {
            windReportDetailService.updateBatchById(toUpdateList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void removeReportAndDetails(String id) {
        WindReport report = super.getById(id);
        if (report == null) {
            throw new JeecgBootException("未找到对应记录");
        }
        windReportDetailService.list(new LambdaQueryWrapper<WindReportDetail>()
                .eq(WindReportDetail::getReportId, id)).forEach(windReportDetail -> {
            windReportDetailService.removeById(windReportDetail.getId());
        });

        windRecordService.update(new LambdaUpdateWrapper<WindNotifRecord>()
                .eq(WindNotifRecord::getNotificationId, report.getNotifyId())
                .eq(WindNotifRecord::getUserId, report.getUserId())
                .set(WindNotifRecord::getState, WindNotifyRecordEnum.NOTDONE.getCode()));
        this.removeById(id);
    }

    public Page<WindReport> getReportList(WindReport windReport,
                                          String name,
                                          Integer pageNo,
                                          Integer pageSize) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Page<WindReport> page = new Page<>(pageNo, pageSize);
        List<WindReport> reportList = new ArrayList<>();
//                "total": 12,
//                "size": 10,
//                "current": 2,
//                "pages": 2

        int total = 0;
        int pages = 0;
        page.setCurrent(pageNo);
        page.setRecords(reportList);
        page.setTotal(total);
        page.setSize(pageSize);
        page.setPages(pages);
        //先获取包含name的记录id
        LambdaQueryWrapper<WindNotification> wrapper = new LambdaQueryWrapper<>();
        if (!StringUtils.isEmpty(name)) {
            wrapper.like(WindNotification::getName, name);
        }
        wrapper.select(WindNotification::getId);
        List<String> notificationIds = windNotificationService.list(wrapper).stream().map(WindNotification::getId).collect(Collectors.toList());
        if (notificationIds.size() == 0) {
            return page;
        }
        PersonInfo personInfo = personInfoService.getOne(new LambdaQueryWrapper<PersonInfo>()
                .eq(PersonInfo::getUserId, sysUser.getId()), false);
        if (personInfo == null) {
            return page;
        }
        String unitId = personInfo.getDepartment();
        ProjectUnit unit = projectUnitService
                .getOne(new LambdaQueryWrapper<ProjectUnit>().eq(ProjectUnit::getId, unitId), false);
        if (unit == null) {
            return page;
        }
        String unitName = unit.getName();
        if (unitName == null) {
            return page;
        }
        LambdaQueryWrapper<WindReport> reportWrapper = new LambdaQueryWrapper<>();
        reportWrapper.eq(WindReport::getUnitName, unitName).in(WindReport::getNotifyId, notificationIds);
        total = baseMapper.selectCount(reportWrapper);
        if (total == 0) {
            return page;
        }
        page.setTotal(total);
        pages = total % pageSize == 0 ? total / pageSize : total / pageSize + 1;
        page.setPages(pages);
        reportWrapper.last("limit " + (pageNo - 1) * pageSize + "," + pageSize);
        reportList = baseMapper.selectList(reportWrapper);
        notificationIds = reportList.stream().map(WindReport::getNotifyId).collect(Collectors.toList());
        if (notificationIds.isEmpty()) {
            return page;
        }
        List<String> reportIds = reportList.stream()
                .map(WindReport::getId)
                .collect(Collectors.toList());
        List<WindReportDetail> detailList = windReportDetailService.list(new LambdaQueryWrapper<WindReportDetail>()
                .in(WindReportDetail::getReportId, reportIds));

        List<WindNotification> notificationList = windNotificationService.list(new LambdaQueryWrapper<WindNotification>()
                .in(WindNotification::getId, notificationIds));
        reportList.forEach(report -> {
            report.setWindNotification(notificationList.stream()
                    .filter(notification -> notification.getId().equals(report.getNotifyId()))
                    .findFirst()
                    .orElse(null));

            report.setDetails(
                    detailList.stream()
                            .filter(detail -> detail.getReportId().equals(report.getId()))
                            .collect(Collectors.toList()));
        });
        page.setRecords(reportList);
        return page;
    }

    /**
     * app获取最近一个月的填报信息,已填报和未填报
     *
     * @return
     */
    public LinkedHashMap<String, List<WindNotification>> getRecentReport() {
        // 要求同单位的人报备过的也算报备过
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userId = sysUser.getId();
        List<WindNotifRecord> recordList = windRecordService.list(new LambdaQueryWrapper<WindNotifRecord>()// 发布记录
                .eq(WindNotifRecord::getUserId, userId)
                .ge(WindNotifRecord::getCreateTime, new Date(System.currentTimeMillis() - 30L * 24 * 3600 * 1000))
                .orderByDesc(WindNotifRecord::getCreateTime));
        List<String> notifyIdList = recordList.stream().map(WindNotifRecord::getNotificationId)
                .collect(Collectors.toList());
        LinkedHashMap<String, List<WindNotification>> map = new LinkedHashMap<>();
        if (notifyIdList.isEmpty()) {
            map.put("reportedList", new ArrayList<>());
            map.put("unReportedList", new ArrayList<>());
            return map;
        }
        List<WindNotification> notifyList = windNotificationService.listByIds(notifyIdList);
        List<String> reportedIdList = baseMapper.selectList(new LambdaQueryWrapper<WindReport>() // 已报备的列表
                        .eq(WindReport::getUserId, userId)
                        .in(WindReport::getNotifyId, notifyIdList)
                        .orderByDesc(WindReport::getDeadline)).stream().map(WindReport::getNotifyId)
                .collect(Collectors.toList());
        // 已报备的按报备时间升序
        List<WindNotification> reportedList = notifyList.stream()
                .filter(x -> reportedIdList.contains(x.getId()))
                .sorted(Comparator.comparing(WindNotification::getCreateTime))
                .collect(Collectors.toList());
        // 未报备的按截止时间升序
        List<WindNotification> unReportedList = notifyList.stream()
                .filter(x -> !reportedIdList.contains(x.getId()))
                .sorted(Comparator.comparing(WindNotification::getDeadline))
                .collect(Collectors.toList());
        map.put("reportedList", reportedList);
        map.put("unReportedList", unReportedList);
        return map;
    }

    @Transactional(rollbackFor = Exception.class)
    public void report(WindReport windReport, String userId) {
        // 需求是当填报完成时,将同单位的本条通知 均设为已做
        String notifyId = windReport.getNotifyId();
        if (StringUtil.isEmpty(notifyId) || StringUtil.isEmpty(userId)) {
            throw new JeecgBootException("未提供完整的参数");
        }
        WindNotifRecord record = windRecordService.getOne(new LambdaQueryWrapper<WindNotifRecord>()
                .eq(WindNotifRecord::getNotificationId, notifyId)
                .eq(WindNotifRecord::getUserId, userId), false);
        WindNotification notification = windNotificationService.getById(notifyId);
        if (notification == null) {
            throw new JeecgBootException("未找到对应的通知");
        }
        if (Objects.equals(notification.getState(), 2) || notification.getPlanEndDate().before(new Date(System.currentTimeMillis()))) {
            throw new JeecgBootException("避风已结束,无法报备");
        }
        if (notification.getDeadline().compareTo(new Date(System.currentTimeMillis())) < 0) {
            throw new JeecgBootException("该通知已超过截止日期,无法报备");
        }
        // 判断是否已经过期,或者结束
        if (record == null) {
            throw new JeecgBootException("未找到该通知的发布记录");
        }
        if (Objects.equals(record.getState(), WindNotifyRecordEnum.HASDONE.getCode())) {
            throw new JeecgBootException("该通知已由同单位其他人填报完成,无需重复填报");
        }
        windReport.setUserId(userId); // 新增的时候 ,该字段前端不传,后端自动获取
        baseMapper.insert(windReport);
        String id = windReport.getId();
        List<WindReportDetail> detailList = windReport.getDetails();
        for (WindReportDetail detail : detailList) {
            detail.setReportId(id);
        }
        windReportDetailService.saveBatch(detailList);
        // 获取自身所在单位的其他所有的PersonInfo
        PersonInfo myself = personInfoService.getOne(new LambdaQueryWrapper<PersonInfo>()
                .eq(PersonInfo::getUserId, userId), false);
        if (myself == null) {
            throw new JeecgBootException("没有找到您所在的单位");
        }
        String unitId = myself.getDepartment();
        List<PersonInfo> personInfoList = personInfoService.list(new LambdaQueryWrapper<PersonInfo>()
                .eq(PersonInfo::getDepartment, unitId));
        List<String> userIdList = personInfoList.stream().map(PersonInfo::getUserId).collect(Collectors.toList());
        // 获取该单位下的所有人员Id
        windRecordService.update(new LambdaUpdateWrapper<WindNotifRecord>()
                .eq(WindNotifRecord::getNotificationId, windReport.getNotifyId())
                .in(WindNotifRecord::getUserId, userIdList)
                .set(WindNotifRecord::getState, WindNotifyRecordEnum.HASDONE.getCode()));
    }
}