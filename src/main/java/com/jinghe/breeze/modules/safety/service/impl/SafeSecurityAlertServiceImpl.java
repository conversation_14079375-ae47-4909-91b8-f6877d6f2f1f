package com.jinghe.breeze.modules.safety.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.common.enums.DelFlagEnum;
import com.jinghe.breeze.common.enums.RiskLevelEnum;
import com.jinghe.breeze.modules.safety.entity.SafeSecurityAlert;
import com.jinghe.breeze.modules.safety.mapper.SafeSecurityAlertMapper;
import com.jinghe.breeze.modules.safety.service.ISafeSecurityAlertService;
import com.jinghe.breeze.modules.safety.vo.SafeSecurityStaticsVo;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 智能安全识别预警
 * @Author: jeecg-boot
 * @Date: 2025-06-22
 * @Version: V1.0
 */
@Service
public class SafeSecurityAlertServiceImpl extends ServiceImpl<SafeSecurityAlertMapper, SafeSecurityAlert> implements ISafeSecurityAlertService {
    public Result<SafeSecurityStaticsVo> statics() {
        LambdaQueryWrapper<SafeSecurityAlert> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SafeSecurityAlert::getDelFlag, DelFlagEnum.NORMAL.getType());
        int total = baseMapper.selectCount(queryWrapper);
        queryWrapper.eq(SafeSecurityAlert::getRiskLevel, RiskLevelEnum.HIGH_RISK.getType());
        int high = baseMapper.selectCount(queryWrapper);
        SafeSecurityStaticsVo vo = new SafeSecurityStaticsVo();
        vo.setTotal(total);
        vo.setHigh(high);
        return Result.OK(vo);
    }
}
