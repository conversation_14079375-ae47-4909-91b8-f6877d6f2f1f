package com.jinghe.breeze.modules.project.entity.vo;

import com.jinghe.breeze.modules.construction.entity.ConstructionLog;
import com.jinghe.breeze.modules.data.entity.DataCableInfo;
import com.jinghe.breeze.modules.data.entity.DataFanInfo;
import com.jinghe.breeze.modules.project.entity.ProjectPbs;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class PbsVo  {
    public PbsVo(ProjectPbs pbs){
        this.id = pbs.getId();
        this.createBy = pbs.getCreateBy();
        this.createTime = pbs.getCreateTime();
        this.updateBy = pbs.getUpdateBy();
        this.updateTime = pbs.getUpdateTime();
        this.delFlag = pbs.getDelFlag();
        this.sysOrgCode = pbs.getSysOrgCode();
        this.projectId = pbs.getProjectId();
        this.code = pbs.getCode();
        this.name = pbs.getName();
        this.pid  = pbs.getPid();
        this.sortNo = pbs.getSortNo();
        this.layer = pbs.getLayer();
        this.type = pbs.getType();
        this.hasChild = pbs.getHasChild();
        this.point = pbs.getPoint();
        this.specificationModel = pbs.getSpecificationModel();
        this.applicableProcess = pbs.getApplicableProcess();
        this.route=pbs.getRoute();
        this.picture = pbs.getPicture();
        this.fanSection = pbs.getFanSection();
        this.startSection = pbs.getStartSection();
        this.endSection = pbs.getEndSection();
        this.children = pbs.getChildren();

    }
    private DataCableInfo dataCableInfo;
    private DataFanInfo dataFanInfo;
    private String lastProcessIcon;
    private java.lang.String id;
    private java.lang.String createBy;
    private java.util.Date createTime;
    private java.lang.String updateBy;
    private java.util.Date updateTime;
    private java.lang.Integer delFlag;
    private java.lang.String sysOrgCode;
    private java.lang.String projectId;
    private java.lang.String code;
    private java.lang.String name;
    private java.lang.String pid;
    private java.lang.Integer sortNo;
    private java.lang.Integer layer;
    private java.lang.String type;
    private java.lang.Integer hasChild;
    private java.lang.String point;
    private java.lang.String specificationModel;

    private Integer pbsComplete;
    private int constructionStatus;

    private java.lang.String applicableProcess;

    private java.lang.String route;

    private java.lang.String picture;

    private java.lang.String fanSection;

    private java.lang.String startSection;

    private java.lang.String endSection;

    private List<ProjectPbs> children;

    /**
     * 已完成工序
     */
    private List<ConstructionLog> completedProcess;

    private Boolean isCompleted;

    public Boolean getIsCompleted() {
        return isCompleted;
    }
    public void setIsCompleted(Boolean isCompleted) {
        this.isCompleted = isCompleted;
    }
    public List<ProjectPbs> getChildren() {
        return children;
    }

    public void setChildren(List<ProjectPbs> children) {
        this.children = children;
    }

    public void addChild(ProjectPbs child) {
        if (this.children == null) {
            this.children = new ArrayList<>();
        }
        this.children.add(child);
    }
    public void setLastProcessIcon(String lastProcessIcon) {
        this.lastProcessIcon = lastProcessIcon;
    }

    public String getLastProcessIcon() {
        return lastProcessIcon;
    }
}
