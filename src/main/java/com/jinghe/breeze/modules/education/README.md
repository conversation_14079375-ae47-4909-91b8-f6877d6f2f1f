### 关系解释

这些表通过逻辑关联（而非外键约束）共同构成了一个完整的业务闭环：

1.  **题库核心 (`edu_question`, `edu_question_tags`)**:
    *   `edu_question` 是所有题目的源头。
    *   `edu_question_tags` 通过 `question_id` 与 `edu_question` 关联，为题目附加分类信息。

2.  **试卷生成与快照 (`edu_exam_paper`, `edu_exam_paper_detail`)**:
    *   当需要组卷时，会创建一条 `edu_exam_paper` 记录，其中 `rules` 字段记录了本次组卷的规则。
    *   系统根据规则从 `edu_question` 中抽取题目，并将每道题的完整信息（题干、选项、答案、分值等）作为一条新记录插入到 `edu_exam_paper_detail` 中，通过 `paper_id` 与 `edu_exam_paper` 关联。
    *   `edu_exam_paper_detail.question_id` 字段用于追溯该快照题目最初来源于 `edu_question` 中的哪条记录。
    *   **这一“快照”机制确保了即使题库中的原题目被修改或删除，已生成的试卷内容和历史考试记录也完全不受影响，保证了考试的公平性和历史数据的准确性。**

3.  **培训与考试安排 (`edu_train`, `edu_train_unit`)**:
    *   `edu_train` 定义一个培训活动，并通过 `paper_id` 明确指定了本次培训所使用的试卷（来自 `edu_exam_paper`）。
    *   `edu_train_unit` 通过 `train_id` 和 `unit_id` 将培训活动分配给一个或多个单位（`project_unit`）。

4.  **学员考试流程 (`edu_exam`, `edu_exam_detail`)**:
    *   当一个员工（`person_info`）参加一场培训（`edu_train`）的考试时，系统会为其创建一条 `edu_exam` 记录。这条记录通过 `person_id`, `train_id`, `paper_id` 将学员、培训和试卷三者锁定。
    *   学员答题时，其对**每一道题**的答案都作为一条记录插入 `edu_exam_detail` 表中，并通过 `exam_id` 与 `edu_exam` 关联。同时，它也通过 `paper_detail_id` 关联到试卷快照中的具体题目。
    *   提交试卷时，应用层逻辑会：
        1.  将所有答案写入 `edu_exam_detail`。
        2.  计算总分更新 `edu_exam.score` 和 `edu_exam.status`。
        3.  根据 `edu_exam_paper_detail.question_id` 找到原始题目，更新 `edu_question` 表中的 `answer_count` 和 `error_count` 计数器。