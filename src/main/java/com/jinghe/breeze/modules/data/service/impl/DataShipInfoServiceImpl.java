package com.jinghe.breeze.modules.data.service.impl;

import com.jinghe.breeze.modules.data.entity.DataShipInfo;
import com.jinghe.breeze.modules.data.mapper.DataShipInfoMapper;
import com.jinghe.breeze.modules.data.service.IDataShipInfoService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 船舶信息
 * @Author: jeecg-boot
 * @Date:   2024-04-17
 * @Version: V1.0
 */
@Service
public class DataShipInfoServiceImpl extends ServiceImpl<DataShipInfoMapper, DataShipInfo> implements IDataShipInfoService {

}
