package com.jinghe.breeze.modules.device.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinghe.breeze.modules.device.entity.DeviceInfo;
import com.jinghe.breeze.modules.device.entity.DeviceStar;
import com.jinghe.breeze.modules.device.service.IDeviceInfoService;
import com.jinghe.breeze.modules.device.service.IDeviceStarService;
import com.jinghe.breeze.modules.device.vo.BaseDevicePage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 关注的监控信息
 * @Author: jeecg-boot
 * @Date: 2024-08-14
 * @Version: V1.0
 */
@Api(tags = "关注的监控信息")
@RestController
@RequestMapping("/device/deviceStar")
@Slf4j
public class DeviceStarController extends JeecgController<DeviceStar, IDeviceStarService> {
    @Autowired
    private IDeviceStarService baseDeviceMonitorStarService;

    @Autowired
    private IDeviceInfoService baseDeviceService;

    /**
     * 分页列表查询
     *
     * @param baseDeviceMonitorStar
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "关注的监控信息-分页列表查询")
    @ApiOperation(value = "关注的监控信息-分页列表查询", notes = "关注的监控信息-分页列表查询")
    @GetMapping(value = "/page")
    public Result<?> queryPageList(DeviceStar baseDeviceMonitorStar,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<DeviceStar> queryWrapper = QueryGenerator.initQueryWrapper(baseDeviceMonitorStar, req.getParameterMap());
        Page<DeviceStar> page = new Page<DeviceStar>(pageNo, pageSize);
        IPage<DeviceStar> pageList = baseDeviceMonitorStarService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * @param baseDeviceMonitorStar
     * @param req
     * @return
     * @description 关注的监控信息-列表
     */
    @AutoLog(value = "关注的监控信息-列表")
    @ApiOperation(value = "关注的监控信息-列表", notes = "关注的监控信息-列表")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(DeviceStar baseDeviceMonitorStar, HttpServletRequest req) {
        try {
            QueryWrapper<DeviceStar> queryWrapper = new QueryWrapper<>();
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

            queryWrapper.eq("user_id", sysUser.getId());
            queryWrapper.eq("del_flag", '0');
            List<DeviceStar> list = baseDeviceMonitorStarService.list(queryWrapper);

            return Result.OK(list);
        } catch (Exception e) {
            // Log the error (optional)
            return Result.error("Error occurred: " + e.getMessage());
        }
    }


    /**
     * 添加
     *
     * @param baseDeviceMonitorStar
     * @return
     */
    @AutoLog(value = "关注的监控信息-添加")
    @ApiOperation(value = "关注的监控信息-添加", notes = "关注的监控信息-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody DeviceStar baseDeviceMonitorStar) {
        DeviceInfo deviceInfo = baseDeviceService.getById(baseDeviceMonitorStar.getDeviceId());
        baseDeviceMonitorStar.setName(deviceInfo.getName());
        baseDeviceMonitorStar.setEquipmentSerial(deviceInfo.getEquipmentSerial());
        baseDeviceMonitorStar.setInstallPosition(deviceInfo.getInstallPosition());
        baseDeviceMonitorStarService.save(baseDeviceMonitorStar);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param baseDeviceMonitorStar
     * @return
     */
    @AutoLog(value = "关注的监控信息-编辑")
    @ApiOperation(value = "关注的监控信息-编辑", notes = "关注的监控信息-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody DeviceStar baseDeviceMonitorStar) {
        baseDeviceMonitorStarService.updateById(baseDeviceMonitorStar);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "关注的监控信息-通过id删除")
    @ApiOperation(value = "关注的监控信息-通过id删除", notes = "关注的监控信息-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        DeviceStar deviceStar = baseDeviceMonitorStarService.getById(id);
        DeviceInfo deviceInfo = baseDeviceService.getById(deviceStar.getDeviceId());
        baseDeviceMonitorStarService.removeById(id);
        return Result.OK(deviceInfo);
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "关注的监控信息-批量删除")
    @ApiOperation(value = "关注的监控信息-批量删除", notes = "关注的监控信息-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.baseDeviceMonitorStarService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "关注的监控信息-通过id查询")
    @ApiOperation(value = "关注的监控信息-通过id查询", notes = "关注的监控信息-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        DeviceStar baseDeviceMonitorStar = baseDeviceMonitorStarService.getById(id);
        if (baseDeviceMonitorStar == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(baseDeviceMonitorStar);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param baseDeviceMonitorStar
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DeviceStar baseDeviceMonitorStar) {
        return super.exportXls(request, baseDeviceMonitorStar, DeviceStar.class, "关注的监控信息");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DeviceStar.class);
    }

    @AutoLog(value = "关注的监控信息-列表查询")
    @ApiOperation(value = "关注的监控信息-列表查询", notes = "关注的监控信息-列表查询")
    @GetMapping(value = "/allList/{userId}")
    public Result<?> queryPageList(@PathVariable("userId") String userId) {
        if (StrUtil.isEmpty(userId)) {
            return Result.error("参数不能为空！");
        }
        LambdaQueryWrapper<DeviceStar> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DeviceStar::getUserId, userId);
        List<DeviceStar> list = baseDeviceMonitorStarService.list(lambdaQueryWrapper);
        List<String> deviceIds = list.stream().map(DeviceStar::getDeviceId).collect(Collectors.toList());
        List<BaseDevicePage> result = baseDeviceService.getListByIds(deviceIds);
        return Result.OK(result);
    }


}
