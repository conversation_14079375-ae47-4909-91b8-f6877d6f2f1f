package com.jinghe.breeze.modules.staff.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinghe.breeze.common.thirdPart.CggcUtil;
import com.jinghe.breeze.modules.staff.entity.ProjectStaff;
import com.jinghe.breeze.modules.staff.entity.StaffAttendance;
import com.jinghe.breeze.modules.staff.entity.StaffSalary;
import com.jinghe.breeze.modules.staff.entity.SubProject;
import com.jinghe.breeze.modules.staff.service.IProjectStaffService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 人员
 * @Author: jeecg-boot
 * @Date: 2024-08-13
 * @Version: V1.0
 */
@Api(tags = "人员")
@RestController
@RequestMapping("/staff")
@Slf4j
public class StaffController extends JeecgController<ProjectStaff, IProjectStaffService> {
    @Autowired
    private IProjectStaffService iService;


    /**
     * 分包商列表
     *
     * @param req
     * @return
     */
    @AutoLog(value = "分包商列表")
    @ApiOperation(value = "分包商列表", notes = "分包商列表")
    @GetMapping(value = "/subInfo")
    public Result<?> queryPageList(HttpServletRequest req) {
        List<SubProject> subProjects = iService.subInfo();
        List<SubProject> result = new ArrayList<>();
        for (SubProject item : subProjects) {
            SubProject vo = new SubProject();
            vo.setName(item.getName());
            vo.setId(item.getId());
            result.add(vo);
        }
        return Result.OK(result);
    }


    /**
     * 人员分页列表查询
     *
     * @param staff
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "人员分页列表查询")
    @ApiOperation(value = "人员分页列表查询", notes = "人员分页列表查询")
    @GetMapping(value = "/staffPage")
    public Result<?> staffPageList(ProjectStaff staff,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        Page<ProjectStaff> pageList = iService.staffPage(pageNo, pageSize, staff.getSubName(), staff.getName(), staff.getIdCard());
        return Result.OK(pageList);
    }


    /**
     * 人员工资分页列表查询
     *
     * @param salary
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "人员工资分页列表查询")
    @ApiOperation(value = "人员工资分页列表查询", notes = "人员工资分页列表查询")
    @GetMapping(value = "/salaryPage")
    public Result<?> staffSalaryPageList(StaffSalary salary,
                                         @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                         @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                         HttpServletRequest req) {
        Page<StaffSalary> pageList = iService.salaryPage(pageNo, pageSize, salary.getSubcontractorName(), salary.getMonthIn(), salary.getUserName(), salary.getIdCard());
        return Result.OK(pageList);
    }

    /**
     * 人员考勤分页列表查询
     *
     * @param attendance
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "人员考勤分页列表查询")
    @ApiOperation(value = "人员考勤分页列表查询", notes = "人员考勤分页列表查询")
    @GetMapping(value = "/attendPage")
    public Result<?> staffAttendPageList(StaffAttendance attendance,
                                         @RequestParam(name = "start", required = false) String start,
                                         @RequestParam(name = "end", required = false) String end,
                                         @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                         @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                         HttpServletRequest req) {
        Page<StaffAttendance> pageList = iService.attendancePage(pageNo, pageSize, attendance.getSubcontractorName(), attendance.getName(), start, end, attendance.getIdentityCard());
        return Result.OK(pageList);
    }

    @AutoLog(value = "人员合同查询")
    @ApiOperation(value = "人员合同查询", notes = "人员合同查询")
    @GetMapping(value = "/staffContract")
    public String staffContract(@RequestParam(name = "staffId") String staffId,
                                HttpServletRequest req) {
        return iService.staffContract(staffId);
    }

    /**
     * 人员信息同步
     *
     * @param req
     * @return
     */
    @AutoLog(value = "人员同步")
    @ApiOperation(value = "人员同步", notes = "人员同步")
    @GetMapping(value = "/staffSync")
    public Result<?> staffSync(HttpServletRequest req) {
        if (!CggcUtil.STAFF_SYNCING) {
            CggcUtil.STAFF_SYNCING = true;
            System.out.println("***开始同步员工");
            iService.staffSync();
        }
        return Result.OK();
    }

    /**
     * 人员考勤同步
     *
     * @param req
     * @return
     */
    @AutoLog(value = "人员考勤同步")
    @ApiOperation(value = "人员考勤同步", notes = "人员考勤同步")
    @GetMapping(value = "/attendSync")
    public Result<?> attendSync(HttpServletRequest req) {
        if (!CggcUtil.ATTEND_SYNCING) {
            CggcUtil.ATTEND_SYNCING = true;
            System.out.println("***开始同步考勤");
            iService.attendSync();
        }
        return Result.OK();
    }

    /**
     * 人员工资同步
     *
     * @param req
     * @return
     */
    @AutoLog(value = "人员工资同步")
    @ApiOperation(value = "人员工资同步", notes = "人员工资同步")
    @GetMapping(value = "/salarySync")
    public Result<?> salarySync(HttpServletRequest req, @RequestParam(name = "month") String month) {
        if (!CggcUtil.SALARY_SYNCING) {
            CggcUtil.SALARY_SYNCING = true;
            System.out.println("***开始同步工资");
            iService.salarySync(month);
        }
        return Result.OK();
    }

}
