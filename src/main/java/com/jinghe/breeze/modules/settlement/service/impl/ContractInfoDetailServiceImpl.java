package com.jinghe.breeze.modules.settlement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.modules.settlement.entity.ContractInfoDetail;
import com.jinghe.breeze.modules.settlement.entity.Settlement;
import com.jinghe.breeze.modules.settlement.mapper.ContractInfoDetailMapper;
import com.jinghe.breeze.modules.settlement.service.IContractInfoDetailService;
import com.jinghe.breeze.modules.settlement.service.IContractInfoService;
import com.jinghe.breeze.modules.settlement.service.ISettlementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: contract_info_detail
 * @Author: jeecg-boot
 * @Date: 2024-05-31
 * @Version: V1.0
 */
@Service
public class ContractInfoDetailServiceImpl extends ServiceImpl<ContractInfoDetailMapper, ContractInfoDetail> implements IContractInfoDetailService {
    @Autowired
    private IContractInfoService contractInfoService;
    @Autowired
    private ISettlementService settlementService;

    /**
     * 更新合同明细中有结算记录的  已结算金额 自动先去重
     *
     * @param details
     */
    public void setSettledInfo(List<ContractInfoDetail> details) {
        Map<String, Integer> map = new HashMap<>();
        details.forEach(p -> {
            String id = p.getId();
            if (!map.containsKey(id)) { //由于一个清单可以有多个结算记录，所以需要去重处理
                map.put(id, 0);
                LambdaQueryWrapper<Settlement> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(Settlement::getContractInfoDetailId, id);
                List<Settlement> settles = settlementService.list(wrapper);
                BigDecimal total = settles.stream().map(Settlement::getCurrentSettlement).reduce(BigDecimal.ZERO, BigDecimal::add);
                //统计出已结算的金额总和
                p.setSettledMoney(total);
                baseMapper.updateById(p);
            }
        });

    }


    public List<ContractInfoDetail> getSettedDetailChildList(String id, boolean isRoot) {
        if (isRoot) {//合同查明细
            List<String> settleCodeList = settlementService.list(new LambdaQueryWrapper<Settlement>()
                            .eq(Settlement::getContractId, id)).stream()
                    .map(Settlement::getContractInfoDetailId).collect(Collectors.toList());
            //获取到有结算记录的 code 集合
            List<String> ancestorCodes = settleCodeList.stream().map(p -> p.split("\\.")[0]).collect(Collectors.toList());
            LambdaQueryWrapper<Settlement> wrapper = new LambdaQueryWrapper<>();


            List<ContractInfoDetail> details = baseMapper.selectList(new LambdaQueryWrapper<ContractInfoDetail>()
                    .in(ContractInfoDetail::getCode, ancestorCodes));
            details.forEach(p -> p.setSettledMoney(getTotalSettledByDetailCode(p.getCode())));//设置已结算的金额
            return details;
            //根据code集合获取所有的1级记录
        }

        //根据明细查子明细
        //1.根据detailId 获取他的下一层的列表,分别求出总金额,过滤金额=0的项返回
        ContractInfoDetail detail = baseMapper.selectById(id);
        String code = detail.getCode();
        List<ContractInfoDetail> details = baseMapper.selectList(new LambdaQueryWrapper<ContractInfoDetail>()
                .likeRight(ContractInfoDetail::getCode, code + "."));
        details.forEach(p -> {
        });
        return null;
    }


    /**
     * 用于在结算登记查询页面 展示指定batch的结算的总额
     *
     * @param code
     * @return
     */
    private BigDecimal getTotalSettledByDetailCode(String code) {
        LambdaQueryWrapper<Settlement> wrapper = new LambdaQueryWrapper<>();
        wrapper.likeRight(Settlement::getCode, code);
        List<Settlement> settles = settlementService.list(wrapper);
        BigDecimal total = settles.stream().map(Settlement::getCurrentSettlement).reduce(BigDecimal.ZERO, BigDecimal::add);
        return total;
    }

    /**
     * 根据合同id,清单code 获取子项的所有详细信息
     * 结算登记页面 编辑时的搜索接口
     *
     * @param contractId
     * @param code
     * @return
     */
    public List<ContractInfoDetail> queryContractInfoSettlement(String contractId, String code, String batch, String name) {
        LambdaQueryWrapper<ContractInfoDetail> wrapper = new LambdaQueryWrapper<ContractInfoDetail>()
                .eq(ContractInfoDetail::getContractId, contractId);
        if (code != null) {
            wrapper.likeRight(ContractInfoDetail::getCode, code);
        }
        if (name != null) {
            wrapper.like(ContractInfoDetail::getName, name);
        }
        List<ContractInfoDetail> details = baseMapper.selectList(wrapper);

        List<Settlement> settlementList = settlementService.list(new LambdaQueryWrapper<Settlement>()
                .eq(Settlement::getBatch, batch)); //值统计这个批次的
        Map<String, Settlement> map = new HashMap<>();
        settlementList.forEach(p -> map.put(p.getCode(), p));
        details.forEach(p -> {
            BigDecimal total = settlementList.stream()
                    .filter(q -> q.getCode().startsWith(p.getCode() + "."))
                    .map(Settlement::getCurrentSettlement)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal selfCurrentSettlement = BigDecimal.ZERO;
            Settlement selfSettlement = map.get(p.getCode());
            if (selfSettlement != null) {
                p.setSettlementId(selfSettlement.getId());//设置这个详单的 结算id
                BigDecimal tmp = selfSettlement.getCurrentSettlement();
                if (tmp != null) {
                    selfCurrentSettlement = tmp;
                }
            }
            total = total.add(selfCurrentSettlement);
            p.setCurrentSettlement(total);
            p.setSettledMoney(getSettledMoney(p));//计算出这个清单的已结算的总额
        });
        return details;
    }

    /**
     * 计算清单的(包括自身和子孙节点)已计算的总金额
     *
     * @param detail 查询到的记录
     * @return
     */
    private BigDecimal getSettledMoney(ContractInfoDetail detail) {
        String contractId = detail.getContractId();
        String code = detail.getCode();
        List<Settlement> settlementList = settlementService.list(new LambdaQueryWrapper<Settlement>()
                .eq(Settlement::getContractId, contractId)
                .and(wrapper -> wrapper
                        .likeRight(Settlement::getCode, code + ".") // 以code.开头的记录 即子项
                        .or()                                           //不能likeRight(::getCode,code) 这样1.1 可能会匹配到同级的 1.11
                        .eq(Settlement::getCode, code))                 // 等于code的记录 即自身
                .select(Settlement::getCurrentSettlement));
        return settlementList.stream().map(Settlement::getCurrentSettlement).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public void setSettledMoney(List<ContractInfoDetail> details) {
        details.forEach(
                detail -> {
                    detail.setSettledMoney(getSettledMoney(detail));
                }
        );
    }
}
