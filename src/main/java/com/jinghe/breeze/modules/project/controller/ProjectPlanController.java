package com.jinghe.breeze.modules.project.controller;

import java.util.*;
import javax.servlet.http.HttpServletRequest;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.jinghe.breeze.modules.project.entity.ProjectPlanExcelData;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.jinghe.breeze.modules.project.entity.ProjectPlan;
import com.jinghe.breeze.modules.project.service.IProjectPlanService;
import com.jinghe.breeze.modules.project.service.IProjectWbsService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

import static com.jinghe.breeze.modules.project.service.IProjectPlanService.ROOT_PID_VALUE;

/**
 * @Description: project_plan
 * @Author: jeecg-boot
 * @Date: 2024-04-17
 * @Version: V1.0
 */
@Api(tags = "project_plan")
@RestController
@RequestMapping("/project/projectPlan")
@Slf4j
public class ProjectPlanController extends JeecgController<ProjectPlan, IProjectPlanService> {
    @Autowired
    private IProjectPlanService projectPlanService;
    @Autowired
    private IProjectWbsService projectWbsService;

    @ApiOperation(value = "project_plan-获取所有记录", notes = "project_plan-获取所有记录")
    @GetMapping(value = "/list")
    public Result<?> queryList(@RequestParam(name = "version", required = false) Integer version) {
        return projectPlanService.queryList(version);

    }

    @ApiOperation(value = "project_plan-根据id查询子级", notes = "project_plan-根据id查询子级,0就是顶级")
    @GetMapping(value = "/queryListById")
    public Result<?> queryListById(@RequestParam(name = "id", required = true) String pid) {
        QueryWrapper<ProjectPlan> queryWrapper = QueryGenerator.initQueryWrapper(new ProjectPlan(), null);
        List<ProjectPlan> projectPlanList;
        if (pid.equals("0")) { // "0"就是查询顶级
            queryWrapper.eq("layer", 1);
            projectPlanList = projectPlanService.list(queryWrapper);
        } else {// 非0 查询具体的id
            queryWrapper.eq("id", pid);
            ProjectPlan projectPlan = projectPlanService.getOne(queryWrapper);
            if (projectPlan == null) {
                return Result.error("未找到对应记录");
            }
            queryWrapper = QueryGenerator.initQueryWrapper(new ProjectPlan(), null);
            queryWrapper.eq("layer", projectPlan.getLayer() + 1).likeRight("code", projectPlan.getCode() + ".");// 查询{code}.x的记录
            projectPlanList = projectPlanService.list(queryWrapper);
        }
        projectPlanService.processSetHasChild(projectPlanList);// 处理是否有子节点
        return Result.OK(projectPlanList);
    }

    /**
     * 分页列表查询
     *
     * @param projectPlan
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "project_plan-分页列表查询")
    @ApiOperation(value = "project_plan-分页列表查询", notes = "project_plan-分页列表查询")
    @GetMapping(value = "/rootList")
    public Result<?> queryPageList(ProjectPlan projectPlan,
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            HttpServletRequest req) {
        // 此接口不再有分页需求
        String hasQuery = req.getParameter("hasQuery");
        if ("true".equals(hasQuery)) {
            QueryWrapper<ProjectPlan> queryWrapper = QueryGenerator.initQueryWrapper(projectPlan,
                    req.getParameterMap());
            List<ProjectPlan> list = projectPlanService.queryTreeListNoPage(queryWrapper);
            projectPlanService.buildTreeWithoutParent(list, ROOT_PID_VALUE);
            IPage<ProjectPlan> pageList = new Page<>(1, list.size(), list.size());
            pageList.setRecords(list);
            return Result.OK(pageList);
        } else {
            QueryWrapper<ProjectPlan> queryWrapper = QueryGenerator.initQueryWrapper(new ProjectPlan(), null);
            List<ProjectPlan> list = projectPlanService.list(queryWrapper);
            projectPlanService.buildTreeWithoutParent(list, ROOT_PID_VALUE);
            return Result.OK(list);
        }
    }

    /**
     * 获取子数据
     *
     * @param projectPlan
     * @param req
     * @return
     */

    @AutoLog(value = "project_plan-获取子数据")
    @ApiOperation(value = "project_plan-获取子数据", notes = "project_plan-获取子数据")
    @GetMapping(value = "/childList")
    public Result<?> queryPageList(ProjectPlan projectPlan, HttpServletRequest req) {
        QueryWrapper<ProjectPlan> queryWrapper = QueryGenerator.initQueryWrapper(projectPlan, req.getParameterMap());
        List<ProjectPlan> list = projectPlanService.list(queryWrapper);
        IPage<ProjectPlan> pageList = new Page<>(1, 10, list.size());
        pageList.setRecords(list);
        return Result.OK(pageList);
    }

    /**
     * 批量查询子节点
     *
     * @param parentIds 父ID（多个采用半角逗号分割）
     * @param parentIds
     * @return 返回 IPage
     * @return
     */
    @AutoLog(value = "project_plan-批量获取子数据")
    @ApiOperation(value = "project_plan-批量获取子数据", notes = "project_plan-批量获取子数据")
    @GetMapping("/getChildListBatch")
    public Result<?> getChildListBatch(@RequestParam("parentIds") String parentIds) {
        List<ProjectPlan> list = new ArrayList<>();
        try {
            QueryWrapper<ProjectPlan> queryWrapper = new QueryWrapper<>();
            List<String> parentIdList = Arrays.asList(parentIds.split(","));
            if (parentIdList.size() == 0) {
                queryWrapper.in("pid", parentIdList);
                list = projectPlanService.list(queryWrapper);
            }
            IPage<ProjectPlan> pageList = new Page<>(1, 10, list.size());
            pageList.setRecords(list);
            return Result.OK(pageList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("批量查询子节点失败：" + e.getMessage());
        }
    }

    @AutoLog(value = "project_plan-计划填报")
    @ApiOperation(value = "project_plan-计划填报", notes = "project_plan-计划填报")
    @GetMapping("/progressReport")
    public Result<?> progressReport(ProjectPlan projectPlan) {
        ProjectPlan oldProjectPlan = projectPlanService.getById(projectPlan.getId());
        if (oldProjectPlan == null) {
            return Result.error("未找到对应记录");
        }
        oldProjectPlan.setStartDate(projectPlan.getStartDate());
        oldProjectPlan.setActualNum(projectPlan.getActualNum());
        oldProjectPlan.setRemark(projectPlan.getRemark());
        oldProjectPlan.setActualEndDate(projectPlan.getActualEndDate());
        projectPlanService.updateById(oldProjectPlan);
        return Result.OK("更新成功");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "project_plan-通过id删除")
    @ApiOperation(value = "project_plan-通过id删除", notes = "project_plan-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        projectPlanService.deleteProjectPlan(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "project_plan-批量删除")
    @ApiOperation(value = "project_plan-批量删除", notes = "project_plan-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.projectPlanService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "project_plan-通过id查询")
    @ApiOperation(value = "project_plan-通过id查询", notes = "project_plan-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        ProjectPlan projectPlan = projectPlanService.getById(id);
        if (projectPlan == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(projectPlan);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param projectPlan
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ProjectPlan projectPlan) {
        return super.exportXls(request, projectPlan, ProjectPlan.class, "project_plan");
    }

    @ApiOperation(value = "project_plan-Excel导入", notes = "project_plan-Excel导入")
    @PostMapping("/importExcel")
    public Result<?> importExcel(@RequestParam("file") MultipartFile file,
            @RequestParam("version") Integer version) {
        try {
            List<ProjectPlanExcelData> projectPlanExcelDataList = EasyExcel
                    .read(file.getInputStream(), ProjectPlanExcelData.class, null)
                    .excelType(ExcelTypeEnum.XLS)
                    .sheet(0).headRowNumber(1).autoTrim(false).doReadSync();
            projectPlanService.importExcelData(projectPlanExcelDataList, version);
            return Result.OK("文件上传并解析成功");
        } catch (Exception e) {
            log.debug("解析文件时发生错误", e);
            return Result.error("解析文件时发生错误");
        }
    }

    @AutoLog(value = "版本号")
    @ApiOperation(value = "版本号", notes = "版本号")
    @GetMapping(value = "/version")
    public Result<?> version() {
        return projectPlanService.version();
    }
}
