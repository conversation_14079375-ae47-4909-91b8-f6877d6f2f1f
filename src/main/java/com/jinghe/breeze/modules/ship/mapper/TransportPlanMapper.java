package com.jinghe.breeze.modules.ship.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import com.jinghe.breeze.modules.ship.entity.TransportPlan;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: transport_plan
 * @Author: jeecg-boot
 * @Date:   2024-06-06
 * @Version: V1.0
 */
public interface TransportPlanMapper extends BaseMapper<TransportPlan> {


    IPage<TransportPlan> findList(@Param("iPage") IPage<TransportPlan> iPage);
}
