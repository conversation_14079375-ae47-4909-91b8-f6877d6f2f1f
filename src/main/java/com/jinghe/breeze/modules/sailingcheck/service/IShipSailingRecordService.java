package com.jinghe.breeze.modules.sailingcheck.service;

import com.jinghe.breeze.modules.sailingcheck.entity.ShipSailingRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghe.breeze.modules.sailingcheck.entity.vo.SailingVo;
import com.jinghe.breeze.modules.ship.entity.ShipOutgoing;
import org.jeecg.common.api.vo.Result;

import java.util.List;

/**
 * @Description: ship_sailing_record
 * @Author: jeecg-boot
 * @Date:   2024-06-18
 * @Version: V1.0
 */
public interface IShipSailingRecordService extends IService<ShipSailingRecord> {
    Result<?> leave(SailingVo vo);

    Result<?> back(SailingVo vo);

    List<ShipOutgoing> shipList(int option, String department);

    SailingVo shipUsers(String shipId);

}
