package com.jinghe.breeze.modules.monitor.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghe.breeze.modules.monitor.entity.AICaptureRecord;

import java.util.Map;

/**
 * @Description: AI抓拍
 * @Author: jeecg-boot
 * @Date:   2024-08-12
 * @Version: V1.0
 */
public interface IAICaptureRecordService extends IService<AICaptureRecord> {

    Map<String, Long> monthStatistic();
}
