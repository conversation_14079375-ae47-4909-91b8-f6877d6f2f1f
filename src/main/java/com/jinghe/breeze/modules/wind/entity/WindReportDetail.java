package com.jinghe.breeze.modules.wind.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.*;
/**
 * @Description: wind_report_detail
 * @Author: jeecg-boot
 * @Date:   2024-05-24
 * @Version: V1.0
 */
@Data
@TableName("wind_report_detail")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="wind_report_detail对象", description="wind_report_detail")
public class WindReportDetail implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @NotNull(message = "主键不能为空!")

    @ApiModelProperty(value = "主键")
    private String id;
	/**创建人*/

    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/

    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**所属部门*/

    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
	/**删除标识*/
	@Excel(name = "删除标识", width = 15)
    @NotNull(message = "删除标识不能为空!")

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;
	/**避风报备id*/
	@Excel(name = "避风报备id", width = 15)
    @NotNull(message = "避风报备id不能为空!")

    @ApiModelProperty(value = "避风报备id")
    private String reportId;
	/**船名*/
	@Excel(name = "船舶Id", width = 15)

    @ApiModelProperty(value = "船舶Id")
    private String shipId;
	/**MMSI*/
	@Excel(name = "MMSI", width = 15)

    @ApiModelProperty(value = "MMSI")
    private String mmsi;
	/**船上紧急联系人*/
	@Excel(name = "船上紧急联系人", width = 15)

    @ApiModelProperty(value = "船上紧急联系人")
    private String shipContact;
	/**在船人数*/
	@Excel(name = "在船人数", width = 15)

    @ApiModelProperty(value = "在船人数")
    private String personTotal;
	/**避风港围栏id*/
	@Excel(name = "避风港围栏id", width = 15)

    @ApiModelProperty(value = "避风港围栏id")
    private String fenceId;
	/**备注*/
	@Excel(name = "备注", width = 15)

    @ApiModelProperty(value = "备注")
    private String remark;
}
