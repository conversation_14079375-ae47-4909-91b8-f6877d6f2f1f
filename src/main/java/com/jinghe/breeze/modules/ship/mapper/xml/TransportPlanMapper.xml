<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghe.breeze.modules.ship.mapper.TransportPlanMapper">

    <select id="findList" resultType="com.jinghe.breeze.modules.ship.entity.TransportPlan">
        ((SELECT
	t.*
FROM
	transport_plan t
WHERE
	t.`status` = 'underway'
ORDER BY
	t.arrive_date ASC LIMIT 99999)UNION ALL
(SELECT
	a.*
FROM
	transport_plan  a
WHERE
	a.`status` = 'over'
ORDER BY
	a.arrive_date desc LIMIT 99999) )
    </select>
</mapper>