package com.jinghe.breeze.modules.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.jeecg.common.exception.JeecgBootException;
import com.jinghe.breeze.modules.project.entity.ProjectWbs;
import com.jinghe.breeze.modules.project.mapper.ProjectWbsMapper;
import com.jinghe.breeze.modules.project.service.IProjectWbsService;

import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: project_wbs
 * @Author: jeecg-boot
 * @Date: 2024-04-17
 * @Version: V1.0
 */
@Service
public class ProjectWbsServiceImpl extends ServiceImpl<ProjectWbsMapper, ProjectWbs> implements IProjectWbsService {

    @Override
    public void editProjectWbs(ProjectWbs projectWbs) {
        ProjectWbs currentWbs = baseMapper.selectById(projectWbs.getId());
        if (currentWbs == null) {
            throw new JeecgBootException("未找到对应记录");
        }
        projectWbs.setCode(currentWbs.getCode());
        projectWbs.setPid(currentWbs.getPid());
        projectWbs.setLayer(currentWbs.getLayer());
        projectWbs.setSortNo(currentWbs.getSortNo());
        baseMapper.updateById(projectWbs);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProjectWbs(String id) throws JeecgBootException {
        // 查询选中节点下所有子节点一并删除
        id = this.queryTreeChildIds(id);
        if (id.indexOf(",") > 0) {
            StringBuffer sb = new StringBuffer();
            String[] idArr = id.split(",");
            for (String idVal : idArr) {
                if (idVal != null) {
                    ProjectWbs projectWbs = this.getById(idVal);
                    String pidVal = projectWbs.getPid();
                    // 查询此节点上一级是否还有其他子节点
                    List<ProjectWbs> dataList = baseMapper.selectList(
                            new QueryWrapper<ProjectWbs>().eq("pid", pidVal).notIn("id", Arrays.asList(idArr)));
                    if ((dataList == null || dataList.size() == 0) && !Arrays.asList(idArr).contains(pidVal)
                            && !sb.toString().contains(pidVal)) {
                        // 如果当前节点原本有子节点 现在木有了，更新状态
                        sb.append(pidVal).append(",");
                    }
                }
            }
            // 批量删除节点
            baseMapper.deleteBatchIds(Arrays.asList(idArr));
            // 修改已无子节点的标识
            String[] pidArr = sb.toString().split(",");
            for (String pid : pidArr) {
                this.updateOldParentNode(pid);
            }
        } else {
            ProjectWbs projectWbs = this.getById(id);
            if (projectWbs == null) {
                throw new JeecgBootException("未找到对应实体");
            }
            updateOldParentNode(projectWbs.getPid());

            baseMapper.deleteById(id);
        }
    }

    @Override
    public List<ProjectWbs> queryTreeListNoPage(QueryWrapper<ProjectWbs> queryWrapper) {
        List<ProjectWbs> dataList = baseMapper.selectList(queryWrapper);
        List<ProjectWbs> mapList = new ArrayList<>();
        for (ProjectWbs data : dataList) {
            String pidVal = data.getPid();
            // 递归查询子节点的根节点
            if (pidVal != null && !"0".equals(pidVal)) {
                ProjectWbs rootVal = this.getTreeRoot(pidVal);
                if (rootVal != null && !mapList.contains(rootVal)) {
                    mapList.add(rootVal);
                }
            } else {
                if (!mapList.contains(data)) {
                    mapList.add(data);
                }
            }
        }
        return mapList;
    }

    /**
     * 根据所传pid查询旧的父级节点的子节点并修改相应状态值
     *
     * @param pid
     */
    private void updateOldParentNode(String pid) {
        if (!IProjectWbsService.ROOT_PID_VALUE.equals(pid)) {
            Integer count = baseMapper.selectCount(new QueryWrapper<ProjectWbs>().eq("pid", pid));
            if (count == null || count <= 1) {
                baseMapper.updateTreeNodeStatus(pid, IProjectWbsService.NOCHILD);
            }
        }
    }

    public void setCanMoveUpDown(List<ProjectWbs> dataList) {
        // 如果同级中比自身sort大的集合为空,则不能下移,同级中比自身sort小的集合为空,则不能上移
        for (ProjectWbs tree : dataList) {
            String pidVal = tree.getPid();
            LambdaQueryWrapper<ProjectWbs> queryWrapper = new LambdaQueryWrapper<>();
            if (pidVal == null) {
                queryWrapper.isNull(ProjectWbs::getPid).lt(ProjectWbs::getSortNo, tree.getSortNo());
            } else {
                queryWrapper.eq(ProjectWbs::getPid, pidVal).lt(ProjectWbs::getSortNo, tree.getSortNo());
            }
            Integer count = baseMapper.selectCount(queryWrapper);
            tree.setUpEnable(count != 0);
            queryWrapper.clear();
            if (pidVal == null) {
                queryWrapper.isNull(ProjectWbs::getPid).gt(ProjectWbs::getSortNo, tree.getSortNo());
            } else {
                queryWrapper.eq(ProjectWbs::getPid, pidVal).gt(ProjectWbs::getSortNo, tree.getSortNo());
            }
            count = baseMapper.selectCount(queryWrapper);
            tree.setDownEnable(count != 0);
        }
    }

    /**
     * 递归查询节点的根节点
     *
     * @param pidVal
     * @return
     */
    private ProjectWbs getTreeRoot(String pidVal) {
        ProjectWbs data = baseMapper.selectById(pidVal);
        if (data != null && !"0".equals(data.getPid())) {
            return this.getTreeRoot(data.getPid());
        } else {
            return data;
        }
    }

    /**
     * 根据id查询所有子节点id
     *
     * @param ids
     * @return
     */
    private String queryTreeChildIds(String ids) {
        // 获取id数组
        String[] idArr = ids.split(",");
        StringBuffer sb = new StringBuffer();
        for (String pidVal : idArr) {
            if (pidVal != null) {
                if (!sb.toString().contains(pidVal)) {
                    if (sb.toString().length() > 0) {
                        sb.append(",");
                    }
                    sb.append(pidVal);
                    this.getTreeChildIds(pidVal, sb);
                }
            }
        }
        return sb.toString();
    }

    /**
     * 递归查询所有子节点
     *
     * @param pidVal
     * @param sb
     * @return
     */
    private StringBuffer getTreeChildIds(String pidVal, StringBuffer sb) {
        List<ProjectWbs> dataList = baseMapper.selectList(new QueryWrapper<ProjectWbs>().eq("pid", pidVal));
        if (dataList != null && dataList.size() > 0) {
            for (ProjectWbs tree : dataList) {
                if (!sb.toString().contains(tree.getId())) {
                    sb.append(",").append(tree.getId());
                }
                this.getTreeChildIds(tree.getId(), sb);
            }
        }
        return sb;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addProjectWbs(ProjectWbs projectWbs) {
        ProjectWbs wbs = baseMapper.selectOne(new LambdaQueryWrapper<ProjectWbs>()
                .eq(ProjectWbs::getCode, projectWbs.getCode()).last("limit 1"));
        if (wbs != null) {
            throw new JeecgBootException("编码（字段名称）重复！");
        }
        String pid = projectWbs.getPid();
        if (pid == null) {
            pid = ROOT_PID_VALUE;
        }
        int layer = 0;
        if (!pid.equals(ROOT_PID_VALUE)) {
            ProjectWbs parent = baseMapper.selectById(pid);
            if (parent != null) {
                layer = parent.getLayer() + 1;
                parent.setHasChild(1);
            } else {
                throw new JeecgBootException("指定的父节点不存在");
            }
        }
        wbs = baseMapper.selectOne(new LambdaQueryWrapper<ProjectWbs>().eq(ProjectWbs::getPid, pid)
                .orderByDesc(ProjectWbs::getSortNo));
        int maxSort = wbs == null ? 0 : wbs.getSortNo();
        projectWbs.setSortNo(maxSort + 1);
        projectWbs.setLayer(layer);
        super.save(wbs);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void moveProjectWbs(ProjectWbs wbs, boolean isUp) {
        wbs = baseMapper.selectById(wbs.getId());
        if (wbs == null) {
            throw new JeecgBootException("记录不存在");
        }
        String pid = wbs.getPid();
        LambdaQueryWrapper<ProjectWbs> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjectWbs::getPid, pid);
        if (isUp) {
            wrapper.lt(ProjectWbs::getSortNo, wbs.getSortNo()).orderByDesc(ProjectWbs::getSortNo);
        } else { //down
            wrapper.gt(ProjectWbs::getSortNo, wbs.getSortNo()).orderByAsc(ProjectWbs::getSortNo);
        }
        wrapper.last("LIMIT 1");
        ProjectWbs target = baseMapper.selectOne(wrapper);
        if (target == null) {
            throw new JeecgBootException(isUp ? "被移动的对象已经是第一个" : "被移动的对象已经是最后一个");
        }
        int tmpSort = target.getSortNo();
        target.setSortNo(wbs.getSortNo());
        wbs.setSortNo(tmpSort);//交换双方的sort
        baseMapper.updateById(wbs);
        baseMapper.updateById(target);
    }

}
