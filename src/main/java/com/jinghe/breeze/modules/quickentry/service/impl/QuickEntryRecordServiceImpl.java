package com.jinghe.breeze.modules.quickentry.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.modules.quickentry.entity.QuickEntryRecord;
import com.jinghe.breeze.modules.quickentry.mapper.QuickEntryRecordMapper;
import com.jinghe.breeze.modules.quickentry.service.IQuickEntryRecordService;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: quick_entry_record
 * @Author: jeecg-boot
 * @Date: 2024-06-26
 * @Version: V1.0
 */
@Service
public class QuickEntryRecordServiceImpl extends ServiceImpl<QuickEntryRecordMapper, QuickEntryRecord>
        implements IQuickEntryRecordService {
    public Result<?> queryByLoginUser() {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userid = loginUser.getId();
        QuickEntryRecord entity = baseMapper.selectOne(new LambdaQueryWrapper<QuickEntryRecord>()
                .eq(QuickEntryRecord::getUserId, userid)
                .last("limit 1"));
        return Result.OK(entity);
    }

    public Result<?> saveInfo(QuickEntryRecord record) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userid = loginUser.getId();
        QuickEntryRecord entity = baseMapper.selectOne(new LambdaQueryWrapper<QuickEntryRecord>()
                .eq(QuickEntryRecord::getUserId, userid));
        if (entity == null) {
            QuickEntryRecord quickEntryRecord = new QuickEntryRecord();
            quickEntryRecord.setUserId(userid);
            quickEntryRecord.setPath(record.getPath());
            super.save(quickEntryRecord);
        } else {
            entity.setPath(record.getPath());
            super.updateById(entity);
        }
        return Result.OK("保存成功");
    }
}
