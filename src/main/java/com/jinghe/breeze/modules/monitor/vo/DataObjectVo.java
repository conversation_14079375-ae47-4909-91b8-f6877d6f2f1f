package com.jinghe.breeze.modules.monitor.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: monitor_person_record
 * @Author: jeecg-boot
 * @Date: 2024-08-07
 * @Version: V1.0
 */
@Data
@ApiModel(value = "对象")
public class DataObjectVo implements Serializable {
    
    private String day;

    private String count;

    private String action;

}
