package com.jinghe.breeze.modules.dashboard.controller;


import com.jinghe.breeze.modules.dashboard.service.IDashboardService;
import com.jinghe.breeze.modules.sea.entity.ExtremeWeather;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Api(tags = "dashboard")
@RestController
@RequestMapping("/dashboard/information")
@Slf4j
public class DashbordController {
    @Autowired
    private IDashboardService dashboardService;

    @ApiOperation(value = "dashboard-app避风通知", notes = "dashboard-app")
    @GetMapping(value = "/windnotifycation")
    public Result<?> windnotifycation() {
        return Result.OK(dashboardService.getWindNotification());
    }

    @ApiOperation(value = "dashboard-app船舶告警", notes = "dashboard-app船舶告警")
    @GetMapping(value = "/getShipAlarm")
    public Result<?> getShipAlarm() {
        return dashboardService.getShipAlarm();
    }


    @ApiOperation(value = "dashboard-app极端天气预警", notes = "dashboard-app极端天气预警")
    @GetMapping(value = "/getExtremeWeather")
    public Result<?> getExtremeWeather() {
        return dashboardService.getExtremeWeather();
    }

    @ApiOperation(value = "dashboard-app实时天气", notes = "dashboard-app实时天气")
    @GetMapping(value = "/getRealTimeWeather")
    public Result<?> getRealTimeWeather() {
        return dashboardService.getRealTimeWeather();
    }

    //========================================以下是pc端首页接口===================================


    @ApiOperation(value = "dashboard-pc首页项目信息", notes = "dashboard-pc首页项目信息")
    @GetMapping(value = "/getProjectInfo")
    public Result<?> getProjectInfo() {
        Map<String, String> map = dashboardService.getProjectInfo();
        return Result.OK(map);
    }

    @ApiOperation(value = "dashboard-pc七天天气", notes = "dashboard-pc七天天气")
    @GetMapping(value = "/getWeather7days")
    public Result<?> getWeather7days() {

        Map map = dashboardService.getWeather7days();
        return Result.OK(map);
    }

    @ApiOperation(value = "dashboard-施工进度", notes = "dashboard-施工进度")
    @GetMapping(value = "/getConstructInfo")
    public Result<?> getConstructInfo() {
        try {
            Map<String, Object> map = dashboardService.getConstructInfo();
            return Result.OK(map);
        } catch (JeecgBootException e) {
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "dashboard-摄像头信息", notes = "dashboard-摄像头信息")
    @GetMapping(value = "/getCameraInfo")
    public Result<?> getCameraInfo() {
        return Result.OK(dashboardService.getCameraInfo());
    }

    @ApiOperation(value = "dashboard-人员概览", notes = "dashboard-人员概览")
    @GetMapping(value = "/getPersonInfo")
    public Result<?> getPersonInfo() {

        return Result.OK(dashboardService.getPersonInfo());
    }

    @ApiOperation(value = "dashboard-船舶概览", notes = "dashboard-船舶概览")
    @GetMapping(value = "/getShipInfo")
    public Result<?> getShipInfo() {
        return Result.OK(dashboardService.getShipInfo());
    }

    @ApiOperation(value = "dashboard-当前出海信息", notes = "dashboard-当前出海信息")
    @GetMapping(value = "/getCurrentShipSailingInfo")
    public Result<?> getShipAlarmInfo() {
        return Result.OK(dashboardService.getCurrentShipSailingInfo());
    }


//    @ApiOperation(value = "dashboard-大屏首页信息接口", notes = "dashboard-大屏首页信息接口")
//    @GetMapping(value = "/homeInfo")
//    public Result<?> homeInfo() {
//        Map<String, Object> stringObjectMap = dashboardService.bigScreenSchedule();
//        List<String> strings = dashboardService.bigScreenPictures();
//        Map<String, List<Integer>> weekWork = dashboardService.bigScreenWeekWork();
//        Map<String, Integer> dolphinInfo = dashboardService.bigScreenDolphinInfo();
//        Map<String, Integer> weatherInfo = dashboardService.bigScreenWeatherInfo();
//        List<ExtremeWeather> extremeWeathers = dashboardService.bigScreenWeatherAlarm();
//        stringObjectMap.put("pictures", strings);
//        stringObjectMap.put("weekWork", weekWork);
//        stringObjectMap.put("dolphinInfo", dolphinInfo);
//        stringObjectMap.put("weatherInfo", weatherInfo);
//        stringObjectMap.put("extremeWeathers", extremeWeathers);
//
//        return Result.OK(stringObjectMap);
//    }

    @ApiOperation(value = "dashboard-大屏首页,项目进度和风机施工状态", notes = "dashboard-大屏首页，项目进度和风机施工状态")
    @GetMapping(value = "/bigScreen/projectInfo")
    public Result<?> projectInfo() {
        Map<String, Object> stringObjectMap = dashboardService.bigScreenSchedule();
        return Result.OK(stringObjectMap);
    }

    @ApiOperation(value = "dashboard-大屏首页,轮播图", notes = "dashboard-大屏首页轮播图")
    @GetMapping(value = "/bigScreen/pictures")
    public Result<?> pictures() {
        List<String> strings = dashboardService.bigScreenPictures();
        return Result.OK(strings);
    }

    @ApiOperation(value = "dashboard-大屏首页7日投入", notes = "dashboard-大屏首页7日投入")
    @GetMapping(value = "/bigScreen/weekWork")
    public Result<?> weekWork() {
        Map<String, List<Integer>> weekWork = dashboardService.bigScreenWeekWork();
        return Result.OK(weekWork);
    }

    @ApiOperation(value = "dashboard-大屏首页观豚统计", notes = "dashboard-大屏首页观豚统计")
    @GetMapping(value = "/bigScreen/dolphinInfo")
    public Result<?> dolphinInfo() {
        Map<String, Integer> dolphinInfo = dashboardService.bigScreenDolphinInfo();
        return Result.OK(dolphinInfo);
    }

    @ApiOperation(value = "dashboard-大屏首页天气状况", notes = "dashboard-大屏首页天气状况")
    @GetMapping(value = "/bigScreen/weatherInfo")
    public Result<?> weatherInfo() {
        Map<String, Integer> weatherInfo = dashboardService.bigScreenWeatherInfo();
        return Result.OK(weatherInfo);
    }

    @ApiOperation(value = "dashboard-大屏首页天气预警", notes = "dashboard-大屏首页天气预警")
    @GetMapping(value = "/bigScreen/weatherAlarm")
    public Result<?> weatherAlarm() {
        List<ExtremeWeather> extremeWeathers = dashboardService.bigScreenWeatherAlarm();
        return Result.OK(extremeWeathers);
    }

    @ApiOperation(value = "dashboard-大屏首数据看板进度", notes = "dashboard-大屏首数据看板进度")
    @GetMapping(value = "/bigScreen/schedule")
    public Result<?> schedule(@RequestParam("id") String id) {
        return dashboardService.schedule(id);
    }

}
