-- 创建测试用的电子围栏数据
-- 包含安全防护围栏和施工围栏两种类型

-- 删除可能存在的测试数据
DELETE FROM project_fence WHERE code IN ('TEST_SAFETY_FENCE_001', 'TEST_CONSTRUCTION_FENCE_001');

-- 插入安全防护围栏数据 (fence_type = '1')
INSERT INTO project_fence (
    id,
    create_by,
    create_time,
    update_by,
    update_time,
    del_flag,
    sys_org_code,
    project_id,
    code,
    name,
    fence_type,
    fence_radius,
    remark
) VALUES (
    'TEST_SAFETY_FENCE_001',
    'admin',
    NOW(),
    'admin',
    NOW(),
    0,
    'A01',
    'PROJECT_001',
    'TEST_SAFETY_FENCE_001',
    '安全防护围栏',
    '1',
    '[[116.397428, 39.90923], [116.407428, 39.90923], [116.407428, 39.89923], [116.397428, 39.89923], [116.397428, 39.90923]]',
    '安全防护围栏，覆盖北京天安门广场附近区域'
);

-- 插入施工围栏数据 (fence_type = '3')
INSERT INTO project_fence (
    id,
    create_by,
    create_time,
    update_by,
    update_time,
    del_flag,
    sys_org_code,
    project_id,
    code,
    name,
    fence_type,
    fence_radius,
    remark
) VALUES (
    'TEST_CONSTRUCTION_FENCE_001',
    'admin',
    NOW(),
    'admin',
    NOW(),
    0,
    'A01',
    'PROJECT_001',
    'TEST_CONSTRUCTION_FENCE_001',
    '施工围栏',
    '3',
    '[[116.408000, 39.91000], [116.418000, 39.91000], [116.418000, 39.90000], [116.408000, 39.90000], [116.408000, 39.91000]]',
    '施工围栏，位于安全围栏东侧，两个围栏不重叠'
);

-- 验证插入结果
SELECT
    id,
    name,
    fence_type,
    fence_radius,
    del_flag,
    create_time
FROM project_fence
WHERE code IN ('TEST_SAFETY_FENCE_001', 'TEST_CONSTRUCTION_FENCE_001')
ORDER BY fence_type;

-- 围栏坐标说明:
--
-- 安全防护围栏 (fence_type = '1'):
-- 1. [116.397428, 39.90923] - 西北角
-- 2. [116.407428, 39.90923] - 东北角
-- 3. [116.407428, 39.89923] - 东南角
-- 4. [116.397428, 39.89923] - 西南角
-- 5. [116.397428, 39.90923] - 回到起点形成闭合多边形
-- 坐标范围: 经度 116.397428~116.407428, 纬度 39.89923~39.90923
--
-- 施工围栏 (fence_type = '3'):
-- 1. [116.408000, 39.91000] - 西北角
-- 2. [116.418000, 39.91000] - 东北角
-- 3. [116.418000, 39.90000] - 东南角
-- 4. [116.408000, 39.90000] - 西南角
-- 5. [116.408000, 39.91000] - 回到起点形成闭合多边形
-- 坐标范围: 经度 116.408000~116.418000, 纬度 39.90000~39.91000
--
-- 围栏类型说明:
-- fence_type = '1' 表示 "安全防护围栏" (ProjectFensEnum.YJ)
-- fence_type = '3' 表示 "施工围栏" (ProjectFensEnum.SG)
--
-- 注意: 两个围栏区域不重叠，施工围栏位于安全围栏的东侧
