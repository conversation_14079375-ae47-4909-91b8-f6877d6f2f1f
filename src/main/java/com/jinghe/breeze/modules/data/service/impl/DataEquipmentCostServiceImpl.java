package com.jinghe.breeze.modules.data.service.impl;

import com.jinghe.breeze.modules.data.entity.DataEquipmentCost;
import com.jinghe.breeze.modules.data.mapper.DataEquipmentCostMapper;
import com.jinghe.breeze.modules.data.service.IDataEquipmentCostService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: data_equipment_cost
 * @Author: jeecg-boot
 * @Date:   2024-06-05
 * @Version: V1.0
 */
@Service
public class DataEquipmentCostServiceImpl extends ServiceImpl<DataEquipmentCostMapper, DataEquipmentCost> implements IDataEquipmentCostService {

}
