package com.jinghe.breeze.modules.construction.service;

import com.jinghe.breeze.modules.construction.entity.MonitorDolphinLog;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghe.breeze.modules.construction.entity.MonitorDolphinLogDetail;
import org.jeecg.common.api.vo.Result;

import java.util.Date;
import java.util.List;

/**
 * @Description: 观豚日志
 * @Author: jeecg-boot
 * @Date: 2024-05-11
 * @Version: V1.0
 */
public interface IMonitorDolphinLogService extends IService<MonitorDolphinLog> {

    public int combineDateAndTime(Date date, long timestamp);

    public void updateTimestamp(MonitorDolphinLog monitorDolphinLog, MonitorDolphinLogDetail detail);

    Result<?> editLog(MonitorDolphinLog monitorDolphinLog);

    Result<?> getSaillingShipName(String dateStr);

    Result<?> addLog(MonitorDolphinLog monitorDolphinLog);

    List<MonitorDolphinLog> queryByMonth(String name);
}
