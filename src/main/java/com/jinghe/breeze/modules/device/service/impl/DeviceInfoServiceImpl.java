package com.jinghe.breeze.modules.device.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghe.breeze.modules.device.entity.DeviceGate;
import com.jinghe.breeze.modules.device.entity.DeviceInfo;
import com.jinghe.breeze.modules.device.mapper.DeviceGateMapper;
import com.jinghe.breeze.modules.device.mapper.DeviceInfoMapper;
import com.jinghe.breeze.modules.device.service.IDeviceInfoService;
import com.jinghe.breeze.modules.device.vo.BaseDevicePage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 设备信息
 * @Author: jeecg-boot
 * @Date: 2024-08-13
 * @Version: V1.0
 */
@Service
public class DeviceInfoServiceImpl extends ServiceImpl<DeviceInfoMapper, DeviceInfo> implements IDeviceInfoService {

    @Autowired
    private DeviceInfoMapper baseDeviceMapper;
    @Autowired
    private DeviceGateMapper baseDeviceGateMapper;

    @Override
    @Transactional
    public void saveMain(DeviceInfo baseDevice, List<DeviceGate> baseDeviceGateList) {
        baseDeviceMapper.insert(baseDevice);
        if (baseDeviceGateList != null && baseDeviceGateList.size() > 0) {
            for (DeviceGate entity : baseDeviceGateList) {
                //外键设置
                entity.setBaseId(baseDevice.getId());
                baseDeviceGateMapper.insert(entity);
            }
        }
    }

    @Override
    @Transactional
    public void updateMain(DeviceInfo baseDevice, List<DeviceGate> baseDeviceGateList) {
        baseDeviceMapper.updateById(baseDevice);

        //1.先删除子表数据
        baseDeviceGateMapper.deleteByMainId(baseDevice.getId());

        //2.子表数据重新插入
        if (baseDeviceGateList != null && baseDeviceGateList.size() > 0) {
            for (DeviceGate entity : baseDeviceGateList) {
                //外键设置
                entity.setBaseId(baseDevice.getId());
                baseDeviceGateMapper.insert(entity);
            }
        }
    }


    @Override
    @Transactional
    public void delMain(String id) {
        baseDeviceGateMapper.delete(Wrappers.<DeviceGate>lambdaQuery().eq(DeviceGate::getBaseId, id));
        baseDeviceMapper.deleteById(id);
    }

    @Override
    @Transactional
    public void delBatchMain(Collection<? extends Serializable> idList) {
        for (Serializable id : idList) {
            baseDeviceGateMapper.delete(Wrappers.<DeviceGate>lambdaQuery().eq(DeviceGate::getBaseId, id));
            baseDeviceMapper.deleteById(id);
        }
    }

    @Override
    public List<BaseDevicePage> getListByIds(List<String> deviceIds) {
        if (CollectionUtil.isEmpty(deviceIds)) {
            return ListUtil.empty();
        }
        LambdaQueryWrapper<DeviceInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(DeviceInfo::getId, deviceIds);
        List<DeviceInfo> baseDevices = baseDeviceMapper.selectList(lambdaQueryWrapper);
        List<BaseDevicePage> result = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(baseDevices)) {
            for (DeviceInfo record : baseDevices) {
                BaseDevicePage obj = new BaseDevicePage();
                BeanUtil.copyProperties(record, obj);
                result.add(obj);
            }
        }
        return result;
    }

}
