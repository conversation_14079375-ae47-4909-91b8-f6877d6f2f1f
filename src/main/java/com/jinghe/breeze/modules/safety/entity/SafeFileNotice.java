package com.jinghe.breeze.modules.safety.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.*;
/**
 * @Description: 文件通知管理
 * @Author: jeecg-boot
 * @Date:   2025-06-26
 * @Version: V1.0
 */
@Data
@TableName("safe_file_notice")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="safe_file_notice对象", description="文件通知管理")
public class SafeFileNotice implements Serializable {
    private static final long serialVersionUID = 1L;

	/**标识*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "标识")
    private String id;
	/**创建人*/

    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**更新人*/

    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
	/**delFlag*/
	@Excel(name = "delFlag", width = 15)
    @ApiModelProperty(value = "delFlag")
    private Integer delFlag;
	/**组织机构编码*/
    @ApiModelProperty(value = "组织机构编码")
    private String sysOrgCode;
	/**通知名称*/
	@Excel(name = "通知名称", width = 15)
    @ApiModelProperty(value = "通知名称")
    private String name;
	/**上传人*/
	@Excel(name = "上传人", width = 15)
    @ApiModelProperty(value = "上传人")
    private String uploader;
	/**上传时间*/
	@Excel(name = "上传时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "上传时间")
    private Date uploadTime;
	/**文件接收人*/
	@Excel(name = "文件接收人", width = 15, dictTable = "person_info", dicText = "name", dicCode = "user_id")
	@Dict(dictTable = "person_info", dicText = "name", dicCode = "user_id")
    @ApiModelProperty(value = "文件接收人")
    private String recipient;

    /**文件接收状态*/
    @Excel(name = "文件接收状态", width = 15,dicCode = "receiving_status")
    @Dict(dicCode = "receiving_status")
    @ApiModelProperty(value = "文件接收状态")
    private String receivingStatus;

    /**文件接收状态*/
    @Excel(name = "文件", width = 15)
    @ApiModelProperty(value = "文件")
    private String noticeFile;

    @TableField(exist = false)
    private List<SafeFileNoticeDetail> safeFileNoticeDetailList;
}
