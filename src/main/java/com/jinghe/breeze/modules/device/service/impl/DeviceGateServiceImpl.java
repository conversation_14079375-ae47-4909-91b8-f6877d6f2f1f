package com.jinghe.breeze.modules.device.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghe.breeze.modules.device.entity.DeviceGate;
import com.jinghe.breeze.modules.device.mapper.DeviceGateMapper;
import com.jinghe.breeze.modules.device.service.IDeviceGateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 闸机设备信息
 * @Author: jeecg-boot
 * @Date: 2024-08-13
 * @Version: V1.0
 */
@Service
public class DeviceGateServiceImpl extends ServiceImpl<DeviceGateMapper, DeviceGate> implements IDeviceGateService {

    @Autowired
    private DeviceGateMapper baseDeviceGateMapper;

    @Override
    public List<DeviceGate> selectByMainId(String mainId) {
        return baseDeviceGateMapper.selectByMainId(mainId);
    }
}
