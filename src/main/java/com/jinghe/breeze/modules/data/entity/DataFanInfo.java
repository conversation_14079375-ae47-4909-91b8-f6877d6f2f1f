package com.jinghe.breeze.modules.data.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * @Description: data_fan_info
 * @Author: jeecg-boot
 * @Date: 2024-05-17
 * @Version: V1.0
 */
@Data
@TableName("data_fan_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "data_fan_info对象", description = "data_fan_info")
public class DataFanInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
//    @NotNull(message = "主键不能为空!")

    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    /**
     * 创建人
     */

    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")

    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
    /**
     * 更新人
     */

    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")

    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
    /**
     * 所属部门
     */

    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
    /**
     * 删除标识
     */
    @Excel(name = "删除标识", width = 15)
    //@NotNull(message = "删除标识不能为空!")

    @ApiModelProperty(value = "删除标识")
    private java.lang.Integer delFlag;
    /**
     * 风机类型名
     */
    @Excel(name = "风机类型名", width = 15)

    @ApiModelProperty(value = "风机类型名")
    private java.lang.String fanType;
    /**
     * 备注
     */
    @Excel(name = "备注", width = 15)

    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
    /**
     * 已安装图标
     */
    @Excel(name = "已安装图标", width = 15)

    @ApiModelProperty(value = "已安装图标")
    private java.lang.String installedIcon;
    /**
     * 未安装图标
     */
    @Excel(name = "未安装图标", width = 15)

    @ApiModelProperty(value = "未安装图标")
    private java.lang.String uninstallIcon;
}
