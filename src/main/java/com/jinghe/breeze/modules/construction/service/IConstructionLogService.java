package com.jinghe.breeze.modules.construction.service;

import com.jinghe.breeze.modules.construction.entity.ConstructionLog;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghe.breeze.modules.construction.entity.vo.ConstructionLogResult;
import com.jinghe.breeze.modules.construction.entity.vo.ConstructionLogVo;
import org.jeecg.common.api.vo.Result;
import org.springframework.scheduling.annotation.Async;

import java.util.List;

/**
 * @Description: construction_log
 * @Author: jeecg-boot
 * @Date: 2024-05-09
 * @Version: V1.0
 */
public interface IConstructionLogService extends IService<ConstructionLog> {

    Result<?> queryList(String pbsId, String projectId, String processType);

    Result<?> getAllList(String projectId);

    void saveList(ConstructionLogVo constructionLogVo);

    @Async
    void planProcess(String pbsId, List<String> processIds);

    List<ConstructionLogResult> commitBatch(List<ConstructionLog> data);

    Result<?> report(String projectId, String type);

    void clear(ConstructionLog constructionLog);
}
