package com.jinghe.breeze.modules.wind.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import com.jinghe.breeze.modules.wind.entity.WindReport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

/**
 * @Description: wind_report
 * @Author: jeecg-boot
 * @Date: 2024-05-24
 * @Version: V1.0
 */
public interface WindReportMapper extends BaseMapper<WindReport> {
    @Select("SELECT wind_report.* from wind_report " +
            "JOIN wind_notification ON wind_report.notify_id = wind_notification.id " +
            "WHERE wind_report.unit_name = #{unitName} " +
            "AND wind_notification.name LIKE #{name} " +
            "ORDER BY wind_report.create_time DESC " +
            " LIMIT   #{offset} ,#{pageSize};"
    )
    List<WindReport> getReportByUnitNameAndName(@Param("unitName") String unitName,
                                                @Param("name") String notificationName,
                                                @Param("pageSize") int pageSize,
                                                @Param("offset") int offset);

    @Select("SELECT wind_report.* from wind_report " +
            "JOIN wind_notification ON wind_report.notify_id = wind_notification.id " +
            "WHERE wind_report.unit_name = #{unitName} " +
            "ORDER BY wind_report.create_time DESC " +
            "LIMIT #{pageSize} OFFSET #{offset}"
    )
    List<WindReport> getReportByUnitName(@Param("unitName") String unitName,
                                         @Param("pageSize") int pageSize,
                                         @Param("offset") int offset);
}
