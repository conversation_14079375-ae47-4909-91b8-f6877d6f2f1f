package com.jinghe.breeze.modules.person.controller;

import java.util.*;

import javax.servlet.http.HttpServletRequest;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.modules.person.entity.PersonInfo;
import com.jinghe.breeze.modules.person.service.IPersonInfoService;
import com.jinghe.breeze.modules.person.dto.LocationDataDTO;
import com.jinghe.breeze.modules.person.dto.EnrichedLocationDataDTO;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.jinghe.breeze.modules.person.entity.DeviceHelmet;
import com.jinghe.breeze.modules.person.service.IDeviceHelmetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: 设备信息表
 * @Author:
 * @Date: 2022-08-09
 * @Version: V1.0
 */
@Api(tags = "设备信息表")
@RestController
@RequestMapping("/person/deviceHelmet")
@Slf4j
public class DeviceHelmetController {
    @Autowired
    private IDeviceHelmetService deviceInfoService;

    @Autowired
    private IPersonInfoService personInfoService;


    /**
     * 分页列表查询
     *
     * @param deviceHelmet
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "设备信息表-分页列表查询")
    @ApiOperation(value = "设备信息表-分页列表查询", notes = "设备信息表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(DeviceHelmet deviceHelmet,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<DeviceHelmet> queryWrapper = QueryGenerator.initQueryWrapper(deviceHelmet, req.getParameterMap());
        //编号模糊搜索
        if (deviceHelmet.getDeviceCode() != null && !deviceHelmet.getDeviceCode().isEmpty()) {
            queryWrapper.like("device_code", deviceHelmet.getDeviceCode());
        }
        //人员搜索
        if (deviceHelmet.getBindUserId() != null && !deviceHelmet.getBindUserId().isEmpty()) {
            List<PersonInfo> personInfoList = personInfoService.list(new LambdaQueryWrapper<PersonInfo>()
                    .eq(PersonInfo::getId, deviceHelmet.getBindUserId())
                    .last("limit 1"));
            if (personInfoList.isEmpty()) {
                return Result.OK(new Page<>());
            }
            String code = personInfoList.get(0).getDeviceCode();
            queryWrapper.eq("device_code", code);
        }
        queryWrapper.orderByDesc("create_time");
        Page<DeviceHelmet> page = new Page<DeviceHelmet>(pageNo, pageSize);
        IPage<DeviceHelmet> pageList = deviceInfoService.page(page, queryWrapper);
        return Result.OK(pageList);
    }


    @AutoLog(value = "设备信息表-添加")
    @ApiOperation(value = "设备信息表-添加", notes = "设备信息表-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody DeviceHelmet deviceHelmet) {
        deviceInfoService.save(deviceHelmet);
        return Result.OK(deviceHelmet);
    }


    @AutoLog(value = "设备信息表-编辑")
    @ApiOperation(value = "设备信息表-编辑", notes = "设备信息表-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody DeviceHelmet deviceHelmet) {
        deviceInfoService.updateById(deviceHelmet);
        return Result.OK(deviceHelmet);
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "设备信息表-通过id删除")
    @ApiOperation(value = "设备信息表-通过id删除", notes = "设备信息表-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        deviceInfoService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "设备信息表-批量删除")
    @ApiOperation(value = "设备信息表-批量删除", notes = "设备信息表-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.deviceInfoService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "设备信息表-通过id查询")
    @ApiOperation(value = "设备信息表-通过id查询", notes = "设备信息表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        DeviceHelmet deviceHelmet = deviceInfoService.getById(id);
        if (deviceHelmet == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(deviceHelmet);

    }


    /**
     * 定时任务判断设备是否在线
     */
//    @Scheduled(cron = "0 */10 * * * ?")
//    public void scheduled() {
//        log.info("执行定时任务判断设备是否在线");
//        deviceInfoService.deviceBootable();
//    }
    /**
     * 接收设备位置数据
     *
     * @param locationDataList 位置数据列表
     * @return
     */
    @ApiOperation(value = "接收设备位置数据", notes = "接收设备位置数据")
    @PostMapping(value = "/location/receive")
    public Result<?> receiveLocationData(@RequestBody List<LocationDataDTO> locationDataList) {
        try {
            if (locationDataList == null || locationDataList.isEmpty()) {
                return Result.error("位置数据不能为空");
            }

            deviceInfoService.receiveLocationData(locationDataList);
            return Result.OK("位置数据接收成功");
        } catch (Exception e) {
            log.error("接收位置数据失败: {}", e.getMessage(), e);
            return Result.error("接收位置数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有人员位置信息
     *
     * @return
     */
    @ApiOperation(value = "获取所有人员位置信息", notes = "获取所有人员位置信息")
    @GetMapping(value = "/location/all")
    public Result<?> getAllPersonnelLocations() {
        try {
            List<EnrichedLocationDataDTO> locations = deviceInfoService.getAllPersonnelLocations();
            return Result.OK(locations);
        } catch (Exception e) {
            log.error("获取人员位置信息失败: {}", e.getMessage(), e);
            return Result.error("获取人员位置信息失败: " + e.getMessage());
        }
    }
}
