package com.jinghe.breeze.modules.base.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinghe.breeze.modules.base.entity.BaseArea;
import com.jinghe.breeze.modules.base.service.IBaseAreaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.common.util.oConvertUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

/**
 * @Description: 区域
 * @Author: jeecg-boot
 * @Date: 2024-08-13
 * @Version: V1.0
 */
@Api(tags = "区域")
@RestController
@RequestMapping("/base/baseArea")
@Slf4j
public class BaseAreaController extends JeecgController<BaseArea, IBaseAreaService> {
    @Autowired
    private IBaseAreaService baseAreaService;

    @Value("${jeecg.uploadFileTypes:}")
    private String uploadFileTypes;
    @Value("${jeecg.path.upload}")
    private String uploadpath;
    @Value("${jeecg.uploadType}")
    private String uploadType;
    @Value("${jeecg.minio.bucketName}")
    private String bucketName;

    /**
     * 分页列表查询
     *
     * @param baseArea
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "区域-分页列表查询")
    @ApiOperation(value = "区域-分页列表查询", notes = "区域-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(BaseArea baseArea,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<BaseArea> queryWrapper = QueryGenerator.initQueryWrapper(baseArea, req.getParameterMap());
        Page<BaseArea> page = new Page<BaseArea>(pageNo, pageSize);
        IPage<BaseArea> pageList = baseAreaService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param baseArea
     * @return
     */
    @AutoLog(value = "区域-添加")
    @ApiOperation(value = "区域-添加", notes = "区域-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody BaseArea baseArea) {
        baseAreaService.save(baseArea);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param baseArea
     * @return
     */
    @AutoLog(value = "区域-编辑")
    @ApiOperation(value = "区域-编辑", notes = "区域-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody BaseArea baseArea) {
        baseAreaService.updateById(baseArea);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "区域-通过id删除")
    @ApiOperation(value = "区域-通过id删除", notes = "区域-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        baseAreaService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "区域-批量删除")
    @ApiOperation(value = "区域-批量删除", notes = "区域-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.baseAreaService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "区域-通过id查询")
    @ApiOperation(value = "区域-通过id查询", notes = "区域-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        BaseArea baseArea = baseAreaService.getById(id);
        if (baseArea == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(baseArea);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param baseArea
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, BaseArea baseArea) {
        return super.exportXls(request, baseArea, BaseArea.class, "区域");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, BaseArea.class);
    }

    @AutoLog(value = "点位标记-上传信息")
    @ApiOperation(value = "点位标记-上传信息", notes = "点位标记-上传信息")
    @PostMapping(value = "/uploadInfo")
    public Result<?> uploadInfo(@RequestParam(value = "id") String id, @RequestParam(value = "file", required = false) MultipartFile file,
                                @RequestParam(value = "positionX", required = false) String positionX,
                                @RequestParam(value = "positionY", required = false) String positionY) {
        if (StrUtil.isEmpty(id)) {
            return Result.error("安装区域不能为空！");
        }
        BaseArea one = baseAreaService.getById(id);
        if (ObjectUtil.isEmpty(one)) {
            return Result.error("安装区域不存在！");
        }
        if (ObjectUtil.isNotEmpty(file)) {
            if (!ObjectUtils.isEmpty(this.uploadFileTypes)) {
                String originalFileName = file.getOriginalFilename();
                if (originalFileName.indexOf(".") == -1) {
                    return Result.error("未检测到文件类型！");
                }
                String ofileType = originalFileName.split("\\.")[1];
                String[] fileTypes = this.uploadFileTypes.split(",");
                if (!Arrays.asList(fileTypes).contains(ofileType)) {
                    return Result.error("文件类型不在白名单中！");
                }
            }
            String savePath = null;
            try {
                savePath = CommonUtils.upload(file, this.uploadpath, this.uploadType);
            } catch (Exception e) {
                log.info("上传失败！");
                return Result.error("上传失败！");
            }
            if (oConvertUtils.isNotEmpty(savePath)) {
                //将上传路径存储
                one.setFilePath(bucketName + savePath);
            } else {
                return Result.error("上传失败！");
            }
        }
        //存储点位数据
        if (StrUtil.isNotEmpty(positionX) || StrUtil.isNotEmpty(positionY)) {
            one.setPositionX(positionX);
            one.setPositionY(positionY);
        }
        baseAreaService.updateById(one);
        return Result.OK(one);
    }

}
