package com.jinghe.breeze.modules.video.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * @Description: video_folder
 * @Author: jeecg-boot
 * @Date: 2024-06-12
 * @Version: V1.0
 */
@Data
@TableName("video_folder")
@ApiModel(value = "video_folder对象", description = "video_folder")
public class VideoFolder implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 标识
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "标识")
    private String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    /**
     * delFlag
     */
    @Excel(name = "delFlag", width = 15)
    @ApiModelProperty(value = "delFlag")
    private Integer delFlag;
    /**
     * 组织机构编码
     */
    @ApiModelProperty(value = "组织机构编码")
    private String sysOrgCode;
    /**
     * 项目id
     */
    @Excel(name = "项目id", width = 15)
    @ApiModelProperty(value = "项目id")
    private String projectId;
    /**
     * 目录名称
     */
    @Excel(name = "目录名称", width = 15)
    @ApiModelProperty(value = "目录名称")
    private String name;
    /**
     * pid
     */
    @Excel(name = "pid", width = 15)
    @ApiModelProperty(value = "pid")
    private String pid;
    /**
     * 排序默认0
     */
    @Excel(name = "排序默认0", width = 15)
    @ApiModelProperty(value = "排序默认0")
    private Integer sortNo;
    /**
     * 层默认0
     */
    @Excel(name = "层默认0", width = 15)
    @ApiModelProperty(value = "层默认0")
    private Integer layer;
    /**
     * 是否有子级 1为有
     */
    @Excel(name = "是否有子级 1为有", width = 15)
    @ApiModelProperty(value = "是否有子级 1为有")
    private String hasChild;

    @TableField(exist = false)
    @ApiModelProperty(value = "子级")
    private List<VideoFolder> children;

    @TableField(exist = false)
    @ApiModelProperty(value = "视频")
    private Boolean upEnable;

    @TableField(exist = false)
    @ApiModelProperty(value = "是否可下移")
    private Boolean downEnable;
}
