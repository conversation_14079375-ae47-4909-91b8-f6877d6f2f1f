package com.jinghe.breeze.modules.project.entity;

import lombok.Data;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjectPlanExcelData {

    @ExcelIgnore
    private String noid;

    @ExcelProperty("WBS")
    private String code;

    @ExcelProperty("大纲级别") //此列已从excel中删除
    private Integer layer;

    @ExcelProperty("名称")
    private String name;

    @ExcelProperty("WBS_后续任务")
    private String followTaskCode;

    @ExcelProperty("计划开始时间")
    private String startDate;

    @ExcelProperty("计划完成时间")
    private String endDate;
    @ExcelProperty("计划工期")
    private String planDay;

    @ExcelProperty("关键")
    private String isKeyTask;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("任务类型")
    private String taskType;

//    //分页相关，当前页与每页的数据条数
//    @ExcelIgnore
//    private Integer pageNum;
//    @ExcelIgnore
//    private Integer pageSize;
}
