package com.jinghe.breeze.modules.person.service.impl;

import com.jinghe.breeze.modules.person.entity.DictPersonItem;
import com.jinghe.breeze.modules.person.mapper.DictPersonItemMapper;
import com.jinghe.breeze.modules.person.service.IDictPersonItemService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 数据字典表-子表
 * @Author: chenliang
 * @Date:   2022-08-31
 * @Version: V1.0
 */
@Service
public class DictPersonItemServiceImpl extends ServiceImpl<DictPersonItemMapper, DictPersonItem> implements IDictPersonItemService {
	
	@Autowired
	private DictPersonItemMapper dictPersonItemMapper;
	
	@Override
	public List<DictPersonItem> selectByMainId(String mainId) {
		return dictPersonItemMapper.selectByMainId(mainId);
	}
}
