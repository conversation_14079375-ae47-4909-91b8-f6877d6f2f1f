package com.jinghe.breeze.modules.project.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.baomidou.mybatisplus.annotation.FieldStrategy;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: project_pbs
 * @Author: jeecg-boot
 * @Date: 2024-04-17
 * @Version: V1.0
 */
@Data
@TableName("project_pbs")
@ApiModel(value = "project_pbs对象", description = "project_pbs")
public class ProjectPbs implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
    /**
     * 删除标识
     */
    @Excel(name = "删除标识", width = 15)
    @ApiModelProperty(value = "删除标识")
    private java.lang.Integer delFlag;
    /**
     * 组织机构编码
     */
    @ApiModelProperty(value = "组织机构编码")
    private java.lang.String sysOrgCode;
    /**
     * 项目id
     */
    @Excel(name = "项目id", width = 15)
    @ApiModelProperty(value = "项目id")
    private java.lang.String projectId;
    /**
     * PBS编码
     */
    @Excel(name = "PBS编码", width = 15)
    @ApiModelProperty(value = "PBS编码")
    private java.lang.String code;
    /**
     * pbs名称
     */
    @Excel(name = "pbs名称", width = 15)
    @ApiModelProperty(value = "pbs名称")
    private java.lang.String name;
    /**
     * 父级id 第一层级为0
     */
    @Excel(name = "父级id 第一层级为0", width = 15)
    @ApiModelProperty(value = "父级id 第一层级为0")
    private java.lang.String pid;
    /**
     * 同级排序越大越后
     */
    @Excel(name = "同级排序越大越后", width = 15)
    @ApiModelProperty(value = "同级排序越大越后")
    private java.lang.Integer sortNo;
    /**
     * 所在层级从0开始
     */
    @Excel(name = "所在层级从0开始", width = 15)
    @ApiModelProperty(value = "所在层级从0开始")
    private java.lang.Integer layer;
    /**
     * pbs类型（）关联分类字典
     */
    @Excel(name = "pbs类型（）关联分类字典", width = 15)
    @ApiModelProperty(value = "pbs类型（）关联分类字典")
    private java.lang.String type;
    /**
     * 是否有子节点
     */
    @Excel(name = "是否有子节点", width = 15, dicCode = "yn")
    @Dict(dicCode = "yn")
    @ApiModelProperty(value = "是否有子节点")
    private Integer hasChild;
    /**
     * 坐标信息
     */
    @Excel(name = "坐标信息", width = 15)
    @ApiModelProperty(value = "坐标信息")
    private java.lang.String point;

    @Excel(name = "规格型号", width = 15)
    @ApiModelProperty(value = "规格型号")
    private java.lang.String specificationModel;

    @Excel(name = "适用工序", width = 15)
    @ApiModelProperty(value = "适用工序")
    @TableField(value = "applicable_process", insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED) // 允许插入null
    @Dict(dictTable = "sys_category", dicCode = "code", dicText = "name")
    private java.lang.String applicableProcess;

    @Excel(name = "海缆路由", width = 15)
    @ApiModelProperty(value = "海缆路由")
    @Dict(dicCode = "B12")
    private java.lang.String route;

    @Excel(name = "图例上传", width = 15)
    @ApiModelProperty(value = "图例上传")
    private java.lang.String picture;

    @Excel(name = "所属标段", width = 15)
    @Dict(dicCode = "fan_section")
    @TableField(updateStrategy = FieldStrategy.IGNORED, insertStrategy = FieldStrategy.IGNORED) //忽略空值检查
    @ApiModelProperty(value = "所属标段")
    private java.lang.String fanSection;

    @Excel(name = "所属标段", width = 15)
    @Dict(dicCode = "is_display")
    @TableField(updateStrategy = FieldStrategy.IGNORED, insertStrategy = FieldStrategy.IGNORED) //忽略空值检查
    @ApiModelProperty(value = "所属标段")
    private java.lang.Integer isDisplay;

    @Excel(name = "开始段次", width = 15)
    @ApiModelProperty(value = "开始段次")
    private java.lang.String startSection;

    @Excel(name = "结束段次", width = 15)
    @ApiModelProperty(value = "结束段次")
    private java.lang.String endSection;


    @TableField(exist = false)  // 如果不存储在数据库中
    private boolean upEnable;

    @TableField(exist = false)
    private boolean downEnable;

    @TableField(exist = false)
    private List<ProjectPbs> children;

    public List<ProjectPbs> getChildren() {
        return children;
    }

    public void setChildren(List<ProjectPbs> children) {
        this.children = children;
    }

    public void addChild(ProjectPbs child) {
        if (this.children == null) {
            this.children = new ArrayList<>();
        }
        this.children.add(child);
    }

    public boolean isUpEnable() {
        return upEnable;
    }

    public void setUpEnable(boolean upEnable) {
        this.upEnable = upEnable;
    }

    public boolean isDownEnable() {
        return downEnable;
    }

    public void setDownEnable(boolean downEnable) {
        this.downEnable = downEnable;
    }


}
