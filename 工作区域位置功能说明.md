# 工作区域位置功能说明

## 功能概述

系统现在支持自动识别设备所在的具体工作区域，并在 `position` 字段中显示围栏名称。

### 核心特性

1. **工作区域识别**: 自动检测设备是否在工作区围栏内
2. **围栏名称显示**: 如果在围栏内，`position` 字段显示围栏的名称
3. **优先级逻辑**: 施工围栏优先于安全围栏显示
4. **实时更新**: 位置数据接收时立即更新工作区域信息

## 逻辑规则

### 1. 围栏优先级
```
1. 施工围栏 (fence_type = '3') - 最高优先级
2. 安全防护围栏 (fence_type = '1') - 次优先级
3. 围栏外 - position 为 null
```

### 2. 位置判断逻辑
```java
if (设备在施工围栏内) {
    position = 施工围栏名称;
} else if (设备在安全围栏内) {
    position = 安全围栏名称;
} else {
    position = null;  // 不在任何工作区域内
}
```

### 3. 多围栏处理
- 如果有多个同类型围栏，返回第一个匹配的围栏名称
- 围栏检测按照数据库中的顺序进行

## 数据结构

### EnrichedLocationDataDTO 字段
```java
public class EnrichedLocationDataDTO {
    // 围栏检测结果
    private Boolean isInSafetyFence;      // 是否在安全防护围栏内
    private Boolean isInConstructionFence; // 是否在施工围栏内
    
    // 工作区域位置
    private String position;              // 所在工作区域的围栏名称
    
    // 其他字段...
}
```

## API 响应示例

### 1. 设备在施工围栏内
```json
{
  "puname": "HELMET_001",
  "longitude": 116.413000,
  "latitude": 39.90500,
  "isInSafetyFence": false,
  "isInConstructionFence": true,
  "position": "东区施工作业区",
  "isRegistered": true,
  "personId": "PERSON_001"
}
```

### 2. 设备在安全围栏内
```json
{
  "puname": "HELMET_002",
  "longitude": 116.400428,
  "latitude": 39.90423,
  "isInSafetyFence": true,
  "isInConstructionFence": false,
  "position": "办公区安全围栏",
  "isRegistered": true,
  "personId": "PERSON_002"
}
```

### 3. 设备在围栏外
```json
{
  "puname": "HELMET_003",
  "longitude": 116.395000,
  "latitude": 39.90423,
  "isInSafetyFence": false,
  "isInConstructionFence": false,
  "position": null,
  "isRegistered": false,
  "personId": null
}
```

## 实现细节

### 1. 核心方法

#### `getLocationSafetyFenceName()`
```java
// 获取位置所在的安全防护围栏名称
private String getLocationSafetyFenceName(Double longitude, Double latitude)
```
- 遍历所有安全防护围栏
- 返回第一个匹配的围栏名称
- 如果不在任何安全围栏内返回 null

#### `getLocationConstructionFenceName()`
```java
// 获取位置所在的施工围栏名称
private String getLocationConstructionFenceName(Double longitude, Double latitude)
```
- 遍历所有施工围栏
- 返回第一个匹配的围栏名称
- 如果不在任何施工围栏内返回 null

### 2. 数据流程

#### 位置数据接收流程
```
位置数据推送 
→ 安全围栏检测 
→ 施工围栏检测 
→ 获取围栏名称 
→ 设置 position 字段 
→ 存储到 Redis
```

#### 位置数据查询流程
```
查询请求 
→ 从 Redis 获取数据 
→ 兼容性检测 (补充 position 字段) 
→ 补充人员信息 
→ 返回完整数据
```

## 测试数据

### 测试围栏配置
```sql
-- 安全防护围栏
INSERT INTO project_fence VALUES (
    'TEST_SAFETY_FENCE_001', 
    '办公区安全围栏',  -- 围栏名称
    '1',  -- fence_type: 安全防护围栏
    '[[116.397428, 39.90923], [116.407428, 39.90923], [116.407428, 39.89923], [116.397428, 39.89923], [116.397428, 39.90923]]'
);

-- 施工围栏
INSERT INTO project_fence VALUES (
    'TEST_CONSTRUCTION_FENCE_001', 
    '东区施工作业区',  -- 围栏名称
    '3',  -- fence_type: 施工围栏
    '[[116.408000, 39.91000], [116.418000, 39.91000], [116.418000, 39.90000], [116.408000, 39.90000], [116.408000, 39.91000]]'
);
```

### 测试位置坐标
```javascript
// 安全围栏内 - position: "办公区安全围栏"
const safetyFenceLocation = [116.400428, 39.90423];

// 施工围栏内 - position: "东区施工作业区"
const constructionFenceLocation = [116.413000, 39.90500];

// 围栏外 - position: null
const outsideLocation = [116.395000, 39.90423];
```

## 使用场景

### 1. 实时监控
```javascript
// 过滤特定工作区域的设备
const officeAreaDevices = locations.filter(device => 
    device.position === "办公区安全围栏"
);

const constructionAreaDevices = locations.filter(device => 
    device.position === "东区施工作业区"
);
```

### 2. 区域统计
```javascript
// 统计各工作区域的人员数量
const areaStats = locations.reduce((stats, device) => {
    const area = device.position || "围栏外";
    stats[area] = (stats[area] || 0) + 1;
    return stats;
}, {});
```

### 3. 安全管理
```javascript
// 检查是否有人员在危险区域外
const outsideDevices = locations.filter(device => 
    device.position === null && device.isRegistered
);

if (outsideDevices.length > 0) {
    console.warn(`发现 ${outsideDevices.length} 个设备在安全区域外`);
}
```

## 注意事项

1. **围栏名称**: 确保围栏名称具有业务意义，便于识别
2. **优先级**: 施工围栏优先于安全围栏显示
3. **性能**: 围栏名称获取使用缓存，性能良好
4. **兼容性**: 支持旧数据的自动补充检测
5. **实时性**: 位置数据接收时立即更新工作区域信息

## 扩展功能

### 1. 多语言支持
可以根据用户语言设置返回不同语言的围栏名称。

### 2. 区域层级
可以扩展支持多层级的工作区域，如：楼层 > 区域 > 具体位置。

### 3. 历史轨迹
结合 position 字段可以分析人员在不同工作区域的停留时间和移动轨迹。
