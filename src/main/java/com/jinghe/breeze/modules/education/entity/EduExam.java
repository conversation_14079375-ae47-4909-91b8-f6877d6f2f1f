package com.jinghe.breeze.modules.education.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.validation.constraints.*;

/**
 * @Description: 学员考试实例表
 * @Author: jeecg-boot
 * @Date: 2025-06-26
 * @Version: V1.0
 */
@Data
@TableName("edu_exam")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "edu_exam对象", description = "学员考试实例表")
public class EduExam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;

    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;

    /**
     * 删除标识
     */
    @Excel(name = "删除标识", width = 15)
    @ApiModelProperty(value = "删除标识")
    private String delFlag;

    /**
     * 学员ID
     */
    @Excel(name = "学员ID", width = 15, dictTable = "person_info", dicText = "name", dicCode = "id")
    @Dict(dictTable = "person_info", dicText = "name", dicCode = "id")
    @NotNull(message = "学员ID不能为空!")
    @ApiModelProperty(value = "学员ID (关联 person_info.id)")
    private String personId;

    /**
     * 培训ID
     */
    @Excel(name = "培训ID", width = 15, dictTable = "edu_train", dicText = "subject", dicCode = "id")
    @Dict(dictTable = "edu_train", dicText = "subject", dicCode = "id")
    @NotNull(message = "培训ID不能为空!")
    @ApiModelProperty(value = "培训ID (关联 edu_train.id)")
    private String trainId;

    /**
     * 试卷ID
     */
    @Excel(name = "试卷ID", width = 15, dictTable = "edu_exam_paper", dicText = "name", dicCode = "id")
    @Dict(dictTable = "edu_exam_paper", dicText = "name", dicCode = "id")
    @NotNull(message = "试卷ID不能为空!")
    @ApiModelProperty(value = "试卷ID (关联 edu_exam_paper.id)")
    private String paperId;

    /**
     * 考试状态
     */
    @NotNull(message = "考试状态不能为空!")
    @Dict(dicCode = "exam_status")
    @Enumerated(EnumType.STRING)
    @ApiModelProperty(value = "考试状态: PENDING-待开始, IN_PROGRESS-进行中, FINISHED-已完成")
    private String status;

    /**
     * 答题开始时间
     */
    @Excel(name = "答题开始时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "答题开始时间")
    private Date startTime;

    /**
     * 答题结束时间
     */
    @Excel(name = "答题结束时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "答题结束时间")
    private Date endTime;

    /**
     * 最终得分
     */
    @Excel(name = "最终得分", width = 15)
    @ApiModelProperty(value = "最终得分")
    private BigDecimal score;


    @Excel(name = "考试名称")
    @ApiModelProperty(value = "考试名称")
    private String name;


    @TableField(exist = false)
    private EduExamPaper paper;

//    @TableField(exist = false)
//    private EduTrain train;

    @TableField(exist = false)
    private List<EduExamDetail> details;
}
