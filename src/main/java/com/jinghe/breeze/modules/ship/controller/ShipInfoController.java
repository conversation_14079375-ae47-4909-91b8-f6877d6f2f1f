package com.jinghe.breeze.modules.ship.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.jinghe.breeze.modules.ship.service.IShipOutgoingService;
import org.jeecg.common.api.vo.Result;
import com.jinghe.breeze.modules.ship.entity.ShipInfo;
import com.jinghe.breeze.modules.ship.service.IShipInfoService;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: ship_info
 * @Author: jeecg-boot
 * @Date: 2024-04-19
 * @Version: V1.0
 */
@Api(tags = "ship_info")
@RestController
@RequestMapping("/departureWarning/shipInfo")
@Slf4j
public class ShipInfoController extends JeecgController<ShipInfo, IShipInfoService> {
    @Autowired
    private IShipInfoService shipInfoService;

    @Autowired
    private IShipOutgoingService shipOutgoingService;

    /**
     * 分页列表查询
     *
     * @param shipInfo
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "ship_info-分页列表查询")
    @ApiOperation(value = "ship_info-分页列表查询", notes = "ship_info-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(ShipInfo shipInfo,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        IPage<ShipInfo> pageList = shipInfoService.getList(shipInfo, pageNo, pageSize);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param shipInfo
     * @return
     */
    @AutoLog(value = "ship_info-添加")
    @ApiOperation(value = "ship_info-添加", notes = "ship_info-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody ShipInfo shipInfo) {
        shipInfoService.saveInfo(shipInfo);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param shipInfo
     * @return
     */
    @AutoLog(value = "ship_info-编辑")
    @ApiOperation(value = "ship_info-编辑", notes = "ship_info-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody ShipInfo shipInfo) {
        shipInfoService.updateById(shipInfo);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "ship_info-通过id删除")
    @ApiOperation(value = "ship_info-通过id删除", notes = "ship_info-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        shipInfoService.delete(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "ship_info-批量删除")
    @ApiOperation(value = "ship_info-批量删除", notes = "ship_info-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.shipInfoService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "ship_info-通过id查询")
    @ApiOperation(value = "ship_info-通过id查询", notes = "ship_info-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        ShipInfo shipInfo = shipInfoService.getById(id);
        if (shipInfo == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(shipInfo);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param shipInfo
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ShipInfo shipInfo) {
        return super.exportXls(request, shipInfo, ShipInfo.class, "ship_info");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ShipInfo.class);
    }

}
