package com.jinghe.breeze.modules.project.controller;

import java.util.Arrays;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.jinghe.breeze.modules.project.entity.ProjectInfo;
import com.jinghe.breeze.modules.project.service.IProjectInfoService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: project_info
 * @Author: jeecg-boot
 * @Date:   2024-04-11
 * @Version: V1.0
 */
@Api(tags="project_info")
@RestController
@RequestMapping("/project/projectInfo")
@Slf4j
public class ProjectInfoController extends JeecgController<ProjectInfo, IProjectInfoService> {
	@Autowired
	private IProjectInfoService projectInfoService;
	
	/**
	 * 分页列表查询
	 *
	 * @param projectInfo
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "project_info-分页列表查询")
	@ApiOperation(value="project_info-分页列表查询", notes="project_info-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(ProjectInfo projectInfo,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<ProjectInfo> queryWrapper = QueryGenerator.initQueryWrapper(projectInfo, req.getParameterMap());
		Page<ProjectInfo> page = new Page<ProjectInfo>(pageNo, pageSize);
		IPage<ProjectInfo> pageList = projectInfoService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param projectInfo
	 * @return
	 */
	@AutoLog(value = "project_info-添加")
	@ApiOperation(value="project_info-添加", notes="project_info-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody ProjectInfo projectInfo) {
		projectInfoService.save(projectInfo);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param projectInfo
	 * @return
	 */
	@AutoLog(value = "project_info-编辑")
	@ApiOperation(value="project_info-编辑", notes="project_info-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody ProjectInfo projectInfo) {
		projectInfoService.updateById(projectInfo);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "project_info-通过id删除")
	@ApiOperation(value="project_info-通过id删除", notes="project_info-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		projectInfoService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "project_info-批量删除")
	@ApiOperation(value="project_info-批量删除", notes="project_info-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.projectInfoService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "project_info-通过id查询")
	@ApiOperation(value="project_info-通过id查询", notes="project_info-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		ProjectInfo projectInfo = projectInfoService.getById(id);
		if(projectInfo ==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(projectInfo);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param projectInfo
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ProjectInfo projectInfo) {
        return super.exportXls(request, projectInfo, ProjectInfo.class, "project_info");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ProjectInfo.class);
    }

	 /**
	  * 首页接口,
	  * @return
	  */
	@RequestMapping(value= "/getProjectInfo", method = RequestMethod.GET)
	public Result<?> getProjectInfo() {
		Map<String,String> map = projectInfoService.getProjectInfo();
		return Result.OK(map);
	}
}
