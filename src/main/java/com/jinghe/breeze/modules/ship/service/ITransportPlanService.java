package com.jinghe.breeze.modules.ship.service;

import com.jinghe.breeze.modules.ship.entity.TransportPlan;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;

/**
 * @Description: transport_plan
 * @Author: jeecg-boot
 * @Date:   2024-06-06
 * @Version: V1.0
 */
public interface ITransportPlanService extends IService<TransportPlan> {

    Result<?> getList(String transportCode, Integer pageNo, Integer pageSize,String status);

    Result<?> getUnderwayList();

    Result<?> getOrderDetail(String id) throws IOException, NoSuchAlgorithmException;
}
