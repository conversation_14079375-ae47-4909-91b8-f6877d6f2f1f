package com.jinghe.breeze.modules.wind.service.impl;

import com.jinghe.breeze.modules.wind.entity.WindReportDetail;
import com.jinghe.breeze.modules.wind.mapper.WindReportDetailMapper;
import com.jinghe.breeze.modules.wind.service.IWindReportDetailService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: wind_report_detail
 * @Author: jeecg-boot
 * @Date:   2024-05-24
 * @Version: V1.0
 */
@Service
public class WindReportDetailServiceImpl extends ServiceImpl<WindReportDetailMapper, WindReportDetail> implements IWindReportDetailService {

}
