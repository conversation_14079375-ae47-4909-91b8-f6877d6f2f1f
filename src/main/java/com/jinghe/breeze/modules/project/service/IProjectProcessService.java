package com.jinghe.breeze.modules.project.service;

import com.jinghe.breeze.modules.project.entity.ProjectProcess;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;

/**
 * @Description: project_process
 * @Author: jeecg-boot
 * @Date:   2024-04-17
 * @Version: V1.0
 */
public interface IProjectProcessService extends IService<ProjectProcess> {

    Result<?> listAll();
}
