package com.jinghe.breeze.modules.project.mapper;

import com.jinghe.breeze.modules.project.entity.ProjectPbs;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: project_pbs
 * @Author: jeecg-boot
 * @Date:   2024-04-17
 * @Version: V1.0
 */
public interface ProjectPbsMapper extends BaseMapper<ProjectPbs> {

	/**
	 * 编辑节点状态
	 * @param id
	 * @param status
	 */
	void updateTreeNodeStatus(@Param("id") String id,@Param("status") String status);

}
